"""
数据库管理器 - 自律守护者 v7.0 融合版
提供线程安全的、统一的数据库访问接口
管理应用分类、专注时间日志、临时白名单等数据
"""

import os
import sqlite3
import threading
import time
from datetime import datetime
from typing import Dict, List, Any, Optional, Tuple


class DatabaseManager:
    """线程安全的数据库管理器"""
    
    def __init__(self, db_path: str = None):
        """初始化数据库管理器"""
        if db_path is None:
            # 默认数据库路径
            db_path = os.path.join(os.path.dirname(__file__), '..', 'data', 'database.db')
        
        self.db_path = db_path
        self.db_lock = threading.Lock()
        
        # 确保数据目录存在
        os.makedirs(os.path.dirname(self.db_path), exist_ok=True)
        
        # 初始化数据库
        self._initialize_database()
    
    def _initialize_database(self):
        """初始化数据库表结构"""
        with self.db_lock:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            try:
                # 应用分类表
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS app_classification (
                        process_name TEXT PRIMARY KEY,
                        category TEXT NOT NULL,
                        last_updated INTEGER NOT NULL
                    )
                ''')
                
                # 专注时间日志表
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS focus_log (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        process_name TEXT NOT NULL,
                        window_title TEXT,
                        start_time INTEGER NOT NULL,
                        end_time INTEGER,
                        duration_seconds INTEGER
                    )
                ''')
                
                # 临时白名单表
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS temp_whitelist (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        entry_type TEXT NOT NULL,
                        value TEXT NOT NULL UNIQUE,
                        expiry_timestamp INTEGER NOT NULL
                    )
                ''')
                
                # 创建索引以提高查询性能
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_focus_log_process ON focus_log(process_name)')
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_focus_log_start_time ON focus_log(start_time)')
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_temp_whitelist_expiry ON temp_whitelist(expiry_timestamp)')
                
                conn.commit()
                print("数据库初始化完成")
                
            except Exception as e:
                print(f"数据库初始化失败: {str(e)}")
                conn.rollback()
                raise
            finally:
                conn.close()
    
    # ==================== 应用分类相关方法 ====================
    
    def get_app_category(self, process_name: str) -> Optional[str]:
        """获取应用分类"""
        with self.db_lock:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            try:
                cursor.execute(
                    'SELECT category FROM app_classification WHERE process_name = ?',
                    (process_name,)
                )
                result = cursor.fetchone()
                return result[0] if result else None
                
            except Exception as e:
                print(f"获取应用分类失败: {str(e)}")
                return None
            finally:
                conn.close()
    
    def set_app_category(self, process_name: str, category: str) -> bool:
        """设置应用分类"""
        with self.db_lock:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            try:
                current_time = int(time.time())
                cursor.execute('''
                    INSERT OR REPLACE INTO app_classification 
                    (process_name, category, last_updated) 
                    VALUES (?, ?, ?)
                ''', (process_name, category, current_time))
                
                conn.commit()
                return True
                
            except Exception as e:
                print(f"设置应用分类失败: {str(e)}")
                conn.rollback()
                return False
            finally:
                conn.close()
    
    def get_all_app_classifications(self) -> List[Dict[str, Any]]:
        """获取所有应用分类"""
        with self.db_lock:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            try:
                cursor.execute('SELECT process_name, category, last_updated FROM app_classification')
                results = cursor.fetchall()
                
                return [
                    {
                        'process_name': row[0],
                        'category': row[1],
                        'last_updated': row[2]
                    }
                    for row in results
                ]
                
            except Exception as e:
                print(f"获取所有应用分类失败: {str(e)}")
                return []
            finally:
                conn.close()
    
    # ==================== 专注时间日志相关方法 ====================
    
    def add_focus_log(self, process_name: str, window_title: str = None, 
                     start_time: int = None, end_time: int = None, 
                     duration_seconds: int = None) -> bool:
        """添加专注时间日志"""
        if start_time is None:
            start_time = int(time.time())
        
        with self.db_lock:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            try:
                cursor.execute('''
                    INSERT INTO focus_log 
                    (process_name, window_title, start_time, end_time, duration_seconds) 
                    VALUES (?, ?, ?, ?, ?)
                ''', (process_name, window_title, start_time, end_time, duration_seconds))
                
                conn.commit()
                return True
                
            except Exception as e:
                print(f"添加专注时间日志失败: {str(e)}")
                conn.rollback()
                return False
            finally:
                conn.close()
    
    def get_daily_focus_time(self, process_name: str, date: str = None) -> int:
        """获取指定应用在指定日期的专注时间（秒）"""
        if date is None:
            date = datetime.now().strftime('%Y-%m-%d')
        
        # 计算日期的时间戳范围
        start_timestamp = int(datetime.strptime(date, '%Y-%m-%d').timestamp())
        end_timestamp = start_timestamp + 86400  # 24小时
        
        with self.db_lock:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            try:
                cursor.execute('''
                    SELECT SUM(duration_seconds) 
                    FROM focus_log 
                    WHERE process_name = ? 
                    AND start_time >= ? 
                    AND start_time < ?
                    AND duration_seconds IS NOT NULL
                ''', (process_name, start_timestamp, end_timestamp))
                
                result = cursor.fetchone()
                return result[0] if result[0] is not None else 0
                
            except Exception as e:
                print(f"获取每日专注时间失败: {str(e)}")
                return 0
            finally:
                conn.close()
    
    def get_category_daily_focus_time(self, category: str, date: str = None) -> int:
        """获取指定分类在指定日期的总专注时间（秒）"""
        if date is None:
            date = datetime.now().strftime('%Y-%m-%d')
        
        # 计算日期的时间戳范围
        start_timestamp = int(datetime.strptime(date, '%Y-%m-%d').timestamp())
        end_timestamp = start_timestamp + 86400  # 24小时
        
        with self.db_lock:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            try:
                cursor.execute('''
                    SELECT SUM(f.duration_seconds) 
                    FROM focus_log f
                    JOIN app_classification a ON f.process_name = a.process_name
                    WHERE a.category = ? 
                    AND f.start_time >= ? 
                    AND f.start_time < ?
                    AND f.duration_seconds IS NOT NULL
                ''', (category, start_timestamp, end_timestamp))
                
                result = cursor.fetchone()
                return result[0] if result[0] is not None else 0
                
            except Exception as e:
                print(f"获取分类每日专注时间失败: {str(e)}")
                return 0
            finally:
                conn.close()
    
    # ==================== 临时白名单相关方法 ====================
    
    def add_to_temp_whitelist(self, entry_type: str, value: str, expiry_hours: int = 24) -> bool:
        """添加到临时白名单"""
        expiry_timestamp = int(time.time()) + (expiry_hours * 3600)
        
        with self.db_lock:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            try:
                cursor.execute('''
                    INSERT OR REPLACE INTO temp_whitelist 
                    (entry_type, value, expiry_timestamp) 
                    VALUES (?, ?, ?)
                ''', (entry_type, value, expiry_timestamp))
                
                conn.commit()
                return True
                
            except Exception as e:
                print(f"添加到临时白名单失败: {str(e)}")
                conn.rollback()
                return False
            finally:
                conn.close()
    
    def is_in_temp_whitelist(self, entry_type: str, value: str) -> bool:
        """检查是否在临时白名单中"""
        current_time = int(time.time())
        
        with self.db_lock:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            try:
                cursor.execute('''
                    SELECT 1 FROM temp_whitelist 
                    WHERE entry_type = ? AND value = ? AND expiry_timestamp > ?
                ''', (entry_type, value, current_time))
                
                result = cursor.fetchone()
                return result is not None
                
            except Exception as e:
                print(f"检查临时白名单失败: {str(e)}")
                return False
            finally:
                conn.close()
    
    def cleanup_expired_whitelist(self) -> int:
        """清理过期的白名单条目，返回清理的条目数"""
        current_time = int(time.time())
        
        with self.db_lock:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            try:
                cursor.execute(
                    'DELETE FROM temp_whitelist WHERE expiry_timestamp <= ?',
                    (current_time,)
                )
                
                deleted_count = cursor.rowcount
                conn.commit()
                return deleted_count
                
            except Exception as e:
                print(f"清理过期白名单失败: {str(e)}")
                conn.rollback()
                return 0
            finally:
                conn.close()
    
    # ==================== 维护方法 ====================
    
    def get_database_stats(self) -> Dict[str, int]:
        """获取数据库统计信息"""
        with self.db_lock:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            try:
                stats = {}
                
                # 应用分类数量
                cursor.execute('SELECT COUNT(*) FROM app_classification')
                stats['app_classifications'] = cursor.fetchone()[0]
                
                # 专注时间日志数量
                cursor.execute('SELECT COUNT(*) FROM focus_log')
                stats['focus_logs'] = cursor.fetchone()[0]
                
                # 临时白名单数量
                cursor.execute('SELECT COUNT(*) FROM temp_whitelist')
                stats['temp_whitelist_entries'] = cursor.fetchone()[0]
                
                return stats
                
            except Exception as e:
                print(f"获取数据库统计信息失败: {str(e)}")
                return {}
            finally:
                conn.close()
