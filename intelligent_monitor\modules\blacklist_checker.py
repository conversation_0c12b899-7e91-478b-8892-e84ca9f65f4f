# intelligent_monitor/modules/blacklist_checker.py
# 中文说明：
# 本模块用于黑名单检测，提供标题和URL的关键字匹配功能。
# 供主监控循环和决策逻辑调用。

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from config import DEBUG_BLACKLIST

def check_title_against_blacklist(title, blacklist):
    """检查标题是否命中黑名单关键字。返回True表示需拦截。"""
    if not title: return False
    title_lower = title.lower()
    for keyword in blacklist:
        if keyword.lower() in title_lower:
            if DEBUG_BLACKLIST:
                print(f"  [黑名单] 匹配到关键字 '{keyword}' in title '{title}'")
            return True
    return False

def check_url_against_blacklist(url, blacklist):
    """检查URL是否命中黑名单关键字。返回True表示需拦截。"""
    if not url:
        return False
    url_lower = url.lower()
    for keyword in blacklist:
        if keyword.lower() in url_lower:
            if DEBUG_BLACKLIST:
                print(f"  [黑名单] 匹配到URL关键字 '{keyword}' in url '{url}'")
            return True
    return False
