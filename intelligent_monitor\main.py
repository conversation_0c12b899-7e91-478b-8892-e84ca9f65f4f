# intelligent_monitor/main.py

# 功能说明：
# 本文件为智能浏览器监控系统的主程序入口。
# V8.3: 拦截逻辑增强。当检测到黑名单浏览器时，会通过PID反查其真实的进程名，
# 确保taskkill命令的准确性，并保留了验证和重试机制。
# 适用于 Windows 环境，需配合 config.py、core、modules 目录下各模块使用。

import sys
import io
import time
import traceback
import os
import subprocess
import ctypes  # 导入ctypes

# 设置UTF-8输出
sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8', line_buffering=True)

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 导入所有解耦的模块和配置
import config
from core import uia_base
from modules.managers import StrategyLifecycleManager, AIAuditManager
from modules.browser_info_fetcher import get_browser_info_with_lifecycle
from modules.tab_closer import close_browser_tab
from modules.blacklist_checker import check_title_against_blacklist, check_url_against_blacklist


def get_real_process_name_from_pid(pid):
    """
    通过PID反向查询真实的进程映像名称。
    """
    try:
        # 使用tasklist精确查找PID对应的映像名称
        # /NH (无表头), /FO CSV (CSV格式)
        command = f'tasklist /FI "PID eq {pid}" /NH /FO CSV'
        # 创建StartupInfo对象以隐藏可能弹出的命令行窗口
        si = subprocess.STARTUPINFO()
        si.dwFlags |= subprocess.STARTF_USESHOWWINDOW

        result = subprocess.run(
            command,
            capture_output=True,
            text=True,
            encoding='gbk',  # tasklist在中文系统下默认使用GBK编码
            startupinfo=si,
            check=False
        )

        if result.returncode == 0 and result.stdout.strip():
            # CSV输出示例: "firefox.exe","12345","Console","1","1,234,567 K"
            parts = result.stdout.strip().split(',')
            # 真实进程名是第一部分，去除两边的引号
            image_name = parts[0].strip('"')
            return image_name
    except Exception as e:
        print(f"    [错误] 通过PID查询真实进程名时发生异常: {e}")
        return None
    return None


def monitor_focused_window():
    """主监控逻辑"""
    if not uia_base.UIA_LOADED:
        print("\nUIA组件加载失败，程序无法运行。")
        input("按Enter键退出...")
        return

    # 1. 初始化所有管理器
    lifecycle_manager = StrategyLifecycleManager()
    ai_manager = AIAuditManager()

    # 打印启动信息
    print("\n" + "=" * 80)
    print("开始实时监控焦点窗口 (V8.3 - 精准拦截模式)")
    print("核心思想: 白名单监控 + PID反查精准拦截 + 验证重试")
    print(f"允许的浏览器: {', '.join(sorted(list(config.ALLOWED_BROWSERS)))}")
    print(f"将自动关闭的浏览器: {', '.join(sorted(list(config.BLACKLISTED_BROWSERS)))}")
    # ... (其他打印信息保持不变)
    print("按 Ctrl+C 退出程序。")
    print("=" * 80 + "\n")

    last_hwnd, last_process_name, last_reported_state = None, None, (None, None, None)

    try:
        while True:
            time.sleep(1.5)
            hwnd = uia_base.user32.GetForegroundWindow()

            if not hwnd:
                if last_hwnd is not None:
                    last_hwnd, last_process_name, last_reported_state = None, None, (None, None, None)
                    print(f"--- STATE CHANGE at {time.strftime('%Y-%m-%d %H:%M:%S')} ---")
                    print("[系统] 暂无活动窗口")
                    print("-" * 50 + "\n")
                continue

            normalized_last_process = last_process_name.lower().replace('.exe', '') if last_process_name else ""
            if hwnd == last_hwnd and normalized_last_process not in config.ALLOWED_BROWSERS:
                continue

            process_name, window_title = uia_base.get_window_process_name_and_title(hwnd)
            normalized_process_name = process_name.lower().replace('.exe', '') if process_name else ''

            last_hwnd, last_process_name = hwnd, process_name

            # --- 核心处理分支 ---

            # 1. 黑名单浏览器处理：精准拦截
            if normalized_process_name in config.BLACKLISTED_BROWSERS:
                print(f"--- FOCUSED BANNED BROWSER at {time.strftime('%Y-%m-%d %H:%M:%S')} ---")
                print(f"[拦截] 检测到不允许的浏览器: {process_name or '未知进程'}。")

                pid = uia_base.get_pid_from_hwnd(hwnd)
                if not pid:
                    print("  [错误] 无法获取进程PID，无法执行关闭操作。")
                    continue

                print(f"  > 正在获取PID {pid} 的真实进程名...")
                real_process_name = get_real_process_name_from_pid(pid)

                if real_process_name:
                    print(f"  > 成功获取真实进程名: '{real_process_name}'")
                    print(f"  > 第一次尝试关闭 (通过真实名称)...")
                    os.system(f"taskkill /F /IM \"{real_process_name}\" > nul 2>&1")
                else:
                    print("  > 未能获取真实进程名，将直接尝试通过PID关闭。")

                # 验证和重试（无论第一次是否成功，都用PID做最终检查和终结）
                time.sleep(0.5)

                check_command = f'tasklist /FI "PID eq {pid}"'
                result = subprocess.run(check_command, capture_output=True, text=True, shell=True)

                if str(pid) in result.stdout:
                    print(f"  > [验证-失败] 进程 {pid} 仍然存在。")
                    print(f"  > 第二次尝试关闭 (通过PID {pid})...")
                    os.system(f"taskkill /F /PID {pid} > nul 2>&1")
                    print("  > 已发送最终关闭命令。")
                else:
                    print("  > [验证-成功] 进程已关闭。")

                last_reported_state = (None, None, None)
                print("-" * 50 + "\n")
                time.sleep(1)
                continue

            # 2. 白名单浏览器处理：详细监控
            elif normalized_process_name in config.ALLOWED_BROWSERS:
                # ... (这部分逻辑保持不变) ...
                current_url, doc_title, url_strategy, window_element, status_info = get_browser_info_with_lifecycle(
                    hwnd, process_name, lifecycle_manager
                )
                if doc_title: window_title = doc_title

                should_close = False
                close_reason = ""
                if window_element:
                    if check_title_against_blacklist(window_title, config.BLACKLIST_KEYWORDS):
                        should_close = True
                        close_reason = f"标题命中黑名单: {window_title}"
                    elif check_url_against_blacklist(current_url, config.URL_BLACKLIST_KEYWORDS):
                        should_close = True
                        close_reason = f"URL命中黑名单: {current_url}"
                    elif config.AI_AUDIT_CONFIG['enabled'] and current_url:
                        decision = ai_manager.check_and_audit(current_url, window_title)
                        if decision == 'deny':
                            should_close = True
                            close_reason = f"AI审核拒绝: {window_title}"
                        elif decision == 'pending':
                            url_strategy += " (AI审核中...)"

                if should_close:
                    print(f"[{time.strftime('%H:%M:%S')}] 检测到违规内容，原因: {close_reason}")
                    if current_url: print(f"  关联URL: {current_url}")
                    print("  正在尝试关闭标签页...")
                    if close_browser_tab(window_element, process_name, lifecycle_manager):
                        print("  成功关闭标签页！")
                        last_reported_state = (None, None, None)
                        print("-" * 50 + "\n")
                        time.sleep(0.5)
                    else:
                        print("  关闭标签页失败。")
                    continue

                current_state = (hwnd, window_title, current_url)
                if current_state != last_reported_state:
                    print(f"--- STATE CHANGE at {time.strftime('%Y-%m-%d %H:%M:%S')} ---")
                    print(f"[浏览器] {process_name}: {window_title}")
                    if current_url: print(f"  URL     : {current_url}")
                    if url_strategy: print(f"  获取方式: {url_strategy}")
                    if status_info and config.DEBUG_LIFECYCLE:
                        phase = status_info.get('phase', 'N/A')
                        print(
                            f"  [生命周期] 阶段: {phase}, 策略: {status_info.get('strategy', 'N/A')}, 质量: {status_info.get('quality', 'N/A')}")
                    print("-" * 50 + "\n")
                    last_reported_state = current_state

            # 3. 其他非浏览器应用处理
            else:
                current_state = (hwnd, window_title, None)
                if current_state != last_reported_state:
                    print(f"--- STATE CHANGE at {time.strftime('%Y-%m-%d %H:%M:%S')} ---")
                    print(f"[应  用] {process_name or '未知'}: {window_title}")
                    print("-" * 50 + "\n")
                    last_reported_state = current_state

    except KeyboardInterrupt:
        print("\n监控程序已停止。")
    except Exception as e:
        print(f"\n监控过程中发生未预料的错误: {e}")
        traceback.print_exc()


if __name__ == "__main__":
    monitor_focused_window()