"""
加密模块 - 保护配置文件不被直接查看和修改
使用AES-GCM加密算法提供加密和完整性校验
"""

import json
import os
from cryptography.hazmat.primitives.ciphers.aead import AESGCM
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC

# 硬编码密钥 - 在实际部署时应该使用更复杂的密钥
ENCRYPTION_KEY = b'a3f7b1e9c2d8a4b6c3d8e9f1a2b3c4d5e6f7a8b9c1d2e3f4a5b6c7d8e9f1a2b3'

# 启动器专用密钥 - 与主程序密钥完全不同的字节串
LAUNCHER_KEY = b'f9e8d7c6b5a4938271605f4e3d2c1b0a9f8e7d6c5b4a39281706f5e4d3c2b1a0'

class ConfigEncryption:
    def __init__(self, key: bytes = None):
        # 使用指定密钥或默认的ENCRYPTION_KEY
        if key is None:
            key = ENCRYPTION_KEY
        self.key = key[:32]  # AES-256需要32字节密钥
        self.aesgcm = AESGCM(self.key)
    
    def encrypt(self, data: dict) -> bytes:
        """
        加密Python字典数据
        
        Args:
            data: 要加密的字典数据
            
        Returns:
            bytes: 加密后的字节数据
        """
        try:
            # 将字典转换为JSON字符串，再转换为字节
            json_str = json.dumps(data, ensure_ascii=False, indent=2)
            plaintext = json_str.encode('utf-8')
            
            # 生成随机nonce
            nonce = os.urandom(12)  # AES-GCM推荐12字节nonce
            
            # 加密数据
            ciphertext = self.aesgcm.encrypt(nonce, plaintext, None)
            
            # 返回nonce + ciphertext
            return nonce + ciphertext
            
        except Exception as e:
            raise Exception(f"加密失败: {str(e)}")
    
    def decrypt(self, encrypted_data: bytes) -> dict:
        """
        解密字节数据为Python字典
        
        Args:
            encrypted_data: 加密的字节数据
            
        Returns:
            dict: 解密后的字典数据
        """
        try:
            if len(encrypted_data) < 12:
                raise ValueError("加密数据长度不足")
            
            # 分离nonce和密文
            nonce = encrypted_data[:12]
            ciphertext = encrypted_data[12:]
            
            # 解密数据
            plaintext = self.aesgcm.decrypt(nonce, ciphertext, None)
            
            # 将字节转换为字符串，再转换为字典
            json_str = plaintext.decode('utf-8')
            data = json.loads(json_str)
            
            return data
            
        except Exception as e:
            raise Exception(f"解密失败: {str(e)}")

# 全局加密实例
_encryption = ConfigEncryption()
_launcher_encryption = ConfigEncryption(LAUNCHER_KEY)

def encrypt(data: dict, key: bytes = None) -> bytes:
    """
    加密字典数据

    Args:
        data: 要加密的字典数据
        key: 可选的加密密钥，如果不提供则使用默认的ENCRYPTION_KEY

    Returns:
        bytes: 加密后的字节数据
    """
    if key is not None:
        # 使用指定密钥创建临时加密实例
        temp_encryption = ConfigEncryption(key)
        return temp_encryption.encrypt(data)
    else:
        # 使用默认加密实例
        return _encryption.encrypt(data)

def decrypt(encrypted_data: bytes, key: bytes = None) -> dict:
    """
    解密字节数据

    Args:
        encrypted_data: 加密的字节数据
        key: 可选的解密密钥，如果不提供则使用默认的ENCRYPTION_KEY

    Returns:
        dict: 解密后的字典数据
    """
    if key is not None:
        # 使用指定密钥创建临时加密实例
        temp_encryption = ConfigEncryption(key)
        return temp_encryption.decrypt(encrypted_data)
    else:
        # 使用默认加密实例
        return _encryption.decrypt(encrypted_data)
