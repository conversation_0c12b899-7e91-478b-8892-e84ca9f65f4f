# intelligent_monitor/config.py
# 中文说明：
# 本模块为智能浏览器监控系统的全局配置中心，包含调试开关、黑名单、AI审核参数、
# 生命周期管理参数及相关枚举类。所有核心模块均需引用本配置。
# 各枚举类用于标识策略阶段、质量和失效类型。

import os

# --- 调试开关 ---
DEBUG_BLACKLIST = True
DEBUG_CACHE = True
DEBUG_LIFECYCLE = True

# --- 黑名单配置 ---
BLACKLIST_KEYWORDS = [
    "广告", "游戏", "抽奖", "lottery", "gambling",
]

# --- URL黑名单配置 ---
URL_BLACKLIST_KEYWORDS = [
    "example-malicious-domain.com",
    "bad-ad-network.net/tracker",
    "gambling-site.com",
    "adult-content.xxx"
]

# --- AI 审核配置 ---
AI_AUDIT_CONFIG = {
    "enabled": True,  # AI审核总开关
    "cache_file_path": "ai_audit_cache.json",
    "cache_ttl_seconds": 3600 * 24,  # 审核结果缓存24小时
    "api_key": os.getenv("AI_API_KEY", ""),  # 优先从环境变量读取
    "api_endpoint": "https://openrouter.ai/api/v1/chat/completions",  # 可替换为任何兼容的API
    "model": "google/gemma-3-27b-it:free",
    # 提示词模板，{title} 和 {url} 将被替换
    "prompt_template": """
作为一名网络内容审核员，请根据以下规则判断用户是否应该访问给定的网页。
规则：
1. 禁止任何形式的赌博、色情、暴力内容。
2. 禁止已知的钓鱼网站、恶意软件分发站点。
3. 禁止游戏，动漫，小说，八卦等娱乐页面。
4. 允许正常的新闻、技术、社交、购物网站。
5. 如果内容可疑但无法确定，倾向于允许。

网页标题: "{title}"
网页URL: "{url}"

你的回答必须且只能是 "allow" 或 "deny"。
    """
}

# --- 生命周期配置 ---
EXPLORATION_BUDGET = 5  # 探索阶段最大尝试次数
HARD_FAILURE_THRESHOLD = 3  # 硬失效容忍度阈值
FULL_SEARCH_INTERVAL = 60  # 全量搜索间隔（秒）
CACHE_FILE_PATH = "browser_strategy_cache.json"  # 缓存文件路径

# --- 浏览器进程列表 (新版：白名单/黑名单) ---
# 仅允许的浏览器进程名 (不含 .exe)
ALLOWED_BROWSERS = {
    "chrome", "msedge"
}

# 需要强制关闭的浏览器进程名 (不含 .exe)
BLACKLISTED_BROWSERS = {
    "firefox", "opera", "brave", "vivaldi", "iexplore",
    "360se", "liebao", "360chrome", "wechatappex"
}

# --- 策略状态枚举 ---
class StrategyPhase:
    """策略生命周期阶段枚举：用于标识浏览器策略的当前阶段。"""
    EXPLORATION = "exploration"  # 阶段一：探索与学习
    STABLE = "stable"  # 阶段二：稳定运行
    FAILED = "failed"  # 过渡态：失效与再学习
    ABANDONED = "abandoned"  # 阶段三：放弃与降级 (使用次优解)

class StrategyQuality:
    """策略质量枚举：用于标识当前策略的有效性。"""
    HIGH = "high"  # 高质量：能获取URL和标题
    LOW = "low"  # 低质量：只能获取标题
    NONE = "none"  # 无质量：什么都获取不到

class FailureType:
    """失效类型枚举：用于区分软失效和硬失效。"""
    SOFT = "soft"  # 软失效：有标题但无URL
    HARD = "hard"  # 硬失效：连标题都没有