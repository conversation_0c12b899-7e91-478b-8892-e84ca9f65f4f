<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>自律守护者 - 控制面板</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <link href="{{ url_for('static', filename='css/style.css') }}" rel="stylesheet">
</head>
<body>
    <div class="container-fluid">
        <!-- 顶部导航栏 -->
        <nav class="navbar navbar-expand-lg navbar-dark bg-primary mb-4">
            <div class="container-fluid">
                <span class="navbar-brand mb-0 h1">
                    <i class="bi bi-shield-check"></i>
                    自律守护者
                </span>
                <span class="navbar-text">
                    智能进程管理控制面板
                </span>
            </div>
        </nav>

        <!-- 主要内容区域 -->
        <div class="row">
            <!-- 左侧面板 -->
            <div class="col-lg-8">
                <!-- 系统状态卡片 -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="bi bi-info-circle"></i>
                            系统状态
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="d-flex align-items-center">
                            <div class="status-indicator me-3" id="statusIndicator">
                                <div class="spinner-border spinner-border-sm text-secondary" role="status">
                                    <span class="visually-hidden">检测中...</span>
                                </div>
                            </div>
                            <div>
                                <h6 class="mb-1" id="statusText">检测中...</h6>
                                <small class="text-muted" id="statusDetail">正在检测系统状态...</small>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 黑名单规则卡片 -->
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="card-title mb-0">
                            <i class="bi bi-list-ul"></i>
                            黑名单规则
                        </h5>
                        <div>
                            <span class="badge bg-secondary" id="rulesCount">0</span>
                            <button class="btn btn-sm btn-outline-primary ms-2" onclick="loadRules()">
                                <i class="bi bi-arrow-clockwise"></i>
                                刷新
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>进程名称</th>
                                        <th>类型</th>
                                        <th>时间范围</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody id="rulesTableBody">
                                    <tr>
                                        <td colspan="4" class="text-center text-muted">
                                            <div class="spinner-border spinner-border-sm me-2" role="status"></div>
                                            加载中...
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 右侧控制面板 -->
            <div class="col-lg-4">
                <!-- 添加规则卡片 -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="bi bi-plus-circle"></i>
                            添加新规则
                        </h5>
                    </div>
                    <div class="card-body">
                        <form id="addRuleForm">
                            <div class="mb-3">
                                <label for="processName" class="form-label">进程名称</label>
                                <div class="input-group">
                                    <input type="text" class="form-control" id="processName" placeholder="例如: notepad.exe" required>
                                    <button class="btn btn-outline-secondary" type="button" onclick="showProcessList()">
                                        <i class="bi bi-search"></i>
                                        浏览
                                    </button>
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <label class="form-label">限制类型</label>
                                <div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="radio" name="ruleType" id="allDay" value="all_day" checked>
                                        <label class="form-check-label" for="allDay">
                                            全天禁止
                                        </label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="radio" name="ruleType" id="timeRange" value="time_range">
                                        <label class="form-check-label" for="timeRange">
                                            时间段限制
                                        </label>
                                    </div>
                                </div>
                            </div>
                            
                            <div id="timeRangeInputs" class="mb-3" style="display: none;">
                                <div class="row">
                                    <div class="col-6">
                                        <label for="startTime" class="form-label">开始时间</label>
                                        <input type="time" class="form-control" id="startTime" value="09:00">
                                    </div>
                                    <div class="col-6">
                                        <label for="endTime" class="form-label">结束时间</label>
                                        <input type="time" class="form-control" id="endTime" value="18:00">
                                    </div>
                                </div>
                            </div>
                            
                            <button type="submit" class="btn btn-success w-100">
                                <i class="bi bi-plus"></i>
                                添加规则
                            </button>
                        </form>
                    </div>
                </div>

                <!-- 系统控制卡片 -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="bi bi-gear"></i>
                            系统控制
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="d-grid gap-2">
                            <button class="btn btn-success" id="activateBtn" onclick="activateProtection()" disabled>
                                <i class="bi bi-shield-check"></i>
                                开启守护
                            </button>
                            <button class="btn btn-warning" id="deactivateBtn" onclick="deactivateProtection()" disabled>
                                <i class="bi bi-pause-circle"></i>
                                暂停守护
                            </button>
                        </div>
                        <div class="mt-3">
                            <small class="text-muted" id="controlHint">请根据当前状态选择操作</small>
                        </div>
                    </div>
                </div>

                <!-- 窗口期配置卡片 -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="bi bi-clock"></i>
                            窗口期配置
                        </h5>
                    </div>
                    <div class="card-body">
                        <p class="text-muted small">设置允许暂停守护的时间窗口</p>
                        <button class="btn btn-outline-primary w-100" onclick="showWindowConfig()">
                            <i class="bi bi-gear"></i>
                            配置窗口期
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 进程选择模态框 -->
    <div class="modal fade" id="processModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">选择进程</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <input type="text" class="form-control" id="processSearch" placeholder="搜索进程...">
                    </div>
                    <div class="table-responsive" style="max-height: 400px;">
                        <table class="table table-hover table-sm">
                            <thead class="table-light">
                                <tr>
                                    <th>PID</th>
                                    <th>进程名称</th>
                                    <th>路径</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody id="processTableBody">
                                <tr>
                                    <td colspan="4" class="text-center">
                                        <div class="spinner-border spinner-border-sm me-2" role="status"></div>
                                        加载中...
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 窗口期配置模态框 -->
    <div class="modal fade" id="windowConfigModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">配置窗口期</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="windowConfigForm">
                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="enableWindow" checked>
                                <label class="form-check-label" for="enableWindow">
                                    启用窗口期
                                </label>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="dayOfWeek" class="form-label">星期几</label>
                            <select class="form-select" id="dayOfWeek">
                                <option value="0">周一</option>
                                <option value="1">周二</option>
                                <option value="2">周三</option>
                                <option value="3">周四</option>
                                <option value="4">周五</option>
                                <option value="5">周六</option>
                                <option value="6">周日</option>
                            </select>
                        </div>
                        <div class="row">
                            <div class="col-6">
                                <label for="windowStartTime" class="form-label">开始时间</label>
                                <input type="time" class="form-control" id="windowStartTime" value="02:00">
                            </div>
                            <div class="col-6">
                                <label for="windowEndTime" class="form-label">结束时间</label>
                                <input type="time" class="form-control" id="windowEndTime" value="04:00">
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="saveWindowConfig()">保存</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="{{ url_for('static', filename='js/app.js') }}"></script>
</body>
</html>
