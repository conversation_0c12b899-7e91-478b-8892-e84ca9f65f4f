"""
可靠时间源 - 提供不受本地系统时间修改影响的准确时间
使用NTP服务器同步时间，防止通过修改系统时间绕过限制
"""

import time
import socket
import struct
import threading
from datetime import datetime, timezone
from typing import Optional

class ReliableClock:
    def __init__(self):
        self.initial_network_time: Optional[float] = None
        self.initial_monotonic_time: Optional[float] = None
        self.lock = threading.Lock()
        self._is_synced = False
        self.ntp_servers = [
            'pool.ntp.org',
            'time.windows.com',
            'time.nist.gov',
            'time.cloudflare.com'
        ]
        
        # 在后台启动持续的时间同步
        self.sync_thread = threading.Thread(target=self._persistent_sync, daemon=True)
        self.sync_thread.start()
    
    def _get_ntp_time(self, server: str, timeout: float = 5.0) -> Optional[float]:
        """
        从NTP服务器获取时间
        
        Args:
            server: NTP服务器地址
            timeout: 超时时间（秒）
            
        Returns:
            float: Unix时间戳，失败返回None
        """
        try:
            # NTP请求包格式
            ntp_packet = b'\x1b' + 47 * b'\0'
            
            # 创建UDP socket
            sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            sock.settimeout(timeout)
            
            try:
                # 发送NTP请求
                sock.sendto(ntp_packet, (server, 123))
                
                # 接收响应
                response, _ = sock.recvfrom(1024)
                
                if len(response) >= 48:
                    # 解析NTP响应中的时间戳（字节40-43为传输时间戳）
                    timestamp = struct.unpack('!I', response[40:44])[0]
                    
                    # NTP时间戳从1900年开始，Unix时间戳从1970年开始
                    # 1900年到1970年的秒数：2208988800
                    unix_timestamp = timestamp - 2208988800
                    
                    return float(unix_timestamp)
                    
            finally:
                sock.close()
                
        except Exception as e:
            print(f"从NTP服务器 {server} 获取时间失败: {str(e)}")
            return None
    
    def is_synced(self) -> bool:
        """检查时间是否已同步"""
        with self.lock:
            return self._is_synced

    def _persistent_sync(self):
        """
        在后台持续尝试同步时间，直到首次成功。
        """
        while not self.is_synced():
            if self._sync_once():
                print("初始时间同步成功。")
                break  # 成功后退出循环
            else:
                print("所有NTP服务器同步失败，将在30秒后重试...")
                time.sleep(30)

    def _sync_once(self) -> bool:
        """
        尝试从NTP服务器列表同步一次时间。
        成功返回 True，失败返回 False。
        """
        with self.lock:
            for server in self.ntp_servers:
                network_time = self._get_ntp_time(server)
                if network_time is not None:
                    self.initial_network_time = network_time
                    self.initial_monotonic_time = time.monotonic()
                    self._is_synced = True
                    print(f"时间同步成功，使用服务器: {server}")
                    return True
        return False

    def now(self) -> float:
        """
        获取当前可靠时间。如果时间未同步，会引发RuntimeError。
        
        Returns:
            float: 当前Unix时间戳
        
        Raises:
            RuntimeError: 如果时钟尚未同步
        """
        if not self.is_synced():
            raise RuntimeError("可靠时钟尚未同步")

        with self.lock:
            # 计算当前时间：初始网络时间 + 单调时间差
            current_monotonic = time.monotonic()
            if self.initial_monotonic_time is None or self.initial_network_time is None:
                 raise RuntimeError("可靠时钟尚未同步")
            elapsed = current_monotonic - self.initial_monotonic_time
            return self.initial_network_time + elapsed
    
    def calibrate(self):
        """
        重新校准时间。这是一个阻塞操作。
        """
        print("开始时间校准...")
        if not self._sync_once():
            print("校准失败。")
        else:
            print("校准成功。")
    
    def get_datetime(self) -> datetime:
        """
        获取当前可靠时间的datetime对象
        
        Returns:
            datetime: 当前时间的datetime对象（UTC时区）
        """
        timestamp = self.now()
        return datetime.fromtimestamp(timestamp, tz=timezone.utc)
    
    def is_time_in_range(self, start_time: str, end_time: str, 
                        day_of_week: Optional[int] = None) -> bool:
        """
        检查当前时间是否在指定范围内
        
        Args:
            start_time: 开始时间，格式为 "HH:MM"
            end_time: 结束时间，格式为 "HH:MM"
            day_of_week: 星期几（0=周一，6=周日），None表示不限制
            
        Returns:
            bool: 在范围内返回True，否则返回False
        """
        try:
            current_dt = self.get_datetime().astimezone()  # 转换为本地时区
            
            # 检查星期几
            if day_of_week is not None:
                if current_dt.weekday() != day_of_week:
                    return False
            
            # 解析时间
            start_hour, start_minute = map(int, start_time.split(':'))
            end_hour, end_minute = map(int, end_time.split(':'))
            
            current_hour = current_dt.hour
            current_minute = current_dt.minute
            
            # 转换为分钟数进行比较
            current_minutes = current_hour * 60 + current_minute
            start_minutes = start_hour * 60 + start_minute
            end_minutes = end_hour * 60 + end_minute
            
            # 处理跨天的情况
            if end_minutes < start_minutes:
                # 跨天：例如 23:00 到 02:00
                return current_minutes >= start_minutes or current_minutes <= end_minutes
            else:
                # 同一天：例如 02:00 到 04:00
                return start_minutes <= current_minutes <= end_minutes
                
        except Exception as e:
            print(f"时间范围检查失败: {str(e)}")
            return False

# 全局时钟实例
_reliable_clock = None
_clock_lock = threading.Lock()

def get_reliable_clock() -> ReliableClock:
    """获取全局可靠时钟实例"""
    global _reliable_clock
    with _clock_lock:
        if _reliable_clock is None:
            _reliable_clock = ReliableClock()
        return _reliable_clock

def now() -> float:
    """获取当前可靠时间戳"""
    return get_reliable_clock().now()

def get_datetime() -> datetime:
    """获取当前可靠时间的datetime对象"""
    return get_reliable_clock().get_datetime()

def is_time_in_range(start_time: str, end_time: str, 
                    day_of_week: Optional[int] = None) -> bool:
    """检查当前时间是否在指定范围内"""
    return get_reliable_clock().is_time_in_range(start_time, end_time, day_of_week)
