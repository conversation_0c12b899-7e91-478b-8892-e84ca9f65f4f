"""
Guardian Service - Windows服务版本的规则执行者
系统的"大脑"和"攻击之矛"，负责执行所有黑名单规则，处理与前端的通信
重构为Windows服务模型，移除对watchdog的依赖，实现自恢复机制
"""

import os
import sys
import time
import json
import socket
import threading
from datetime import datetime
from typing import Dict, List, Any, Optional

# Windows服务相关导入
try:
    import win32serviceutil
    import win32service
    import win32event
    import servicemanager
    WIN32_AVAILABLE = True
except ImportError:
    WIN32_AVAILABLE = False
    print("警告: pywin32库未安装，Windows服务功能不可用")

# 添加common模块到路径
sys.path.insert(0, os.path.join(os.path.dirname(os.path.dirname(__file__)), 'common'))

import common

class Guardian:
    """Guardian核心逻辑类，现在接受停止事件以支持服务模型"""
    
    def __init__(self, stop_event=None):
        self.running = True
        self.stop_event = stop_event  # 来自服务的停止事件
        self.clock = common.get_reliable_clock()
        self.config = {}
        self.server_socket = None
        self.server_thread = None
        self.tcp_port = 54321

        # 初始化
        self._initialize()
    
    def _initialize(self):
        """初始化Guardian"""
        try:
            # 加载配置
            self.config = common.load_config()

            # 启动TCP服务器
            self._start_tcp_server()

            print("Guardian初始化完成")

        except Exception as e:
            print(f"Guardian初始化失败: {str(e)}")
            sys.exit(1)
    
    def _start_tcp_server(self):
        """启动TCP服务器用于与前端通信"""
        try:
            self.server_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.server_socket.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
            self.server_socket.bind(('127.0.0.1', self.tcp_port))
            self.server_socket.listen(5)
            
            self.server_thread = threading.Thread(target=self._handle_tcp_connections, daemon=True)
            self.server_thread.start()
            
            print(f"TCP服务器启动成功，监听端口: {self.tcp_port}")
            
        except Exception as e:
            print(f"启动TCP服务器失败: {str(e)}")
    
    def _handle_tcp_connections(self):
        """处理TCP连接"""
        while self.running:
            try:
                if self.server_socket:
                    client_socket, addr = self.server_socket.accept()
                    client_thread = threading.Thread(
                        target=self._handle_client,
                        args=(client_socket,),
                        daemon=True
                    )
                    client_thread.start()
            except Exception as e:
                if self.running:
                    print(f"处理TCP连接失败: {str(e)}")
                break
    
    def _handle_client(self, client_socket):
        """处理客户端请求"""
        try:
            # 接收数据
            data = client_socket.recv(4096).decode('utf-8')
            if not data:
                return
            
            # 解析JSON请求
            request = json.loads(data)
            command = request.get('command', '')
            
            # 处理命令
            response = self._process_command(command, request.get('data', {}))
            
            # 发送响应
            response_json = json.dumps(response, ensure_ascii=False)
            client_socket.send(response_json.encode('utf-8'))
            
        except Exception as e:
            error_response = {'status': 'error', 'message': str(e)}
            try:
                client_socket.send(json.dumps(error_response).encode('utf-8'))
            except:
                pass
        finally:
            try:
                client_socket.close()
            except:
                pass
    
    def _process_command(self, command: str, data: Dict[str, Any]) -> Dict[str, Any]:
        """处理命令"""
        try:
            if command == 'get_rules':
                return self._get_rules()
            elif command == 'add_rule':
                return self._add_rule(data.get('rule', ''))
            elif command == 'remove_rule':
                return self._remove_rule(data.get('rule', ''))
            elif command == 'get_process_list':
                return self._get_process_list()
            elif command == 'set_uninstall_window':
                return self._set_uninstall_window(data)
            elif command == 'get_status':
                return self._get_status()
            elif command == 'deactivate_protection':
                return self._deactivate_protection()
            else:
                return {'status': 'error', 'message': f'未知命令: {command}'}
                
        except Exception as e:
            return {'status': 'error', 'message': str(e)}
    
    def _get_rules(self) -> Dict[str, Any]:
        """获取黑名单规则"""
        try:
            rules = common.get_blacklist_rules()
            return {'status': 'success', 'rules': rules}
        except Exception as e:
            return {'status': 'error', 'message': str(e)}
    
    def _add_rule(self, rule: str) -> Dict[str, Any]:
        """添加黑名单规则"""
        try:
            if not rule.strip():
                return {'status': 'error', 'message': '规则不能为空'}

            # 添加新规则
            common.add_blacklist_rule(rule)

            return {'status': 'success', 'message': '规则添加成功'}

        except ValueError as e:
            return {'status': 'error', 'message': str(e)}
        except Exception as e:
            return {'status': 'error', 'message': str(e)}
    
    def _remove_rule(self, rule: str) -> Dict[str, Any]:
        """删除黑名单规则"""
        try:
            # 删除规则
            common.remove_blacklist_rule(rule)

            return {'status': 'success', 'message': '规则删除成功'}

        except ValueError as e:
            return {'status': 'error', 'message': str(e)}
        except Exception as e:
            return {'status': 'error', 'message': str(e)}
    
    def _get_process_list(self) -> Dict[str, Any]:
        """获取进程列表"""
        try:
            processes = common.get_running_processes()
            return {'status': 'success', 'processes': processes}
        except Exception as e:
            return {'status': 'error', 'message': str(e)}
    
    def _set_uninstall_window(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """设置卸载窗口期"""
        try:
            self.config['uninstall_window'] = data
            common.save_config(self.config)
            return {'status': 'success', 'message': '窗口期设置成功'}
        except Exception as e:
            return {'status': 'error', 'message': str(e)}
    
    def _get_status(self) -> Dict[str, Any]:
        """获取系统状态"""
        try:
            is_time_synced = self.clock.is_synced()
            status_payload = {
                'status': 'success',
                'is_time_synced': is_time_synced,
                'current_time': None,
                'current_datetime': None,
                'uninstall_window': self.config.get('uninstall_window', {}),
                'in_uninstall_window': False,
                'blacklist_integrity': common.verify_blacklist_integrity()
            }

            if is_time_synced:
                current_time = self.clock.now()
                current_dt = self.clock.get_datetime()
                status_payload['current_time'] = current_time
                status_payload['current_datetime'] = current_dt.isoformat()

                # 检查是否在窗口期
                window_config = status_payload['uninstall_window']
                if window_config.get('enabled', False):
                    status_payload['in_uninstall_window'] = self.clock.is_time_in_range(
                        window_config.get('start_time', '02:00'),
                        window_config.get('end_time', '04:00'),
                        window_config.get('day_of_week')
                    )
            
            return status_payload
        except Exception as e:
            return {'status': 'error', 'message': str(e)}

    def _deactivate_protection(self) -> Dict[str, Any]:
        """停用保护（仅在窗口期允许）- 服务版本"""
        try:
            # 必须先确保时间同步
            if not self.clock.is_synced():
                return {'status': 'error', 'message': '时间未同步，无法验证窗口期'}

            # 检查是否在窗口期
            window_config = self.config.get('uninstall_window', {})

            if not window_config.get('enabled', False):
                return {'status': 'error', 'message': '未设置窗口期'}

            in_window = self.clock.is_time_in_range(
                window_config.get('start_time', '02:00'),
                window_config.get('end_time', '04:00'),
                window_config.get('day_of_week')
            )

            if not in_window:
                return {'status': 'error', 'message': '当前不在允许的窗口期内'}

            # 停用保护 - 服务版本通过停止和删除服务来实现
            service_name = self.config.get('service_name', 'GuardianProtectionService')

            # 在单独线程中执行服务停止和禁用，避免阻塞响应
            import threading
            def stop_and_disable_service():
                try:
                    import subprocess
                    import time

                    # 等待一小段时间让响应发送完成
                    time.sleep(2)

                    # 停止服务
                    stop_cmd = ['sc', 'stop', service_name]
                    subprocess.run(stop_cmd, capture_output=True, text=True)

                    # 等待服务完全停止
                    time.sleep(3)

                    # 禁用服务自启动 (不再是删除)
                    # 注意：这里我们不能直接调用common模块，因为它可能在打包后路径不同。
                    # 直接使用sc命令更可靠。
                    disable_cmd = ['sc', 'config', service_name, 'start=', 'disabled']
                    subprocess.run(disable_cmd, capture_output=True, text=True)

                except Exception as e:
                    print(f"停止和禁用服务失败: {str(e)}")

            # 启动停止线程
            stop_thread = threading.Thread(target=stop_and_disable_service, daemon=True)
            stop_thread.start()

            # 标记Guardian停止
            self.running = False

            return {'status': 'success', 'message': '保护已暂停，守护服务已停止并禁用。'}

        except Exception as e:
            return {'status': 'error', 'message': str(e)}

    def _parse_blacklist_rule(self, rule: str) -> Dict[str, Any]:
        """
        解析黑名单规则

        Args:
            rule: 规则字符串，格式为 "process_name.exe" 或 "process_name.exe,start_time,end_time"

        Returns:
            dict: 解析后的规则信息
        """
        parts = rule.split(',')

        if len(parts) == 1:
            # 全天禁止
            return {
                'process_name': parts[0].strip(),
                'all_day': True,
                'start_time': None,
                'end_time': None
            }
        elif len(parts) == 3:
            # 分时段禁止
            return {
                'process_name': parts[0].strip(),
                'all_day': False,
                'start_time': parts[1].strip(),
                'end_time': parts[2].strip()
            }
        else:
            raise ValueError(f"无效的规则格式: {rule}")

    def _should_kill_process(self, rule_info: Dict[str, Any]) -> bool:
        """
        判断是否应该终止进程。此方法仅在时间同步时调用。

        Args:
            rule_info: 规则信息

        Returns:
            bool: 应该终止返回True，否则返回False
        """
        if rule_info['all_day']:
            return True

        # 检查时间范围
        return self.clock.is_time_in_range(
            rule_info['start_time'],
            rule_info['end_time']
        )

    def _execute_blacklist_rules(self):
        """执行黑名单规则"""
        try:
            is_time_synced = self.clock.is_synced()
            if not is_time_synced:
                print("警告: 时间未同步，将执行严格查杀模式 (忽略时间段)。")

            # 从配置中读取黑名单规则
            rules = common.get_blacklist_rules()

            if not rules:
                return

            # 执行每个规则
            for rule in rules:
                try:
                    if not rule.strip():
                        continue

                    rule_info = self._parse_blacklist_rule(rule)
                    should_kill = False

                    if is_time_synced:
                        # 时间同步，按规则判断
                        should_kill = self._should_kill_process(rule_info)
                    else:
                        # 时间未同步，默认查杀所有黑名单进程
                        should_kill = True

                    if should_kill:
                        killed_count = common.kill_process_by_name(rule_info['process_name'])
                        if killed_count > 0:
                            print(f"已终止 {killed_count} 个 {rule_info['process_name']} 进程")

                except Exception as e:
                    print(f"执行规则失败 '{rule}': {str(e)}")

        except Exception as e:
            print(f"执行黑名单规则失败: {str(e)}")

    def run(self):
        """主运行循环 - 服务版本，支持停止事件"""
        print("Guardian开始运行...")

        # 每小时校准一次时间
        last_calibration_time = time.monotonic()

        while self.running:
            try:
                # 检查服务停止事件
                if self.stop_event and self.stop_event.is_set():
                    print("收到服务停止信号，正在退出...")
                    break

                # 步骤A: 检查时间同步状态（移除watchdog检查）
                if self.clock.is_synced():
                    # 时间同步后，执行核心逻辑

                    # 1. 执行黑名单规则
                    self._execute_blacklist_rules()

                    # 2. 定期校准时间（每小时一次）
                    current_time = time.monotonic()
                    if current_time - last_calibration_time > 3600:  # 1小时
                        self.clock.calibrate()
                        last_calibration_time = current_time
                else:
                    # 时间未同步，仅打印等待信息
                    print("等待网络时间同步...")

                # 步骤B: 休眠
                time.sleep(5)  # 减慢循环速度以降低CPU占用

            except KeyboardInterrupt:
                print("收到中断信号，正在退出...")
                break
            except Exception as e:
                print(f"主循环异常: {str(e)}")
                time.sleep(5)  # 异常时等待更长时间

        self._shutdown()

    def _shutdown(self):
        """关闭Guardian - 服务版本，移除计划任务和watchdog相关代码"""
        print("Guardian正在关闭...")
        self.running = False

        try:
            # 关闭TCP服务器
            if self.server_socket:
                self.server_socket.close()
                self.server_socket = None

            # 服务版本不再需要处理计划任务和watchdog进程
            # 服务的停止和删除将由外部的sc命令处理

        except Exception as e:
            print(f"关闭过程中出现异常: {str(e)}")


class GuardianService(win32serviceutil.ServiceFramework):
    """Guardian Windows服务包装类"""

    _svc_name_ = "GuardianProtectionService"
    _svc_display_name_ = "Windows Security Enhancement Service"
    _svc_description_ = "Provides enhanced security monitoring and protection for the system"

    def __init__(self, args):
        win32serviceutil.ServiceFramework.__init__(self, args)
        self.hWaitStop = win32event.CreateEvent(None, 0, 0, None)
        self.guardian = None

    def SvcStop(self):
        """服务停止处理"""
        self.ReportServiceStatus(win32service.SERVICE_STOP_PENDING)
        win32event.SetEvent(self.hWaitStop)

        # 停止Guardian
        if self.guardian:
            self.guardian.running = False

    def SvcDoRun(self):
        """服务主运行方法"""
        try:
            # 记录服务启动
            servicemanager.LogMsg(
                servicemanager.EVENTLOG_INFORMATION_TYPE,
                servicemanager.PYS_SERVICE_STARTED,
                (self._svc_name_, '')
            )

            # 创建停止事件对象
            import threading
            stop_event = threading.Event()

            # 创建Guardian实例
            self.guardian = Guardian(stop_event=stop_event)

            # 在单独线程中运行Guardian
            guardian_thread = threading.Thread(target=self.guardian.run, daemon=True)
            guardian_thread.start()

            # 等待停止信号
            while True:
                # 等待停止事件，超时时间为1秒
                rc = win32event.WaitForSingleObject(self.hWaitStop, 1000)
                if rc == win32event.WAIT_OBJECT_0:
                    # 收到停止信号
                    stop_event.set()
                    break

        except Exception as e:
            # 记录错误
            servicemanager.LogErrorMsg(f"Guardian服务运行异常: {str(e)}")

        finally:
            # 记录服务停止
            servicemanager.LogMsg(
                servicemanager.EVENTLOG_INFORMATION_TYPE,
                servicemanager.PYS_SERVICE_STOPPED,
                (self._svc_name_, '')
            )


def main():
    """主函数 - 处理命令行参数"""
    if not WIN32_AVAILABLE:
        print("错误: 需要安装pywin32库才能运行Windows服务")
        sys.exit(1)

    if len(sys.argv) == 1:
        # 没有参数，尝试启动服务
        try:
            servicemanager.Initialize()
            servicemanager.PrepareToHostSingle(GuardianService)
            servicemanager.StartServiceCtrlDispatcher()
        except Exception as e:
            print(f"启动服务失败: {str(e)}")
            # 如果作为服务启动失败，尝试作为普通程序运行（用于调试）
            print("尝试作为普通程序运行...")
            guardian = Guardian()
            guardian.run()
    else:
        # 有参数，使用win32serviceutil处理
        win32serviceutil.HandleCommandLine(GuardianService)


if __name__ == "__main__":
    main()
