/* 自律守护者 - 现代化样式 */

:root {
    --primary-color: #2c3e50;
    --primary-light: #34495e;
    --primary-dark: #1a252f;
    
    --accent-color: #3498db;
    --accent-light: #5dade2;
    --accent-dark: #2980b9;
    
    --success-color: #27ae60;
    --warning-color: #f39c12;
    --danger-color: #e74c3c;
    --info-color: #3498db;
    
    --light-color: #ecf0f1;
    --light-gray: #bdc3c7;
    --gray-color: #95a5a6;
    --dark-gray: #7f8c8d;
    
    --bg-primary: #ffffff;
    --bg-secondary: #f8f9fa;
    --bg-dark: #2c3e50;
    
    --text-primary: #2c3e50;
    --text-secondary: #7f8c8d;
    --text-light: #ffffff;
    
    --border-radius: 8px;
    --box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    --transition: all 0.3s ease;
}

/* 全局样式 */
body {
    font-family: 'Microsoft YaHei UI', 'Microsoft YaHei', 'Segoe UI', sans-serif;
    background-color: var(--bg-secondary);
    color: var(--text-primary);
    line-height: 1.6;
}

/* 导航栏样式 */
.navbar {
    box-shadow: var(--box-shadow);
    border-radius: 0 0 var(--border-radius) var(--border-radius);
}

.navbar-brand {
    font-weight: 600;
    font-size: 1.25rem;
}

/* 卡片样式 */
.card {
    border: none;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    transition: var(--transition);
    background-color: var(--bg-primary);
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.card-header {
    background-color: var(--bg-primary);
    border-bottom: 1px solid var(--light-gray);
    border-radius: var(--border-radius) var(--border-radius) 0 0;
    padding: 1rem 1.25rem;
}

.card-title {
    color: var(--text-primary);
    font-weight: 600;
}

/* 状态指示器 */
.status-indicator {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: var(--transition);
}

.status-indicator.status-success {
    background-color: var(--success-color);
    color: var(--text-light);
}

.status-indicator.status-warning {
    background-color: var(--warning-color);
    color: var(--text-light);
}

.status-indicator.status-danger {
    background-color: var(--danger-color);
    color: var(--text-light);
}

.status-indicator.status-info {
    background-color: var(--info-color);
    color: var(--text-light);
}

/* 按钮样式 */
.btn {
    border-radius: var(--border-radius);
    font-weight: 500;
    transition: var(--transition);
    border: none;
    padding: 0.5rem 1rem;
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.btn-primary {
    background-color: var(--accent-color);
    border-color: var(--accent-color);
}

.btn-primary:hover {
    background-color: var(--accent-dark);
    border-color: var(--accent-dark);
}

.btn-success {
    background-color: var(--success-color);
    border-color: var(--success-color);
}

.btn-success:hover {
    background-color: #229954;
    border-color: #229954;
}

.btn-warning {
    background-color: var(--warning-color);
    border-color: var(--warning-color);
}

.btn-warning:hover {
    background-color: #e67e22;
    border-color: #e67e22;
}

.btn-danger {
    background-color: var(--danger-color);
    border-color: var(--danger-color);
}

.btn-danger:hover {
    background-color: #c0392b;
    border-color: #c0392b;
}

/* 表格样式 */
.table {
    margin-bottom: 0;
}

.table th {
    border-top: none;
    font-weight: 600;
    color: var(--text-primary);
    background-color: var(--bg-secondary);
}

.table td {
    vertical-align: middle;
    border-color: var(--light-gray);
}

.table-hover tbody tr:hover {
    background-color: var(--light-color);
}

/* 表单样式 */
.form-control {
    border-radius: var(--border-radius);
    border: 1px solid var(--light-gray);
    transition: var(--transition);
}

.form-control:focus {
    border-color: var(--accent-color);
    box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25);
}

.form-select {
    border-radius: var(--border-radius);
    border: 1px solid var(--light-gray);
}

.form-check-input:checked {
    background-color: var(--accent-color);
    border-color: var(--accent-color);
}

/* 模态框样式 */
.modal-content {
    border-radius: var(--border-radius);
    border: none;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.modal-header {
    border-bottom: 1px solid var(--light-gray);
    background-color: var(--bg-secondary);
    border-radius: var(--border-radius) var(--border-radius) 0 0;
}

.modal-footer {
    border-top: 1px solid var(--light-gray);
    background-color: var(--bg-secondary);
    border-radius: 0 0 var(--border-radius) var(--border-radius);
}

/* 徽章样式 */
.badge {
    font-size: 0.75rem;
    padding: 0.35em 0.65em;
    border-radius: var(--border-radius);
}

/* 加载动画 */
.spinner-border-sm {
    width: 1rem;
    height: 1rem;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .container-fluid {
        padding: 0.5rem;
    }
    
    .card {
        margin-bottom: 1rem;
    }
    
    .btn {
        font-size: 0.875rem;
    }
    
    .table-responsive {
        font-size: 0.875rem;
    }
}

/* 自定义滚动条 */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: var(--bg-secondary);
}

::-webkit-scrollbar-thumb {
    background: var(--gray-color);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--dark-gray);
}

/* 动画效果 */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in {
    animation: fadeIn 0.3s ease-out;
}

/* 状态文本颜色 */
.text-success {
    color: var(--success-color) !important;
}

.text-warning {
    color: var(--warning-color) !important;
}

.text-danger {
    color: var(--danger-color) !important;
}

.text-info {
    color: var(--info-color) !important;
}

/* 工具提示样式 */
.tooltip {
    font-size: 0.875rem;
}

.tooltip-inner {
    background-color: var(--primary-dark);
    border-radius: var(--border-radius);
}

/* 输入组样式 */
.input-group .btn {
    border-left: none;
}

.input-group .form-control:focus + .btn {
    border-color: var(--accent-color);
}

/* 特殊效果 */
.glow {
    box-shadow: 0 0 10px rgba(52, 152, 219, 0.5);
}

.pulse {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(52, 152, 219, 0.7);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(52, 152, 219, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(52, 152, 219, 0);
    }
}
