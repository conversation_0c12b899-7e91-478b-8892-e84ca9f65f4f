# intelligent_monitor/modules/tab_closer.py
# 中文说明：
# 本模块用于浏览器标签页关闭，包含多种关闭策略和调度逻辑。
# [V2.3 修改] 修复了侦测器，并极大增强了邻近匹配策略，使其能够通过位置和排除法找到真正的关闭按钮。

import time
import ctypes
import math
import sys
import os

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import config
from core import uia_base as uia

# --- 条件常量 ---
ANY_BUTTON_CONDITION = uia.PropertyCondition(uia.AutomationElement.ControlTypeProperty, uia.ControlType.Button)
TAB_ITEM_CONDITION = uia.PropertyCondition(uia.AutomationElement.ControlTypeProperty, uia.ControlType.TabItem)


# --- 辅助函数 ---
def _get_element_debug_info(element, indent=""):
    if not element: return f"{indent}(元素不存在)"
    try:
        name = f"Name: '{element.Current.Name}'"
    except:
        name = "Name: (无法获取)"
    try:
        ctype = f"ControlType: {element.Current.ControlType.ProgrammaticName}"
    except:
        ctype = "ControlType: (无法获取)"
    try:
        rect = element.Current.BoundingRectangle; rect_str = f"Rect: (L:{int(rect.Left)}, T:{int(rect.Top)}, W:{int(rect.Width)}, H:{int(rect.Height)})"
    except:
        rect_str = "Rect: (无法获取或无效)"
    return f"{indent}[{ctype}, {name}, {rect_str}]"


def _invoke_click(element):
    if not element: return False
    try:
        element.GetCurrentPattern(uia.InvokePattern.Pattern).Invoke(); return True
    except:
        return False


# --- 核心调试函数 (已修复) ---
def _debug_dump_tab_surroundings(active_tab, window_element):
    """
    [核心调试功能] 打印活动标签页周围的完整UIA结构，用于分析和定位问题。
    """
    if not config.DEBUG_BLACKLIST: return
    print("\n" + "=" * 25 + " [UIA结构现场侦测报告] " + "=" * 25)
    print(f"目标活动标签页: {_get_element_debug_info(active_tab, '')}")

    try:
        parent = uia.TreeWalker.ControlViewWalker.GetParent(active_tab)
        print(f"\n[父级元素]:")
        print(_get_element_debug_info(parent, "  "))

        if parent:
            print("\n[父级下的所有子元素 (即 真实兄弟节点列表)]:")
            # [修复] PropertyCondition 构造函数需要属性和值两个参数。
            # 这里我们使用一个始终为真的条件来获取所有控件元素。
            condition = uia.PropertyCondition(uia.AutomationElement.IsControlElementProperty, True)
            children = parent.FindAll(uia.TreeScope.Children, condition)

            if children.Count > 0:
                for i in range(children.Count):
                    child = children.get_Item(i)
                    is_target = " <-- ★目标标签页★" if uia.AutomationElement.Equals(child, active_tab) else ""
                    print(f"  - 兄弟 {i + 1}: {_get_element_debug_info(child, '')}{is_target}")
            else:
                print("  (父级没有其他子元素)")
    except Exception as e:
        print(f"\n[侦测异常] 获取父级/兄弟节点时出错: {e}")

    print("=" * 75 + "\n")


# --- 关闭策略 ---
def _strategy_proximity_match(active_tab, **kwargs):
    """
    策略1 (健壮型-V2): 邻近匹配与排除法。
    1. 找到窗口中所有可见的按钮。
    2. 获取窗口本身的矩形，以识别并排除位于右上角的窗口控制按钮。
    3. 在剩余的候选按钮中，通过计算屏幕坐标距离，找到离活动标签页最近的那个。
    """
    if config.DEBUG_BLACKLIST: print("    [探索-邻近匹配V2] 开始执行基于坐标和排除法的的邻近匹配策略...")
    try:
        window_element = kwargs.get('window_element')

        # 获取目标标签页和整个窗口的几何信息
        target_rect = active_tab.Current.BoundingRectangle
        target_center = (target_rect.Left + target_rect.Width / 2, target_rect.Top + target_rect.Height / 2)
        window_rect = window_element.Current.BoundingRectangle

        # 找到所有可见的按钮
        onscreen_cond = uia.PropertyCondition(uia.AutomationElement.IsOffscreenProperty, False)
        search_condition = uia.AndCondition(ANY_BUTTON_CONDITION, onscreen_cond)

        candidate_buttons = window_element.FindAll(uia.TreeScope.Descendants, search_condition)
        if candidate_buttons.Count == 0:
            if config.DEBUG_BLACKLIST: print("    [探索-邻近匹配V2] 失败: 在窗口中未找到任何可见的按钮。")
            return None

        if config.DEBUG_BLACKLIST: print(
            f"    [探索-邻近匹配V2] 找到了 {candidate_buttons.Count} 个可见按钮。开始筛选...")

        closest_button = None
        min_distance = float('inf')

        # 定义一个区域来识别窗口右上角的控制按钮
        # 通常这些按钮在窗口最顶部的40-50像素内，和最右侧的150像素内
        title_bar_height_approx = 50
        control_button_width_approx = 150

        for i in range(candidate_buttons.Count):
            button = candidate_buttons.get_Item(i)
            btn_rect = button.Current.BoundingRectangle

            # --- 筛选逻辑 ---
            # 排除无效矩形的按钮
            if btn_rect.Width == 0 or btn_rect.Height == 0:
                if config.DEBUG_BLACKLIST: print(
                    f"      - 候选: {_get_element_debug_info(button)} -> [排除] 无效的几何尺寸。")
                continue

            # 排除位于窗口右上角区域的按钮
            is_in_title_bar = btn_rect.Top < (window_rect.Top + title_bar_height_approx)
            is_at_far_right = btn_rect.Right > (window_rect.Right - control_button_width_approx)
            if is_in_title_bar and is_at_far_right:
                if config.DEBUG_BLACKLIST: print(
                    f"      - 候选: {_get_element_debug_info(button)} -> [排除] 疑似窗口控制按钮。")
                continue

            btn_center = (btn_rect.Left + btn_rect.Width / 2, btn_rect.Top + btn_rect.Height / 2)
            distance = math.sqrt((target_center[0] - btn_center[0]) ** 2 + (target_center[1] - btn_center[1]) ** 2)

            if config.DEBUG_BLACKLIST: print(
                f"      - 候选: {_get_element_debug_info(button)} -> [通过] 距离: {distance:.2f}")

            if distance < min_distance:
                min_distance = distance
                closest_button = button

        if closest_button:
            if config.DEBUG_BLACKLIST: print(
                f"    [探索-邻近匹配V2] 成功！找到最近且合理的关闭按钮: {_get_element_debug_info(closest_button)}")
            return {'button': closest_button}
        else:
            if config.DEBUG_BLACKLIST: print(f"    [探索-邻近匹配V2] 失败: 没有按钮通过筛选。")

    except Exception as e:
        if config.DEBUG_BLACKLIST: print(f"    [探索-邻近匹配V2] 执行异常: {e}")
    return None


def _strategy_ctrl_w(**kwargs):
    """最终备用策略：模拟Ctrl+W快捷键。"""
    try:
        window_element = kwargs.get('window_element')
        hwnd = window_element.Current.NativeWindowHandle
        uia.user32.SetForegroundWindow(hwnd)
        time.sleep(0.1)
        # (代码省略，与之前版本相同)
        inputs = (uia.INPUT * 4)(
            uia.INPUT(type=uia.INPUT_KEYBOARD, ki=uia.KEYBDINPUT(wVk=uia.VK_CONTROL, dwFlags=0)),
            uia.INPUT(type=uia.INPUT_KEYBOARD, ki=uia.KEYBDINPUT(wVk=uia.VK_W, dwFlags=0)),
            uia.INPUT(type=uia.INPUT_KEYBOARD, ki=uia.KEYBDINPUT(wVk=uia.VK_W, dwFlags=uia.KEYEVENTF_KEYUP)),
            uia.INPUT(type=uia.INPUT_KEYBOARD, ki=uia.KEYBDINPUT(wVk=uia.VK_CONTROL, dwFlags=uia.KEYEVENTF_KEYUP))
        )
        uia.user32.SendInput(4, ctypes.byref(inputs), ctypes.sizeof(uia.INPUT))
        return {'method': 'ctrl_w'}
    except Exception:
        return None


# --- 策略库 (更新为新策略) ---
ALL_CLOSE_STRATEGIES = {
    'proximity_match': _strategy_proximity_match,
    # 'internal_button': _strategy_internal_button, # 暂时只用最强的和最后的保底
    'ctrl_w': _strategy_ctrl_w,
}


# --- 核心执行函数 ---
def close_browser_tab(window_element, process_name, manager):
    if not uia.UIA_LOADED or not window_element: return False

    try:
        active_tab = None
        all_tabs = list(window_element.FindAll(uia.TreeScope.Descendants, TAB_ITEM_CONDITION))
        for tab_item in all_tabs:
            try:
                if tab_item.GetCurrentPattern(uia.SelectionItemPattern.Pattern).Current.IsSelected:
                    active_tab = tab_item;
                    break
            except Exception:
                continue
        if not active_tab and len(all_tabs) == 1:
            active_tab = all_tabs[0]
        if not active_tab:
            if config.DEBUG_BLACKLIST: print("  [关闭失败] 关键失败: 无法定位到活动标签页。")
            return False
        if config.DEBUG_BLACKLIST: print(f"  [关闭-定位] 成功定位到活动标签: '{active_tab.Current.Name}'")

        _debug_dump_tab_surroundings(active_tab, window_element)

    except Exception as e:
        if config.DEBUG_BLACKLIST: print(f"  [关闭失败] 查找激活标签页时异常: {e}")
        return False

    cached_strategy_info = manager.get_close_strategy(process_name)
    if cached_strategy_info and cached_strategy_info.get('name') != 'ctrl_w':
        # (代码省略，与之前版本相同)
        strategy_name = cached_strategy_info.get('name')
        strategy_func = ALL_CLOSE_STRATEGIES.get(strategy_name)
        if config.DEBUG_BLACKLIST: print(f"  [关闭-缓存] 发现可靠缓存策略 '{strategy_name}'，正在尝试...")
        if strategy_func:
            try:
                result = strategy_func(active_tab=active_tab, window_element=window_element, **cached_strategy_info)
                if result and result.get('button') and _invoke_click(result.get('button')):
                    if config.DEBUG_BLACKLIST: print(f"  [关闭-缓存] 策略 '{strategy_name}' 执行成功。")
                    return True
                else:
                    manager.clear_close_strategy(process_name)
            except Exception:
                manager.clear_close_strategy(process_name)

    strategies_to_try = ['proximity_match', 'ctrl_w']

    if config.DEBUG_BLACKLIST: print("  [关闭-探索] 开始探索新的关闭策略...")
    for strategy_name in strategies_to_try:
        strategy_func = ALL_CLOSE_STRATEGIES[strategy_name]
        try:
            if config.DEBUG_BLACKLIST: print(f"    [探索] 尝试策略: {strategy_name}")
            result = strategy_func(active_tab=active_tab, window_element=window_element)

            if result:
                button = result.get('button')
                if button and _invoke_click(button):
                    if config.DEBUG_BLACKLIST: print(f"  [关闭成功] 可靠的接口策略 '{strategy_name}' 成功关闭标签页。")
                    cache_payload = {'name': strategy_name}
                    manager.set_close_strategy(process_name, cache_payload)
                    return True
                elif result.get('method') == 'ctrl_w':
                    if config.DEBUG_BLACKLIST: print(f"  [关闭成功] 已发送 'ctrl_w' 快捷键信号 (此策略不被缓存)。")
                    return True
        except Exception as e:
            if config.DEBUG_BLACKLIST: print(f"    [探索] 策略 '{strategy_name}' 执行异常: {e}")

    if config.DEBUG_BLACKLIST: print("  [关闭失败] 所有关闭策略均已尝试，全部失败。")
    return False

# 另外，关于你的 AI 线程错误，是 gui.py 调用 set_result_callback 的时机问题。
# 在 gui.py 中找到 AIAuditManager 的初始化，然后立即设置回调即可。
#
# 在 gui.py 中找到 self.ai_manager = AIAuditManager()
# 紧接着在下面加一行：
# self.ai_manager.set_result_callback(self.on_ai_result_received)
# 这样可以修复那个 AttributeError。