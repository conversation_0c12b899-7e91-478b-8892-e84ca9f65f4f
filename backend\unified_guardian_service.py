"""
统一守护服务 - 自律守护者 v7.0 融合版
集成项目一的全局进程扫描和项目二的焦点内容监控功能
作为Windows服务运行，管理所有后台工作线程
"""

import os
import sys
import time
import json
import socket
import threading
from datetime import datetime
from typing import Dict, List, Any, Optional

# Windows服务相关导入
try:
    import win32serviceutil
    import win32service
    import win32event
    import servicemanager
    WIN32_AVAILABLE = True
except ImportError:
    WIN32_AVAILABLE = False
    print("警告: pywin32库未安装，Windows服务功能不可用")

# 添加common模块到路径
sys.path.insert(0, os.path.join(os.path.dirname(os.path.dirname(__file__)), 'common'))
sys.path.insert(0, os.path.join(os.path.dirname(os.path.dirname(__file__)), 'intelligent_monitor'))

import common
from common.database_manager import DatabaseManager
from common.ai_manager import AIManager
from common.notification_manager import NotificationManager
from intelligent_monitor import config as monitor_config
from intelligent_monitor.core import uia_base
from intelligent_monitor.modules.managers import StrategyLifecycleManager, AIAuditManager
from intelligent_monitor.modules.browser_info_fetcher import get_browser_info_with_lifecycle
from intelligent_monitor.modules.tab_closer import close_browser_tab
from intelligent_monitor.modules.blacklist_checker import check_title_against_blacklist, check_url_against_blacklist


class GuardianCore:
    """统一守护核心逻辑类，管理所有功能模块"""
    
    def __init__(self, stop_event=None):
        self.running = True
        self.stop_event = stop_event  # 来自服务的停止事件
        self.clock = common.get_reliable_clock()
        self.config = {}
        self.server_socket = None
        self.server_thread = None
        self.tcp_port = 54321
        
        # 工作线程
        self.global_scan_thread = None
        self.focus_monitor_thread = None
        self.vision_analyzer_thread = None
        
        # 智能监控管理器
        self.lifecycle_manager = None
        self.ai_manager = None

        # 数据库管理器
        self.database_manager = None

        # AI管理器
        self.unified_ai_manager = None

        # 通知管理器
        self.notification_manager = None
        
        # 初始化
        self._initialize()
    
    def _initialize(self):
        """初始化GuardianCore"""
        try:
            # 加载配置
            self.config = common.load_config()

            # 初始化数据库管理器
            self.database_manager = DatabaseManager()

            # 初始化统一AI管理器
            self.unified_ai_manager = AIManager()

            # 初始化通知管理器
            self.notification_manager = NotificationManager(self.database_manager)

            # 初始化智能监控管理器
            if uia_base.UIA_LOADED:
                self.lifecycle_manager = StrategyLifecycleManager()
                self.ai_manager = AIAuditManager()
                print("智能浏览器监控组件初始化成功")
            else:
                print("警告: UIA组件加载失败，浏览器监控功能不可用")
            
            # 启动TCP服务器
            self._start_tcp_server()
            
            print("GuardianCore初始化完成")
            
        except Exception as e:
            print(f"GuardianCore初始化失败: {str(e)}")
            sys.exit(1)
    
    def _start_tcp_server(self):
        """启动TCP服务器用于与前端通信"""
        try:
            self.server_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.server_socket.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
            self.server_socket.bind(('127.0.0.1', self.tcp_port))
            self.server_socket.listen(5)
            
            self.server_thread = threading.Thread(target=self._handle_tcp_connections, daemon=True)
            self.server_thread.start()
            
            print(f"TCP服务器启动成功，监听端口: {self.tcp_port}")
            
        except Exception as e:
            print(f"启动TCP服务器失败: {str(e)}")
    
    def _handle_tcp_connections(self):
        """处理TCP连接"""
        while self.running:
            try:
                if self.server_socket:
                    client_socket, addr = self.server_socket.accept()
                    client_thread = threading.Thread(
                        target=self._handle_client,
                        args=(client_socket,),
                        daemon=True
                    )
                    client_thread.start()
            except Exception as e:
                if self.running:
                    print(f"处理TCP连接失败: {str(e)}")
                break
    
    def _handle_client(self, client_socket):
        """处理客户端连接"""
        try:
            # 接收数据
            data = client_socket.recv(4096).decode('utf-8')
            if not data:
                return
            
            # 解析命令
            try:
                request = json.loads(data)
                command = request.get('command', '')
                request_data = request.get('data', {})
            except json.JSONDecodeError:
                response = {'status': 'error', 'message': '无效的JSON格式'}
                client_socket.send(json.dumps(response).encode('utf-8'))
                return
            
            # 处理命令
            response = self._process_command(command, request_data)
            
            # 发送响应
            client_socket.send(json.dumps(response).encode('utf-8'))
            
        except Exception as e:
            error_response = {'status': 'error', 'message': str(e)}
            try:
                client_socket.send(json.dumps(error_response).encode('utf-8'))
            except:
                pass
        finally:
            try:
                client_socket.close()
            except:
                pass
    
    def _process_command(self, command: str, data: Dict[str, Any]) -> Dict[str, Any]:
        """处理命令"""
        try:
            if command == 'get_rules':
                return self._get_rules()
            elif command == 'add_rule':
                return self._add_rule(data.get('rule', ''))
            elif command == 'remove_rule':
                return self._remove_rule(data.get('rule', ''))
            elif command == 'get_process_list':
                return self._get_process_list()
            elif command == 'set_uninstall_window':
                return self._set_uninstall_window(data)
            elif command == 'get_status':
                return self._get_status()
            elif command == 'deactivate_protection':
                return self._deactivate_protection()
            else:
                return {'status': 'error', 'message': f'未知命令: {command}'}
                
        except Exception as e:
            return {'status': 'error', 'message': str(e)}
    
    def _get_rules(self) -> Dict[str, Any]:
        """获取黑名单规则"""
        try:
            rules = common.get_blacklist_rules()
            return {'status': 'success', 'rules': rules}
        except Exception as e:
            return {'status': 'error', 'message': str(e)}
    
    def _add_rule(self, rule: str) -> Dict[str, Any]:
        """添加黑名单规则"""
        try:
            # 添加规则
            common.add_blacklist_rule(rule)
            return {'status': 'success', 'message': '规则添加成功'}
        except ValueError as e:
            return {'status': 'error', 'message': str(e)}
        except Exception as e:
            return {'status': 'error', 'message': str(e)}
    
    def _remove_rule(self, rule: str) -> Dict[str, Any]:
        """删除黑名单规则"""
        try:
            # 删除规则
            common.remove_blacklist_rule(rule)
            return {'status': 'success', 'message': '规则删除成功'}
        except ValueError as e:
            return {'status': 'error', 'message': str(e)}
        except Exception as e:
            return {'status': 'error', 'message': str(e)}
    
    def _get_process_list(self) -> Dict[str, Any]:
        """获取进程列表"""
        try:
            processes = common.get_running_processes()
            return {'status': 'success', 'processes': processes}
        except Exception as e:
            return {'status': 'error', 'message': str(e)}
    
    def _set_uninstall_window(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """设置卸载窗口期"""
        try:
            self.config['uninstall_window'] = data
            common.save_config(self.config)
            return {'status': 'success', 'message': '窗口期设置成功'}
        except Exception as e:
            return {'status': 'error', 'message': str(e)}
    
    def _get_status(self) -> Dict[str, Any]:
        """获取系统状态"""
        try:
            is_time_synced = self.clock.is_synced()
            status_payload = {
                'status': 'success',
                'is_time_synced': is_time_synced,
                'current_time': None,
                'current_datetime': None,
                'uninstall_window': self.config.get('uninstall_window', {}),
                'in_uninstall_window': False,
                'blacklist_integrity': common.verify_blacklist_integrity(),
                'features': self.config.get('features', {})
            }

            if is_time_synced:
                current_time = self.clock.now()
                current_dt = self.clock.get_datetime()
                status_payload['current_time'] = current_time
                status_payload['current_datetime'] = current_dt.isoformat()

                # 检查是否在窗口期
                window_config = status_payload['uninstall_window']
                if window_config.get('enabled', False):
                    status_payload['in_uninstall_window'] = self.clock.is_time_in_range(
                        window_config.get('start_time', '02:00'),
                        window_config.get('end_time', '04:00'),
                        window_config.get('day_of_week')
                    )
            
            return status_payload
        except Exception as e:
            return {'status': 'error', 'message': str(e)}

    def _deactivate_protection(self) -> Dict[str, Any]:
        """停用保护（仅在窗口期允许）"""
        try:
            # 必须先确保时间同步
            if not self.clock.is_synced():
                return {'status': 'error', 'message': '时间未同步，无法验证窗口期'}

            # 检查是否在窗口期
            window_config = self.config.get('uninstall_window', {})

            if not window_config.get('enabled', False):
                return {'status': 'error', 'message': '未设置窗口期'}

            in_window = self.clock.is_time_in_range(
                window_config.get('start_time', '02:00'),
                window_config.get('end_time', '04:00'),
                window_config.get('day_of_week')
            )

            if not in_window:
                return {'status': 'error', 'message': '当前不在允许的窗口期内'}

            # 停用保护 - 服务版本通过停止和删除服务来实现
            service_name = self.config.get('service_name', 'WinSecEnhSvc')

            # 在单独线程中执行服务停止和禁用，避免阻塞响应
            def stop_and_disable_service():
                try:
                    import subprocess
                    import time

                    # 等待一小段时间让响应发送完成
                    time.sleep(2)

                    # 停止服务
                    stop_cmd = ['sc', 'stop', service_name]
                    subprocess.run(stop_cmd, capture_output=True, text=True)

                    # 等待服务完全停止
                    time.sleep(3)

                    # 禁用服务自启动
                    disable_cmd = ['sc', 'config', service_name, 'start=', 'disabled']
                    subprocess.run(disable_cmd, capture_output=True, text=True)

                except Exception as e:
                    print(f"停止和禁用服务失败: {str(e)}")

            threading.Thread(target=stop_and_disable_service, daemon=True).start()
            return {'status': 'success', 'message': '保护已停用'}

        except Exception as e:
            return {'status': 'error', 'message': str(e)}

    def run(self):
        """主运行方法 - 启动所有工作线程"""
        print("GuardianCore开始运行...")

        # 读取配置中的功能开关
        features = self.config.get('features', {})

        # 根据开关启动相应的工作线程
        if features.get('enable_global_process_scan', True):
            self.global_scan_thread = threading.Thread(target=self._worker_global_scan, daemon=True)
            self.global_scan_thread.start()
            print("全局进程扫描线程已启动")

        if features.get('enable_focus_content_monitor', True) and uia_base.UIA_LOADED:
            self.focus_monitor_thread = threading.Thread(target=self._worker_focus_monitor, daemon=True)
            self.focus_monitor_thread.start()
            print("焦点内容监控线程已启动")

        if features.get('enable_vision_analysis', True):
            self.vision_analyzer_thread = threading.Thread(target=self._worker_vision_analyzer, daemon=True)
            self.vision_analyzer_thread.start()
            print("智能视觉分析线程已启动")

        # 主线程进入等待循环
        while self.running:
            try:
                # 检查服务停止事件
                if self.stop_event and self.stop_event.is_set():
                    print("收到服务停止信号，正在退出...")
                    break

                # 定期校准时间（每小时一次）
                if hasattr(self, '_last_calibration_time'):
                    current_time = time.monotonic()
                    if current_time - self._last_calibration_time > 3600:  # 1小时
                        self.clock.calibrate()
                        self._last_calibration_time = current_time
                else:
                    self._last_calibration_time = time.monotonic()

                # 休眠
                time.sleep(10)  # 主线程休眠时间较长

            except KeyboardInterrupt:
                print("收到中断信号，正在退出...")
                break
            except Exception as e:
                print(f"主循环异常: {str(e)}")
                time.sleep(5)

        self._shutdown()

    def _worker_global_scan(self):
        """工作线程1: 全局进程扫描（继承自项目一）"""
        print("全局进程扫描工作线程开始运行...")

        while self.running:
            try:
                # 检查停止事件
                if self.stop_event and self.stop_event.is_set():
                    break

                # 检查时间同步状态
                if self.clock.is_synced():
                    # 执行黑名单规则
                    self._execute_blacklist_rules()
                else:
                    print("等待网络时间同步...")

                # 休眠
                time.sleep(5)

            except Exception as e:
                print(f"全局进程扫描异常: {str(e)}")
                time.sleep(5)

        print("全局进程扫描工作线程已停止")

    def _execute_blacklist_rules(self):
        """执行黑名单规则（从原Guardian类移植）"""
        try:
            is_time_synced = self.clock.is_synced()
            if not is_time_synced:
                print("警告: 时间未同步，将执行严格查杀模式 (忽略时间段)。")

            # 从配置中读取黑名单规则
            rules = common.get_blacklist_rules()

            if not rules:
                return

            # 执行每个规则
            for rule in rules:
                try:
                    if not rule.strip():
                        continue

                    rule_info = self._parse_blacklist_rule(rule)
                    should_kill = False

                    if is_time_synced:
                        # 时间同步，按规则判断
                        should_kill = self._should_kill_process(rule_info)
                    else:
                        # 时间未同步，默认查杀所有黑名单进程
                        should_kill = True

                    if should_kill:
                        killed_count = common.kill_process_by_name(rule_info['process_name'])
                        if killed_count > 0:
                            print(f"已终止 {killed_count} 个 {rule_info['process_name']} 进程")

                except Exception as e:
                    print(f"执行规则失败 '{rule}': {str(e)}")

        except Exception as e:
            print(f"执行黑名单规则异常: {str(e)}")

    def _parse_blacklist_rule(self, rule: str) -> Dict[str, Any]:
        """解析黑名单规则（从原Guardian类移植）"""
        parts = rule.split(',')
        process_name = parts[0].strip()

        if len(parts) == 1:
            # 全天候规则
            return {
                'process_name': process_name,
                'all_day': True,
                'start_time': None,
                'end_time': None
            }
        elif len(parts) == 3:
            # 时间段规则
            start_time = parts[1].strip()
            end_time = parts[2].strip()
            return {
                'process_name': process_name,
                'all_day': False,
                'start_time': start_time,
                'end_time': end_time
            }
        else:
            raise ValueError(f"无效的规则格式: {rule}")

    def _should_kill_process(self, rule_info: Dict[str, Any]) -> bool:
        """判断是否应该终止进程（从原Guardian类移植）"""
        if rule_info['all_day']:
            return True

        # 检查时间范围
        return self.clock.is_time_in_range(
            rule_info['start_time'],
            rule_info['end_time']
        )

    def _worker_focus_monitor(self):
        """工作线程2: 焦点内容监控（继承自项目二）"""
        print("焦点内容监控工作线程开始运行...")

        last_hwnd = None
        last_process_name = None

        while self.running:
            try:
                # 检查停止事件
                if self.stop_event and self.stop_event.is_set():
                    break

                # 获取前台窗口
                hwnd = uia_base.user32.GetForegroundWindow()
                if not hwnd:
                    if last_hwnd is not None:
                        last_hwnd, last_process_name = None, None
                    time.sleep(1.5)
                    continue

                # 获取进程信息
                process_name, window_title = uia_base.get_window_process_name_and_title(hwnd)
                normalized_process_name = process_name.lower().replace('.exe', '') if process_name else ''

                # 检查是否为浏览器
                if normalized_process_name not in monitor_config.ALLOWED_BROWSERS:
                    if last_hwnd != hwnd:
                        last_hwnd, last_process_name = hwnd, process_name
                    time.sleep(1.5)
                    continue

                # 如果是同一个窗口，跳过
                if hwnd == last_hwnd:
                    time.sleep(1.5)
                    continue

                last_hwnd, last_process_name = hwnd, process_name

                # 获取浏览器信息
                current_url, doc_title, url_strategy, window_element, status_info = get_browser_info_with_lifecycle(
                    hwnd, process_name, self.lifecycle_manager
                )

                if doc_title:
                    window_title = doc_title

                # 执行决策三部曲
                should_close = False
                close_reason = ""

                if window_element:
                    # 0. 检查临时白名单
                    if current_url and self.database_manager.is_in_temp_whitelist("url", current_url):
                        # URL在临时白名单中，跳过所有检查
                        pass
                    else:
                        # 1. 检查标题黑名单
                        if check_title_against_blacklist(window_title, monitor_config.BLACKLIST_KEYWORDS):
                            should_close = True
                            close_reason = f"标题命中黑名单: {window_title}"

                        # 2. 检查URL黑名单
                        elif check_url_against_blacklist(current_url, monitor_config.URL_BLACKLIST_KEYWORDS):
                            should_close = True
                            close_reason = f"URL命中黑名单: {current_url}"

                        # 3. AI内容审核
                        elif current_url and self.unified_ai_manager:
                            decision = self.unified_ai_manager.audit_text(current_url, window_title)
                            if decision == 'deny':
                                should_close = True
                                close_reason = f"AI审核拒绝: {window_title}"

                # 执行关闭操作
                if should_close:
                    print(f"[焦点监控] {close_reason}")
                    success = close_browser_tab(window_element, process_name, self.lifecycle_manager)
                    if success:
                        print(f"[焦点监控] 标签页关闭成功")

                        # 如果是AI审核拒绝，显示通知
                        if "AI审核拒绝" in close_reason and current_url:
                            try:
                                # 在单独线程中显示通知，避免阻塞主循环
                                def show_notification():
                                    self.notification_manager.show_ai_rejection_notification(current_url, window_title)

                                import threading
                                notification_thread = threading.Thread(target=show_notification, daemon=True)
                                notification_thread.start()
                            except Exception as e:
                                print(f"[焦点监控] 显示通知失败: {str(e)}")
                    else:
                        print(f"[焦点监控] 标签页关闭失败")

                # 休眠
                time.sleep(1.5)

            except Exception as e:
                print(f"焦点内容监控异常: {str(e)}")
                time.sleep(1.5)

        print("焦点内容监控工作线程已停止")

    def _worker_vision_analyzer(self):
        """工作线程3: 智能视觉分析（新功能）"""
        print("智能视觉分析工作线程开始运行...")

        # 应用使用时长跟踪
        app_focus_tracker = {}  # process_name -> {'start_time': timestamp, 'total_today': seconds}
        last_focused_app = None
        last_focus_start = None

        # 获取配置
        vision_config = common.get_ai_config('vision_audit')
        trigger_minutes = vision_config.get('focus_time_trigger_minutes', 30)

        while self.running:
            try:
                # 检查停止事件
                if self.stop_event and self.stop_event.is_set():
                    break

                # 获取当前前台窗口
                hwnd = uia_base.user32.GetForegroundWindow()
                if not hwnd:
                    # 没有前台窗口，结束当前应用的计时
                    if last_focused_app and last_focus_start:
                        self._end_focus_session(last_focused_app, last_focus_start, app_focus_tracker)
                        last_focused_app = None
                        last_focus_start = None
                    time.sleep(5)
                    continue

                # 获取进程信息
                process_name, window_title = uia_base.get_window_process_name_and_title(hwnd)
                if not process_name:
                    time.sleep(5)
                    continue

                current_time = time.time()

                # 检查是否切换了应用
                if process_name != last_focused_app:
                    # 结束上一个应用的计时
                    if last_focused_app and last_focus_start:
                        self._end_focus_session(last_focused_app, last_focus_start, app_focus_tracker)

                    # 开始新应用的计时
                    last_focused_app = process_name
                    last_focus_start = current_time

                    # 初始化应用跟踪记录
                    if process_name not in app_focus_tracker:
                        app_focus_tracker[process_name] = {
                            'total_today': self.database_manager.get_daily_focus_time(process_name),
                            'sessions': []
                        }

                # 检查是否需要进行AI分类
                if process_name in app_focus_tracker:
                    total_time = app_focus_tracker[process_name]['total_today']
                    current_session_time = current_time - last_focus_start if last_focus_start else 0
                    total_with_current = total_time + current_session_time

                    # 如果总使用时间超过阈值且应用未分类，进行AI分类
                    if total_with_current > (trigger_minutes * 60):
                        existing_category = self.database_manager.get_app_category(process_name)
                        if not existing_category:
                            print(f"[视觉分析] 应用 {process_name} 使用时长超过阈值，开始AI分类...")
                            self._classify_application(process_name, hwnd)

                # 检查时间限制规则
                self._check_time_limits(process_name, app_focus_tracker)

                # 休眠
                time.sleep(5)

            except Exception as e:
                print(f"智能视觉分析异常: {str(e)}")
                time.sleep(5)

        # 工作线程结束时，保存最后的会话
        if last_focused_app and last_focus_start:
            self._end_focus_session(last_focused_app, last_focus_start, app_focus_tracker)

        print("智能视觉分析工作线程已停止")

    def _end_focus_session(self, process_name: str, start_time: float, app_focus_tracker: dict):
        """结束专注会话并记录到数据库"""
        try:
            end_time = time.time()
            duration = int(end_time - start_time)

            if duration > 0:  # 只记录有效的时长
                # 记录到数据库
                self.database_manager.add_focus_log(
                    process_name=process_name,
                    start_time=int(start_time),
                    end_time=int(end_time),
                    duration_seconds=duration
                )

                # 更新跟踪器
                if process_name in app_focus_tracker:
                    app_focus_tracker[process_name]['total_today'] += duration

                print(f"[视觉分析] 记录专注时长: {process_name} - {duration}秒")

        except Exception as e:
            print(f"记录专注会话失败: {str(e)}")

    def _classify_application(self, process_name: str, hwnd: int):
        """对应用进行AI分类"""
        try:
            # 截取应用窗口
            screenshot_bytes = common.take_screenshot(hwnd)
            if not screenshot_bytes:
                print(f"[视觉分析] 截图失败: {process_name}")
                return

            # 调用AI进行分类
            category = self.unified_ai_manager.classify_image(screenshot_bytes)
            if category and category != 'error':
                # 保存分类结果
                self.database_manager.set_app_category(process_name, category)
                print(f"[视觉分析] AI分类完成: {process_name} -> {category}")

                # 显示分类通知
                try:
                    def show_notification():
                        self.notification_manager.show_classification_notification(process_name, category)

                    import threading
                    notification_thread = threading.Thread(target=show_notification, daemon=True)
                    notification_thread.start()
                except Exception as e:
                    print(f"[视觉分析] 显示分类通知失败: {str(e)}")
            else:
                print(f"[视觉分析] AI分类失败: {process_name}")

        except Exception as e:
            print(f"应用分类失败: {str(e)}")

    def _check_time_limits(self, process_name: str, app_focus_tracker: dict):
        """检查时间限制规则"""
        try:
            # 获取应用分类
            category = self.database_manager.get_app_category(process_name)
            if not category:
                return

            # 获取该分类的时间限制规则
            time_limit_rules = common.get_time_limit_rules()
            if category not in time_limit_rules:
                return

            rule = time_limit_rules[category]
            if not rule.get('enabled', False):
                return

            # 计算今日该分类的总使用时间
            total_category_time = self.database_manager.get_category_daily_focus_time(category)

            # 加上当前会话时间
            if process_name in app_focus_tracker:
                current_session = time.time() - app_focus_tracker[process_name].get('last_start', time.time())
                total_category_time += current_session

            # 检查是否超过限制
            limit_seconds = rule.get('limit_minutes_per_day', 60) * 60
            if total_category_time > limit_seconds:
                print(f"[视觉分析] 分类 {category} 使用时间超限，强制关闭 {process_name}")
                # 强制关闭进程
                killed_count = common.kill_process_by_name(process_name)
                if killed_count > 0:
                    print(f"[视觉分析] 已终止 {killed_count} 个 {process_name} 进程")

                    # 显示时间限制通知
                    try:
                        def show_notification():
                            self.notification_manager.show_time_limit_notification(category, process_name)

                        import threading
                        notification_thread = threading.Thread(target=show_notification, daemon=True)
                        notification_thread.start()
                    except Exception as e:
                        print(f"[视觉分析] 显示通知失败: {str(e)}")

        except Exception as e:
            print(f"检查时间限制失败: {str(e)}")

    def _shutdown(self):
        """关闭GuardianCore"""
        print("GuardianCore正在关闭...")
        self.running = False

        try:
            # 关闭TCP服务器
            if self.server_socket:
                self.server_socket.close()
                self.server_socket = None

        except Exception as e:
            print(f"关闭过程中出现异常: {str(e)}")


class GuardianService(win32serviceutil.ServiceFramework):
    """统一守护Windows服务包装类"""

    _svc_name_ = "WinSecEnhSvc"
    _svc_display_name_ = "Windows Security Enhancement Service"
    _svc_description_ = "Provides enhanced security monitoring and protection for the system"

    def __init__(self, args):
        win32serviceutil.ServiceFramework.__init__(self, args)
        self.hWaitStop = win32event.CreateEvent(None, 0, 0, None)
        self.guardian_core = None

    def SvcStop(self):
        """服务停止处理"""
        self.ReportServiceStatus(win32service.SERVICE_STOP_PENDING)
        win32event.SetEvent(self.hWaitStop)

        # 停止GuardianCore
        if self.guardian_core:
            self.guardian_core.running = False

    def SvcDoRun(self):
        """服务主运行方法"""
        try:
            # 记录服务启动
            servicemanager.LogMsg(
                servicemanager.EVENTLOG_INFORMATION_TYPE,
                servicemanager.PYS_SERVICE_STARTED,
                (self._svc_name_, '')
            )

            # 创建停止事件对象
            import threading
            stop_event = threading.Event()

            # 创建GuardianCore实例
            self.guardian_core = GuardianCore(stop_event=stop_event)

            # 在单独线程中运行GuardianCore
            guardian_thread = threading.Thread(target=self.guardian_core.run, daemon=True)
            guardian_thread.start()

            # 等待停止信号
            while True:
                # 等待停止事件，超时时间为1秒
                rc = win32event.WaitForSingleObject(self.hWaitStop, 1000)
                if rc == win32event.WAIT_OBJECT_0:
                    # 收到停止信号
                    stop_event.set()
                    break

        except Exception as e:
            # 记录错误
            servicemanager.LogErrorMsg(f"统一守护服务运行异常: {str(e)}")

        finally:
            # 记录服务停止
            servicemanager.LogMsg(
                servicemanager.EVENTLOG_INFORMATION_TYPE,
                servicemanager.PYS_SERVICE_STOPPED,
                (self._svc_name_, '')
            )


def main():
    """主函数 - 处理命令行参数"""
    if not WIN32_AVAILABLE:
        print("错误: 需要安装pywin32库才能运行Windows服务")
        sys.exit(1)

    if len(sys.argv) == 1:
        # 没有参数，尝试启动服务
        try:
            servicemanager.Initialize()
            servicemanager.PrepareToHostSingle(GuardianService)
            servicemanager.StartServiceCtrlDispatcher()
        except Exception as e:
            print(f"启动服务失败: {str(e)}")
            # 如果作为服务启动失败，尝试作为普通程序运行（用于调试）
            print("尝试作为普通程序运行...")
            guardian_core = GuardianCore()
            guardian_core.run()
    else:
        # 有参数，使用win32serviceutil处理
        win32serviceutil.HandleCommandLine(GuardianService)


if __name__ == "__main__":
    main()
