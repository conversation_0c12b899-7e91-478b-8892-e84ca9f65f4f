# intelligent_monitor/test_gui.py (v3.1 - 手动关闭增强)

import tkinter as tk
from tkinter import ttk, font, scrolledtext
import threading
import time
import queue
import os
import random
import io
import sys

# --- 项目模块导入 ---
# sys.path.append(os.path.dirname(os.path.abspath(__file__))) # 这行如果test_gui在根目录，可以省略

import config
from core import uia_base
from modules.managers import StrategyLifecycleManager, AIAuditManager
from modules.browser_info_fetcher import get_browser_info_with_lifecycle
from modules.tab_closer import close_browser_tab
from modules.blacklist_checker import check_title_against_blacklist, check_url_against_blacklist


# --- 自定义流，用于重定向print输出 ---
class QueueIO(io.TextIOBase):
    def __init__(self, q):
        self.queue = q

    def write(self, s):
        self.queue.put(("log_update", s))
        return len(s)

    def flush(self):
        pass


class MonitorApp:
    def __init__(self, root):
        self.root = root
        self.root.title("智能监控 - 高级测试工具 v3.1")
        self.root.geometry("750x700")  # 增加了窗口高度以容纳日志
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)

        # --- 状态与核心变量 ---
        self.monitor_thread = None
        self.stop_event = threading.Event()
        self.update_queue = queue.Queue()
        self.last_reported_state = (None, None, None)
        self.current_context = {}

        # --- 管理器 ---
        self.lifecycle_manager = StrategyLifecycleManager()
        self.ai_manager = AIAuditManager()
        self.ai_manager.set_result_callback(self.on_ai_result_received)

        # --- TKinter 变量 ---
        self.process_name_var = tk.StringVar(value="未开始监控")
        self.is_browser_var = tk.StringVar(value="N/A")
        self.title_var = tk.StringVar(value="N/A")
        self.url_var = tk.StringVar(value="N/A")
        self.strategy_var = tk.StringVar(value="N/A")
        self.status_var = tk.StringVar(value="等待开始...")
        self.ai_result_var = tk.StringVar(value="等待手动触发...")

        # --- 控制开关变量 ---
        self.ai_audit_enabled = tk.BooleanVar(value=config.AI_AUDIT_CONFIG['enabled'])
        self.auto_close_enabled = tk.BooleanVar(value=True)

        self.create_widgets()
        self.process_queue()

        # --- 设置print重定向 ---
        self.log_queue = self.update_queue
        self.old_stdout = sys.stdout
        sys.stdout = QueueIO(self.log_queue)
        config.DEBUG_BLACKLIST = True  # 强制开启关闭调试日志
        config.DEBUG_LIFECYCLE = True  # 强制开启生命周期调试日志

    def create_widgets(self):
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # 字体
        label_font = font.Font(family="Arial", size=10, weight="bold")
        value_font = font.Font(family="Consolas", size=10)

        # --- 实时信息显示区 (不变) ---
        info_frame = ttk.LabelFrame(main_frame, text="实时焦点信息 (仅在内容变化时更新)", padding="10")
        info_frame.pack(fill=tk.X, pady=5)
        # ... (内容和 v2.0 一样)
        info_frame.columnconfigure(1, weight=1)
        labels = ["进程名:", "是否浏览器:", "标题:", "URL:", "获取策略:"]
        variables = [self.process_name_var, self.is_browser_var, self.title_var, self.url_var, self.strategy_var]
        for i, (l_text, var) in enumerate(zip(labels, variables)):
            ttk.Label(info_frame, text=l_text, font=label_font).grid(row=i, column=0, sticky=tk.W, pady=2)
            ttk.Label(info_frame, textvariable=var, font=value_font, wraplength=580, anchor="w", justify=tk.LEFT).grid(
                row=i, column=1, sticky=tk.EW, padx=5, pady=2)

        # --- 控制与操作区 (基本不变) ---
        action_frame_container = ttk.Frame(main_frame)
        action_frame_container.pack(fill=tk.X, pady=5)
        control_frame = ttk.LabelFrame(action_frame_container, text="自动化开关", padding="10")
        control_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 5))
        ttk.Checkbutton(control_frame, text="开启AI审核", variable=self.ai_audit_enabled,
                        command=self.toggle_ai_audit).pack(anchor=tk.W)
        ttk.Checkbutton(control_frame, text="开启自动关闭", variable=self.auto_close_enabled).pack(anchor=tk.W)
        manual_action_frame = ttk.LabelFrame(action_frame_container, text="手动测试操作", padding="10")
        manual_action_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        self.manual_close_button = ttk.Button(manual_action_frame, text="手动关闭当前标签页",
                                              command=self.delayed_manual_close_tab, state=tk.DISABLED)
        self.manual_close_button.pack(fill=tk.X, pady=2)
        self.manual_ai_button = ttk.Button(manual_action_frame, text="手动发送给AI审核", command=self.manual_ai_audit,
                                           state=tk.DISABLED)
        self.manual_ai_button.pack(fill=tk.X, pady=2)

        # --- 日志/Prompt 显示区 (重大改动) ---
        log_frame = ttk.LabelFrame(main_frame, text="详细日志 / AI Prompt", padding="10")
        log_frame.pack(fill=tk.BOTH, expand=True, pady=5)
        self.log_text = scrolledtext.ScrolledText(log_frame, height=10, wrap=tk.WORD, state=tk.DISABLED,
                                                  font=("Consolas", 9))
        self.log_text.pack(fill=tk.BOTH, expand=True, pady=(2, 5))

        result_label_frame = ttk.Frame(log_frame)
        result_label_frame.pack(fill=tk.X)
        ttk.Label(result_label_frame, text="AI返回结果:", font=label_font).pack(side=tk.LEFT)
        ttk.Label(result_label_frame, textvariable=self.ai_result_var, font=value_font).pack(side=tk.LEFT, padx=5)

        # --- 底部按钮和状态栏 (不变) ---
        bottom_frame = ttk.Frame(main_frame)
        bottom_frame.pack(fill=tk.X, pady=5)
        # ... (内容和 v2.0 一样)
        self.start_button = ttk.Button(bottom_frame, text="开始监控", command=self.start_monitor)
        self.start_button.pack(side=tk.LEFT, expand=True, fill=tk.X, padx=(0, 5))
        self.stop_button = ttk.Button(bottom_frame, text="停止监控", command=self.stop_monitor, state=tk.DISABLED)
        self.stop_button.pack(side=tk.LEFT, expand=True, fill=tk.X, padx=5)
        ttk.Button(bottom_frame, text="清除所有缓存", command=self.clear_caches).pack(side=tk.LEFT, expand=True,
                                                                                      fill=tk.X)
        status_bar = ttk.Frame(self.root, relief=tk.SUNKEN, padding=(5, 2))
        status_bar.pack(fill=tk.X, side=tk.BOTTOM)
        ttk.Label(status_bar, textvariable=self.status_var).pack(side=tk.LEFT)

    def process_queue(self):
        try:
            while not self.update_queue.empty():
                msg_type, data = self.update_queue.get_nowait()
                if msg_type == "force_refresh":
                    self.last_reported_state = (None, None, None)
                elif msg_type == "main_update":
                    self.process_name_var.set(data.get("process_name", "N/A"))
                    self.is_browser_var.set("是" if data.get("is_browser") else "否")
                    self.title_var.set(data.get("title", "N/A"))
                    self.url_var.set(data.get("url", "N/A"))
                    self.strategy_var.set(data.get("strategy", "N/A"))
                elif msg_type == "status_update":
                    self.status_var.set(data)
                elif msg_type == "ui_control":
                    state = tk.NORMAL if data.get("buttons_enabled") else tk.DISABLED
                    if self.manual_close_button['state'] != tk.DISABLED or state == tk.NORMAL:
                        self.manual_close_button.config(state=state)
                    self.manual_ai_button.config(state=state)
                elif msg_type == "log_update":
                    self.log_text.config(state=tk.NORMAL)
                    self.log_text.insert(tk.END, data)
                    self.log_text.see(tk.END)  # 自动滚动到底部
                    self.log_text.config(state=tk.DISABLED)
                elif msg_type == "clear_log":
                    self.log_text.config(state=tk.NORMAL)
                    self.log_text.delete(1.0, tk.END)
                    self.log_text.config(state=tk.DISABLED)
                elif msg_type == "ai_result":
                    self.ai_result_var.set(data.upper())
        finally:
            self.root.after(100, self.process_queue)

    def monitoring_loop(self):
        while not self.stop_event.is_set():
            time.sleep(1.0)
            try:
                hwnd = uia_base.user32.GetForegroundWindow()
                if not hwnd:
                    self.update_queue.put(("status_update", "暂无活动窗口..."))
                    continue

                process_name, window_title = uia_base.get_window_process_name_and_title(hwnd)
                is_browser = process_name in config.BROWSER_PROCESSES

                self.update_queue.put(("ui_control", {"buttons_enabled": is_browser}))

                current_url, doc_title, url_strategy, window_element = None, None, "N/A", None

                if is_browser:
                    current_url, doc_title, url_strategy, window_element, _ = \
                        get_browser_info_with_lifecycle(hwnd, process_name, self.lifecycle_manager)
                    if doc_title: window_title = doc_title

                    self.current_context = {
                        "window_element": window_element,
                        "process_name": process_name,
                        "url": current_url,
                        "title": window_title
                    }
                else:
                    self.current_context = {}

                current_state = (process_name, window_title, current_url)
                if current_state != self.last_reported_state:
                    self.last_reported_state = current_state
                    update_data = {
                        "process_name": process_name, "is_browser": is_browser,
                        "title": window_title, "url": current_url or ("非浏览器" if not is_browser else "未能获取"),
                        "strategy": url_strategy
                    }
                    self.update_queue.put(("main_update", update_data))
                    self.update_queue.put(("status_update", "状态已更新。"))
                else:
                    status_msg = f"状态未变. "
                    if is_browser:
                        status_msg += f"策略: {url_strategy}"
                    self.update_queue.put(("status_update", status_msg))

                # --- 自动化决策逻辑 ---
                if is_browser and self.auto_close_enabled.get() and window_element:
                    close_reason = ""
                    if check_title_against_blacklist(window_title, config.BLACKLIST_KEYWORDS):
                        close_reason = f"标题黑名单: {window_title[:30]}..."
                    elif check_url_against_blacklist(current_url, config.URL_BLACKLIST_KEYWORDS):
                        close_reason = f"URL黑名单: {current_url[:30]}..."
                    elif self.ai_audit_enabled.get() and current_url:
                        decision = self.ai_manager.check_and_audit(current_url, window_title)
                        if decision == 'deny':
                            close_reason = f"AI审核拒绝: {window_title[:30]}..."
                        elif decision == 'pending':
                            self.update_queue.put(("status_update", f"状态未变. 策略: {url_strategy} (AI审核中...)"))

                    if close_reason:
                        self.update_queue.put(("status_update", f"检测到违规，正在关闭: {close_reason}"))
                        # 在后台静默执行，日志通过print重定向显示
                        if close_browser_tab(window_element, process_name, self.lifecycle_manager,
                                             active_tab_title=window_title):
                            self.update_queue.put(("status_update", "成功关闭标签页！"))
                            self.last_reported_state = (None, None, None)
                            time.sleep(0.5)
                        else:
                            self.update_queue.put(("status_update", "关闭标签页失败。"))
            except Exception as e:
                self.update_queue.put(("status_update", f"监控循环出错: {e}"))

    def start_monitor(self):
        if not (self.monitor_thread and self.monitor_thread.is_alive()):
            self.stop_event.clear()
            self.monitor_thread = threading.Thread(target=self.monitoring_loop, daemon=True)
            self.monitor_thread.start()
            self.start_button.config(state=tk.DISABLED)
            self.stop_button.config(state=tk.NORMAL)
            self.update_queue.put(("status_update", "监控已启动..."))

    def stop_monitor(self):
        self.stop_event.set()
        self.start_button.config(state=tk.NORMAL)
        self.stop_button.config(state=tk.DISABLED)
        self.manual_close_button.config(state=tk.DISABLED)
        self.manual_ai_button.config(state=tk.DISABLED)
        self.update_queue.put(("status_update", "监控已停止。"))

    def clear_caches(self):
        was_running = self.monitor_thread and self.monitor_thread.is_alive()
        if was_running: self.stop_monitor(); time.sleep(0.5)
        files_to_delete = [config.CACHE_FILE_PATH, config.AI_AUDIT_CONFIG['cache_file_path']]
        for f_path in files_to_delete:
            if os.path.exists(f_path): os.remove(f_path)
        self.lifecycle_manager.load_cache()
        self.ai_manager.load_cache()
        self.update_queue.put(("status_update", "已清除所有缓存。请重新开始监控。"))

    def toggle_ai_audit(self):
        config.AI_AUDIT_CONFIG['enabled'] = self.ai_audit_enabled.get()
        status = "开启" if config.AI_AUDIT_CONFIG['enabled'] else "关闭"
        self.update_queue.put(("status_update", f"自动AI审核功能已{status}。"))

    def delayed_manual_close_tab(self):
        self.update_queue.put(("clear_log", None))
        self.update_queue.put(("log_update", "关闭指令已发送...\n"))
        self.update_queue.put(("log_update", "请在 3 秒内将鼠标点击到目标浏览器窗口！\n"))
        self.manual_close_button.config(state=tk.DISABLED)
        threading.Thread(target=self._execute_close_after_delay, daemon=True).start()

    def _execute_close_after_delay(self):
        """后台执行关闭的实际逻辑"""
        time.sleep(3.0)
        try:
            hwnd = uia_base.user32.GetForegroundWindow()
            if not hwnd:
                self.update_queue.put(("log_update", "\n错误：未找到任何前台窗口。\n"))
                return

            process_name, window_title_fallback = uia_base.get_window_process_name_and_title(hwnd)
            if not (process_name in config.BROWSER_PROCESSES):
                self.update_queue.put(("log_update", f"\n错误：当前焦点窗口 '{process_name}' 不是浏览器。\n"))
                return

            self.update_queue.put(("log_update", f"检测到焦点窗口: {process_name}, 开始获取详细信息...\n"))

            # [关键修改] 在关闭前，先调用信息获取函数来得到精确的标签页标题
            current_url, doc_title, url_strategy, window_element, _ = \
                get_browser_info_with_lifecycle(hwnd, process_name, self.lifecycle_manager)

            # 如果获取到了文档标题，就用它，否则用窗口标题作为备用
            title_to_close = doc_title if doc_title else window_title_fallback

            self.update_queue.put(("log_update", f"获取到待关闭标签页标题: '{title_to_close}', 开始执行关闭策略...\n"))

            # [关键修改] 将获取到的标题传递给关闭函数
            success = close_browser_tab(window_element, process_name, self.lifecycle_manager,
                                        active_tab_title=title_to_close)

            result_msg = "\n--- 手动关闭成功！ ---\n" if success else "\n--- 手动关闭失败。 ---\n"
            self.update_queue.put(("log_update", result_msg))
            if success:
                self.update_queue.put(("force_refresh", None))
        except Exception as e:
            self.update_queue.put(("log_update", f"\n关闭过程中发生严重错误: {e}\n"))
        finally:
            self.update_queue.put(("ui_control", {"buttons_enabled": True}))

    def manual_ai_audit(self):
        self.update_queue.put(("clear_log", None))
        ctx = self.current_context
        if ctx.get("url") and ctx.get("title"):
            url, title = ctx["url"], ctx["title"]
            prompt = config.AI_AUDIT_CONFIG['prompt_template'].format(title=title, url=url)
            self.update_queue.put(("log_update", prompt))
            self.ai_result_var.set("已发送，等待AI返回...")
            self.ai_manager.check_and_audit(url, title)
        else:
            self.update_queue.put(("status_update", "无法审核：缺少URL或标题。"))

    def on_ai_result_received(self, url, decision, prompt):
        # 这是一个回调，可能从任何线程调用，所以使用队列是安全的
        if self.current_context.get("url") == url:
            self.update_queue.put(("ai_result", decision))
            self.update_queue.put(("status_update", f"收到AI审核结果: {decision.upper()}"))

    def on_closing(self):
        sys.stdout = self.old_stdout
        if self.monitor_thread and self.monitor_thread.is_alive():
            self.stop_event.set()
        self.root.destroy()


if __name__ == "__main__":
    if not uia_base.UIA_LOADED:
        print("UIA组件加载失败，无法启动。")
    else:
        root = tk.Tk()
        app = MonitorApp(root)
        root.mainloop()