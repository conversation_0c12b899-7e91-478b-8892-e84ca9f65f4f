"""
Setup - 安装程序
持有私钥的安装器，等待用户提供公钥加密的安装凭证
"""

import os
import sys
import base64
import shutil
import tempfile
import tkinter as tk
from tkinter import ttk, messagebox
import subprocess
from datetime import datetime
from typing import Optional

try:
    from cryptography.hazmat.primitives import serialization, hashes
    from cryptography.hazmat.primitives.asymmetric import rsa, padding
    CRYPTO_AVAILABLE = True
except ImportError:
    CRYPTO_AVAILABLE = False
    print("警告: cryptography库未安装，RSA功能不可用")

# 添加common模块到路径
sys.path.insert(0, os.path.join(os.path.dirname(os.path.dirname(__file__)), 'common'))

try:
    import common
    COMMON_AVAILABLE = True
except ImportError:
    COMMON_AVAILABLE = False
    print("警告: common模块不可用")

# 硬编码的RSA私钥（PEM格式）
# 这是真实的RSA私钥，用于解密安装凭证
PRIVATE_KEY_PEM = base64.b64decode("""
****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
""".strip())

class SetupInstaller:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("自律守护者 - 安装程序")
        self.root.geometry("500x400")
        self.root.resizable(False, False)

        # 检查权限
        if not self._check_admin():
            messagebox.showerror("错误", "安装程序需要管理员权限才能运行")
            sys.exit(1)

        # 检查依赖
        if not CRYPTO_AVAILABLE:
            messagebox.showerror("错误", "缺少必要的加密库，请安装cryptography")
            sys.exit(1)

        self.private_key = None
        self._load_private_key()
        self._create_gui()

    def _check_admin(self) -> bool:
        """检查管理员权限"""
        try:
            import ctypes
            return ctypes.windll.shell32.IsUserAnAdmin() != 0
        except Exception:
            return False

    def _load_private_key(self):
        """加载RSA私钥"""
        try:
            self.private_key = serialization.load_pem_private_key(
                PRIVATE_KEY_PEM,
                password=None
            )
            print("RSA私钥加载成功")
        except Exception as e:
            messagebox.showerror("错误", f"加载RSA私钥失败: {str(e)}")
            sys.exit(1)

    def _create_gui(self):
        """创建GUI界面"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # 标题
        title_label = ttk.Label(main_frame, text="自律守护者安装程序",
                               font=("Arial", 16, "bold"))
        title_label.pack(pady=(0, 20))

        # 说明文本
        info_text = """
请输入安装凭证以继续安装。
安装凭证是由配对的公钥加密的Base64字符串。
        """
        info_label = ttk.Label(main_frame, text=info_text.strip(),
                              justify=tk.CENTER)
        info_label.pack(pady=(0, 20))

        # 凭证输入区域
        credential_label = ttk.Label(main_frame, text="安装凭证:")
        credential_label.pack(anchor=tk.W)

        # 文本框
        self.credential_text = tk.Text(main_frame, height=8, width=60)
        self.credential_text.pack(pady=(5, 20), fill=tk.BOTH, expand=True)

        # 按钮框架
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X)

        # 安装按钮
        install_button = ttk.Button(button_frame, text="开始安装",
                                   command=self._start_installation)
        install_button.pack(side=tk.LEFT, padx=(0, 10))

        # 退出按钮
        exit_button = ttk.Button(button_frame, text="退出",
                                command=self.root.quit)
        exit_button.pack(side=tk.LEFT)

    def _decrypt_credential(self, credential: str) -> Optional[str]:
        """解密安装凭证"""
        try:
            # Base64解码
            encrypted_data = base64.b64decode(credential)

            # RSA解密
            decrypted_data = self.private_key.decrypt(
                encrypted_data,
                padding.OAEP(
                    mgf=padding.MGF1(algorithm=hashes.SHA256()),
                    algorithm=hashes.SHA256(),
                    label=None
                )
            )

            return decrypted_data.decode('utf-8')

        except Exception as e:
            print(f"解密凭证失败: {str(e)}")
            return None

    def _start_installation(self):
        """开始安装过程"""
        try:
            # 获取凭证
            credential = self.credential_text.get("1.0", tk.END).strip()
            if not credential:
                messagebox.showwarning("警告", "请输入安装凭证")
                return

            # 解密凭证
            decrypted_path = self._decrypt_credential(credential)
            if not decrypted_path:
                messagebox.showerror("错误", "无效的安装凭证")
                return

            # 验证路径
            if not os.path.isabs(decrypted_path):
                messagebox.showerror("错误", "安装路径必须是绝对路径")
                return

            # 开始安装
            self._perform_installation(decrypted_path)

        except Exception as e:
            messagebox.showerror("错误", f"安装失败: {str(e)}")

    def _perform_installation(self, install_path: str):
        """执行安装"""
        try:
            # 创建安装目录
            os.makedirs(install_path, exist_ok=True)

            # 生成系统化名称
            guardian_name = common.generate_system_name("Guard")

            # 复制文件
            self._copy_files(install_path, guardian_name)

            # 伪造时间戳
            self._forge_timestamps(install_path)

            # 锁定目录
            common.lock_directory(install_path)

            # 安装Windows服务
            service_name = self._install_service(install_path, guardian_name)

            # 初始化配置
            self._initialize_config(install_path, guardian_name, service_name)

            # 创建启动器配置文件
            self._create_launcher_config(install_path)

            # 创建桌面快捷方式
            self._create_shortcuts(install_path)

            # 自删除
            self._self_destruct()

            messagebox.showinfo("成功", "安装完成！程序将在重启后自动运行。")
            self.root.quit()

        except Exception as e:
            messagebox.showerror("错误", f"安装过程失败: {str(e)}")

    def _copy_files(self, install_path: str, guardian_name: str):
        """从最终分发包中复制.exe文件 - V6.0 服务版本"""
        try:
            # 假设 setup.exe 位于 Project-IronContract/installer/ 目录中
            installer_dir = os.path.dirname(os.path.abspath(sys.executable))
            dist_root = os.path.dirname(installer_dir) # 项目根目录

            # 定义源文件路径
            backend_src_dir = os.path.join(dist_root, 'backend')
            frontend_src_dir = os.path.join(dist_root, 'frontend')

            # 定义源文件和目标文件的映射 - V6.0 服务版本
            files_to_copy = {
                os.path.join(backend_src_dir, 'guardian_service.exe'): 'guardian_service.exe',
                os.path.join(frontend_src_dir, 'control_panel.exe'): 'control_panel.exe', # build_script已命名好
                os.path.join(frontend_src_dir, 'launcher.exe'): 'launcher.exe'
            }

            for src_file, dst_name in files_to_copy.items():
                dst_file = os.path.join(install_path, dst_name)

                if os.path.exists(src_file):
                    shutil.copy2(src_file, dst_file)
                    print(f"已复制: {os.path.basename(src_file)} -> {dst_name}")
                else:
                    # 提供更详细的错误信息
                    raise FileNotFoundError(f"找不到所需文件: {src_file}。请确保从 'Project-IronContract/installer' 目录中运行安装程序。")

            print("文件复制完成")

        except Exception as e:
            raise Exception(f"复制文件失败: {str(e)}")

    def _forge_timestamps(self, install_path: str):
        """伪造文件时间戳"""
        try:
            # 使用系统文件作为参考
            reference_file = os.path.join(os.environ.get('WINDIR', 'C:\\Windows'), 'System32', 'kernel32.dll')

            if os.path.exists(reference_file):
                for root, dirs, files in os.walk(install_path):
                    for file in files:
                        file_path = os.path.join(root, file)
                        common.forge_file_timestamp(file_path, reference_file)

            print("时间戳伪造完成")

        except Exception as e:
            print(f"伪造时间戳失败: {str(e)}")  # 非致命错误

    def _install_service(self, install_path: str, guardian_name: str):
        """安装Windows服务"""
        try:
            # 使用guardian_service.exe而不是guardian.exe
            service_exe_path = os.path.join(install_path, 'guardian_service.exe')

            if not os.path.exists(service_exe_path):
                raise Exception(f"找不到服务可执行文件: {service_exe_path}")

            # 生成服务名称
            service_name = common.generate_system_name("Service")

            # 1. 安装服务
            install_cmd = [service_exe_path, 'install']
            result = subprocess.run(install_cmd, capture_output=True, text=True)
            if result.returncode != 0:
                raise Exception(f"安装服务失败: {result.stderr}")

            print(f"Windows服务安装成功: {service_name}")

            # 2. 配置服务失败恢复选项
            self._configure_service_recovery(service_name)

            # 3. 设置服务伪装名称
            self._configure_service_display_name(service_name)

            # 4. 启动服务
            self._start_service(service_name)

            return service_name

        except Exception as e:
            raise Exception(f"安装Windows服务失败: {str(e)}")

    def _configure_service_recovery(self, service_name: str):
        """配置服务失败恢复选项"""
        try:
            # 使用sc命令配置服务在失败后60秒自动重启
            recovery_cmd = [
                'sc', 'failure', service_name,
                'reset=', '86400',  # 24小时后重置失败计数
                'actions=', 'restart/60000/restart/60000/restart/60000'  # 三次失败都在60秒后重启
            ]

            result = subprocess.run(recovery_cmd, capture_output=True, text=True)
            if result.returncode == 0:
                print(f"服务失败恢复配置成功: {service_name}")
            else:
                print(f"配置服务失败恢复失败: {result.stderr}")

        except Exception as e:
            print(f"配置服务失败恢复异常: {str(e)}")

    def _configure_service_display_name(self, service_name: str):
        """设置服务伪装名称"""
        try:
            # 设置伪装的显示名称和描述
            display_name = "Windows Security Enhancement Service"
            description = "Provides enhanced security monitoring and protection for the system"

            # 修改显示名称
            display_cmd = ['sc', 'config', service_name, 'DisplayName=', display_name]
            result = subprocess.run(display_cmd, capture_output=True, text=True)
            if result.returncode != 0:
                print(f"设置服务显示名称失败: {result.stderr}")

            # 修改描述
            desc_cmd = ['sc', 'description', service_name, description]
            result = subprocess.run(desc_cmd, capture_output=True, text=True)
            if result.returncode != 0:
                print(f"设置服务描述失败: {result.stderr}")
            else:
                print(f"服务伪装配置成功: {display_name}")

        except Exception as e:
            print(f"配置服务伪装名称异常: {str(e)}")

    def _start_service(self, service_name: str):
        """启动服务"""
        try:
            start_cmd = ['sc', 'start', service_name]
            result = subprocess.run(start_cmd, capture_output=True, text=True)
            if result.returncode == 0:
                print(f"服务启动成功: {service_name}")
            else:
                print(f"启动服务失败: {result.stderr}")

        except Exception as e:
            print(f"启动服务异常: {str(e)}")

    def _initialize_config(self, install_path: str, guardian_name: str, service_name: str):
        """初始化配置 - 服务版本"""
        try:
            # 创建初始配置
            config_data = {
                'version': '2.0',  # 服务版本
                'installation_path': install_path,
                'executable_names': {}, # 留空或移除此字段
                'service_name': service_name,
                'uninstall_window': {
                    'enabled': True,
                    'day_of_week': 0,  # 周一
                    'start_time': '02:00',
                    'end_time': '04:00'
                },
                'installed_at': datetime.now().isoformat()
            }

            # 保存配置
            if COMMON_AVAILABLE:
                common.save_config(config_data)
                print("配置初始化完成")
            else:
                print("警告: 无法保存配置，common模块不可用")

        except Exception as e:
            print(f"初始化配置失败: {str(e)}")  # 非致命错误

    def _create_launcher_config(self, install_path: str):
        """创建启动器配置文件（加密的path.dat）"""
        try:
            # 控制面板的完整路径
            control_panel_path = os.path.join(install_path, "control_panel.exe")

            # 创建启动器配置数据
            launcher_config = {
                'control_panel_path': control_panel_path,
                'created_at': datetime.now().isoformat(),
                'version': '1.0'
            }

            # 使用LAUNCHER_KEY加密配置
            if COMMON_AVAILABLE:
                from common.cryption import encrypt, LAUNCHER_KEY
                encrypted_data = encrypt(launcher_config, key=LAUNCHER_KEY)

                # 将加密数据保存到path.dat文件
                path_dat_file = os.path.join(install_path, "path.dat")
                with open(path_dat_file, 'wb') as f:
                    f.write(encrypted_data)

                print("启动器配置文件创建完成")
            else:
                print("警告: 无法创建启动器配置，common模块不可用")

        except Exception as e:
            print(f"创建启动器配置失败: {str(e)}")  # 非致命错误

    def _create_shortcuts(self, install_path: str):
        """创建桌面快捷方式"""
        try:
            # 创建启动器快捷方式（指向launcher.exe而不是control_panel.exe）
            desktop = os.path.join(os.path.expanduser("~"), "Desktop")
            shortcut_path = os.path.join(desktop, "自律守护者控制面板.lnk")

            launcher_path = os.path.join(install_path, "launcher.exe")
            control_panel_path = os.path.join(install_path, "control_panel.exe")

            # 使用PowerShell创建快捷方式
            ps_script = f'''
$WshShell = New-Object -comObject WScript.Shell
$Shortcut = $WshShell.CreateShortcut("{shortcut_path}")
$Shortcut.TargetPath = '"{launcher_path}"'
$Shortcut.WorkingDirectory = "{install_path}"
$Shortcut.IconLocation = '"{control_panel_path}",0'
$Shortcut.Description = "自律守护者控制面板"
$Shortcut.Save()
'''

            subprocess.run(['powershell', '-Command', ps_script],
                         capture_output=True, text=True, check=True)

            print("桌面快捷方式创建完成")

        except Exception as e:
            print(f"创建快捷方式失败: {str(e)}")  # 非致命错误

    def _self_destruct(self):
        """自删除"""
        try:
            # 创建自删除脚本
            script_content = f'''
@echo off
timeout /t 2 /nobreak >nul
del "{__file__}"
del "%~f0"
'''

            script_path = os.path.join(tempfile.gettempdir(), "cleanup.bat")
            with open(script_path, 'w') as f:
                f.write(script_content)

            # 启动自删除脚本
            subprocess.Popen([script_path], shell=True)

            print("自删除脚本已启动")

        except Exception as e:
            print(f"自删除失败: {str(e)}")  # 非致命错误

    def run(self):
        """运行安装程序"""
        try:
            self.root.mainloop()
        except KeyboardInterrupt:
            print("安装被中断")

def main():
    """主函数"""
    try:
        installer = SetupInstaller()
        installer.run()
    except Exception as e:
        print(f"安装程序启动失败: {str(e)}")
        if 'messagebox' in globals():
            messagebox.showerror("错误", f"安装程序启动失败: {str(e)}")

if __name__ == "__main__":
    main()