"""
通知管理器 - 自律守护者 v7.0 融合版
创建独立、健壮的自定义弹窗
实现拒绝后的交互与临时豁免功能
"""

import os
import sys
import subprocess
import json
import tempfile
from typing import Dict, Any, Optional, Callable
from .database_manager import DatabaseManager


class NotificationManager:
    """通知管理器，负责显示自定义通知窗口"""
    
    def __init__(self, database_manager: DatabaseManager = None):
        """初始化通知管理器"""
        self.database_manager = database_manager or DatabaseManager()
        self.notifier_script_path = self._create_notifier_script()
        print("通知管理器初始化完成")
    
    def _create_notifier_script(self) -> str:
        """创建通知器脚本文件"""
        script_content = '''
import tkinter as tk
from tkinter import ttk
import sys
import json
import threading
import time

class NotificationWindow:
    def __init__(self, title, message, buttons, callback_data=None):
        self.title = title
        self.message = message
        self.buttons = buttons
        self.callback_data = callback_data or {}
        self.result = None
        self.root = None
        
    def show(self):
        """显示通知窗口"""
        self.root = tk.Tk()
        self.root.title(self.title)
        self.root.geometry("400x200")
        self.root.resizable(False, False)
        
        # 设置窗口属性
        self.root.attributes("-topmost", True)
        self.root.attributes("-toolwindow", True)
        
        # 获取屏幕尺寸并定位到右下角
        screen_width = self.root.winfo_screenwidth()
        screen_height = self.root.winfo_screenheight()
        x = screen_width - 420
        y = screen_height - 250
        self.root.geometry(f"400x200+{x}+{y}")
        
        # 创建界面
        self._create_widgets()
        
        # 设置关闭事件
        self.root.protocol("WM_DELETE_WINDOW", self._on_close)
        
        # 显示窗口
        self.root.mainloop()
        
        return self.result
    
    def _create_widgets(self):
        """创建窗口组件"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 标题
        title_label = ttk.Label(main_frame, text=self.title, font=("Arial", 12, "bold"))
        title_label.grid(row=0, column=0, columnspan=2, pady=(0, 10), sticky=tk.W)
        
        # 消息
        message_label = ttk.Label(main_frame, text=self.message, wraplength=350)
        message_label.grid(row=1, column=0, columnspan=2, pady=(0, 20), sticky=(tk.W, tk.E))
        
        # 按钮框架
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E))
        
        # 创建按钮
        for i, button_config in enumerate(self.buttons):
            button = ttk.Button(
                button_frame,
                text=button_config["text"],
                command=lambda cfg=button_config: self._on_button_click(cfg)
            )
            button.grid(row=0, column=i, padx=(0, 10), sticky=tk.W)
        
        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
    
    def _on_button_click(self, button_config):
        """按钮点击事件"""
        self.result = button_config.get("action", "close")
        self.root.quit()
        self.root.destroy()
    
    def _on_close(self):
        """窗口关闭事件"""
        self.result = "close"
        self.root.quit()
        self.root.destroy()

def main():
    """主函数"""
    if len(sys.argv) < 2:
        print("用法: notifier.py <json_config>")
        sys.exit(1)
    
    try:
        config_json = sys.argv[1]
        config = json.loads(config_json)
        
        title = config.get("title", "通知")
        message = config.get("message", "")
        buttons = config.get("buttons", [{"text": "确定", "action": "ok"}])
        
        window = NotificationWindow(title, message, buttons)
        result = window.show()
        
        # 输出结果
        print(json.dumps({"result": result}))
        
    except Exception as e:
        print(json.dumps({"error": str(e)}))
        sys.exit(1)

if __name__ == "__main__":
    main()
'''
        
        # 创建临时脚本文件
        script_dir = os.path.join(tempfile.gettempdir(), 'guardian_notifier')
        os.makedirs(script_dir, exist_ok=True)
        script_path = os.path.join(script_dir, 'notifier.py')
        
        with open(script_path, 'w', encoding='utf-8') as f:
            f.write(script_content)
        
        return script_path
    
    def show_ai_rejection_notification(self, url: str, title: str) -> str:
        """
        显示AI拒绝通知
        
        Args:
            url: 被拒绝的URL
            title: 被拒绝的页面标题
            
        Returns:
            str: 用户的选择 ('force_allow', 'close')
        """
        notification_config = {
            "title": "内容已被拦截",
            "message": f"当前页面已被AI审核拒绝并关闭。\n\n页面标题: {title[:50]}...\nURL: {url[:50]}...",
            "buttons": [
                {"text": "强制通过", "action": "force_allow"},
                {"text": "确定", "action": "close"}
            ]
        }
        
        result = self._show_notification(notification_config)
        
        # 如果用户选择强制通过，添加到临时白名单
        if result == "force_allow":
            self.database_manager.add_to_temp_whitelist("url", url, 24)  # 24小时有效
            print(f"[通知] URL已添加到临时白名单: {url}")
        
        return result
    
    def show_time_limit_notification(self, category: str, app_name: str) -> str:
        """
        显示时间限制通知
        
        Args:
            category: 应用分类
            app_name: 应用名称
            
        Returns:
            str: 用户的选择
        """
        notification_config = {
            "title": "使用时间已达上限",
            "message": f"应用 {app_name} (分类: {category}) 的今日使用时间已达到设定上限，已被自动关闭。",
            "buttons": [
                {"text": "确定", "action": "close"}
            ]
        }
        
        return self._show_notification(notification_config)
    
    def show_classification_notification(self, app_name: str, category: str) -> str:
        """
        显示应用分类通知
        
        Args:
            app_name: 应用名称
            category: AI分类结果
            
        Returns:
            str: 用户的选择
        """
        notification_config = {
            "title": "应用已自动分类",
            "message": f"应用 {app_name} 已被AI自动分类为: {category}\n\n该分类的时间管理规则将自动生效。",
            "buttons": [
                {"text": "确定", "action": "close"}
            ]
        }
        
        return self._show_notification(notification_config)
    
    def _show_notification(self, config: Dict[str, Any]) -> str:
        """
        显示通知窗口
        
        Args:
            config: 通知配置
            
        Returns:
            str: 用户的选择结果
        """
        try:
            # 将配置转换为JSON字符串
            config_json = json.dumps(config, ensure_ascii=False)
            
            # 调用通知器脚本
            cmd = [sys.executable, self.notifier_script_path, config_json]
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
            
            if result.returncode == 0:
                # 解析结果
                try:
                    response = json.loads(result.stdout.strip())
                    return response.get("result", "close")
                except json.JSONDecodeError:
                    print(f"[通知] 解析通知结果失败: {result.stdout}")
                    return "close"
            else:
                print(f"[通知] 通知器执行失败: {result.stderr}")
                return "close"
                
        except subprocess.TimeoutExpired:
            print("[通知] 通知器执行超时")
            return "close"
        except Exception as e:
            print(f"[通知] 显示通知失败: {str(e)}")
            return "close"
    
    def cleanup(self):
        """清理资源"""
        try:
            if os.path.exists(self.notifier_script_path):
                os.remove(self.notifier_script_path)
                print("[通知] 通知器脚本已清理")
        except Exception as e:
            print(f"[通知] 清理通知器脚本失败: {str(e)}")


# 全局通知管理器实例
_notification_manager = None

def get_notification_manager(database_manager: DatabaseManager = None) -> NotificationManager:
    """获取全局通知管理器实例"""
    global _notification_manager
    if _notification_manager is None:
        _notification_manager = NotificationManager(database_manager)
    return _notification_manager

def show_ai_rejection_notification(url: str, title: str) -> str:
    """显示AI拒绝通知"""
    return get_notification_manager().show_ai_rejection_notification(url, title)

def show_time_limit_notification(category: str, app_name: str) -> str:
    """显示时间限制通知"""
    return get_notification_manager().show_time_limit_notification(category, app_name)

def show_classification_notification(app_name: str, category: str) -> str:
    """显示应用分类通知"""
    return get_notification_manager().show_classification_notification(app_name, category)
