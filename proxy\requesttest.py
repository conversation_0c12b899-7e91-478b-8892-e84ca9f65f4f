import os
import json
import tkinter as tk
from tkinter import scrolledtext, messagebox
import asyncio
import threading
import psutil
from playwright.async_api import async_playwright, <PERSON><PERSON>, <PERSON>rowser
from fake_useragent import UserAgent

# Define the cache file path relative to the script's directory
SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__)) # Use abspath for robustness
USER_AGENT_CACHE_FILE = os.path.join(SCRIPT_DIR, 'user_agent_cache.json')

# --- Memory Usage Monitoring ---
PROCESS = psutil.Process(os.getpid())

def log_memory_usage(stage=""):
    """Logs the current memory usage of the main process and its children (browser)."""
    try:
        main_process_memory = PROCESS.memory_info().rss
        children = PROCESS.children(recursive=True)
        children_memory = 0
        for child in children:
            try:
                children_memory += child.memory_info().rss
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                # Child process might have terminated, ignore it
                continue

        total_memory_mb = (main_process_memory + children_memory) / (1024 * 1024)
        main_process_memory_mb = main_process_memory / (1024 * 1024)
        
        print(f"[{stage}] GUI Process Memory: {main_process_memory_mb:.2f} MB | Total (GUI + Browser) Memory: {total_memory_mb:.2f} MB")
    except psutil.NoSuchProcess:
        print(f"[{stage}] Process not found, unable to log memory usage.")
# -----------------------------

class AsyncioThread(threading.Thread):
    """A thread that runs an asyncio event loop."""
    def __init__(self):
        super().__init__(daemon=True)
        self.loop = asyncio.new_event_loop()
        self.playwright: Playwright = None
        self.browser: Browser = None

    def run(self):
        """Runs the event loop and initializes Playwright."""
        asyncio.set_event_loop(self.loop)
        try:
            self.loop.run_until_complete(self.start_playwright())
            self.loop.run_forever()
        finally:
            # This part runs when loop.stop() is called
            print("Closing event loop...")
            tasks = asyncio.all_tasks(self.loop)
            for task in tasks:
                task.cancel()
            group = asyncio.gather(*tasks, return_exceptions=True)
            self.loop.run_until_complete(group)
            self.loop.run_until_complete(self.close_browser())
            self.loop.close()
            print("Event loop closed.")

    async def start_playwright(self):
        """Initializes Playwright and launches the browser."""
        print("Initializing Playwright in background thread...")
        try:
            self.playwright = await async_playwright().start()
            self.browser = await self.playwright.chromium.launch(headless=True)
            print("Playwright browser started successfully.")
            log_memory_usage("Browser Started")
        except Exception as e:
            print(f"Failed to start Playwright browser: {e}")
            # The GUI will handle the case where the browser is not available.

    def stop(self):
        """Stops the event loop and cleans up resources."""
        if not self.loop.is_running():
            return
        print("Stopping background event loop...")
        self.loop.call_soon_threadsafe(self.loop.stop)
        self.join(timeout=10) # Wait for the thread to finish
        print("Background thread stopped.")

    def submit_coro(self, coro):
        """Submits a coroutine to the event loop from another thread."""
        if not self.loop.is_running():
            raise RuntimeError("Event loop is not running.")
        return asyncio.run_coroutine_threadsafe(coro, self.loop)

    async def close_browser(self):
        """Closes the browser and Playwright instance."""
        if self.browser and self.browser.is_connected():
            print("Closing Playwright browser...")
            await self.browser.close()
            self.browser = None
        if self.playwright:
            print("Stopping Playwright...")
            await self.playwright.stop()
            self.playwright = None
        print("Playwright resources cleaned up.")

def get_user_agent():
    """
    Gets a random User-Agent and caches it.
    Reads from cache if available, otherwise generates a new one.
    """
    if os.path.exists(USER_AGENT_CACHE_FILE):
        try:
            with open(USER_AGENT_CACHE_FILE, 'r', encoding='utf-8') as f:
                cached_data = json.load(f)
                if 'user_agent' in cached_data:
                    return cached_data['user_agent']
        except (json.JSONDecodeError, IOError) as e:
            print(f"Error reading User-Agent cache ({e}), regenerating.")
    
    try:
        ua = UserAgent()
        user_agent = ua.random
        with open(USER_AGENT_CACHE_FILE, 'w', encoding='utf-8') as f:
            json.dump({'user_agent': user_agent}, f, indent=4)
        print(f"Generated and cached new User-Agent to: {USER_AGENT_CACHE_FILE}")
        return user_agent
    except Exception as e:
        print(f"Failed to generate User-Agent or write to cache: {e}")
        # Fallback user agent
        return "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"

async def async_scrape_website(browser: Browser, url: str):
    """
    Uses Playwright to asynchronously scrape the title and content of a URL.
    Ensures resources are cleaned up properly to manage memory.
    """
    if not browser or not browser.is_connected():
        return "Error: Browser not started or disconnected", "Cannot fetch content"

    context = None
    page = None
    result = None
    try:
        context = await browser.new_context(user_agent=get_user_agent())
        page = await context.new_page()

        print(f"Visiting with Playwright: {url}")
        await page.goto(url, wait_until="domcontentloaded", timeout=30000)

        title = await page.title() or "Title not found"

        main_content = []
        content_selectors = ['article p', 'main p', 'div[role="main"] p', 'p']
        for selector in content_selectors:
            elements = await page.query_selector_all(selector)
            for element in elements:
                text = await element.text_content()
                if text and len(text.strip()) > 50:
                    main_content.append(text.strip())
                    if len(" ".join(main_content)) > 500:
                        break
            if len(" ".join(main_content)) > 500:
                break

        if not main_content:
            body_text = await page.evaluate("() => document.body.innerText")
            main_content.append(body_text[:500].strip() + "...")

        content_str = " ".join(main_content)
        result = (title, content_str[:1000] + "..." if len(content_str) > 1000 else content_str)

    except Exception as e:
        print(f"Playwright scraping error: {e}")
        result = (f"Scraping error: {type(e).__name__}", "Cannot fetch content")

    finally:
        if page:
            try:
                # Navigate to a blank page to help release resources.
                # Use a short timeout as this is just for cleanup.
                await page.goto("about:blank", timeout=1000)
            except Exception:
                pass  # Ignore errors during cleanup navigation
            await page.close()
        if context:
            await context.close()

        # Wait a moment for the browser's garbage collection to kick in.
        # This happens after page/context are closed.
        await asyncio.sleep(2)
        log_memory_usage("Page Closed")

    return result

class WebScraperGUI:
    def __init__(self, master, asyncio_thread: AsyncioThread):
        self.master = master
        self.asyncio_thread = asyncio_thread
        master.title("Web Content Scraper")
        master.protocol("WM_DELETE_WINDOW", self.on_closing)

        # URL Input
        self.url_label = tk.Label(master, text="Enter URL:")
        self.url_label.grid(row=0, column=0, padx=5, pady=5, sticky="w")
        
        self.url_entry = tk.Entry(master, width=50)
        self.url_entry.grid(row=0, column=1, padx=5, pady=5, sticky="ew")
        
        self.scrape_button = tk.Button(master, text="Scrape", command=self.start_scrape)
        self.scrape_button.grid(row=0, column=2, padx=5, pady=5)

        # Title Output
        self.title_label = tk.Label(master, text="Title:")
        self.title_label.grid(row=1, column=0, padx=5, pady=5, sticky="w")
        
        self.title_text = scrolledtext.ScrolledText(master, wrap=tk.WORD, width=70, height=3, state=tk.DISABLED)
        self.title_text.grid(row=1, column=1, columnspan=2, padx=5, pady=5, sticky="ew")

        # Content Output
        self.content_label = tk.Label(master, text="Content:")
        self.content_label.grid(row=2, column=0, padx=5, pady=5, sticky="nw")
        
        self.content_text = scrolledtext.ScrolledText(master, wrap=tk.WORD, width=70, height=15, state=tk.DISABLED)
        self.content_text.grid(row=2, column=1, columnspan=2, padx=5, pady=5, sticky="nsew")

        master.grid_rowconfigure(2, weight=1)
        master.grid_columnconfigure(1, weight=1)

    def start_scrape(self):
        url = self.url_entry.get()
        if not url:
            messagebox.showwarning("Input Error", "Please enter a URL!")
            return
        
        if not url.startswith(('http://', 'https://')):
            url = 'https://' + url
        
        if not self.asyncio_thread.browser:
            messagebox.showerror("Browser Error", "Playwright browser is not running. Check console output.")
            return

        self.clear_results()
        self.scrape_button.config(state=tk.DISABLED)
        self.master.update_idletasks()

        log_memory_usage("Before Scrape")
        future = self.asyncio_thread.submit_coro(
            async_scrape_website(self.asyncio_thread.browser, url)
        )
        future.add_done_callback(self.on_scrape_complete)

    def on_scrape_complete(self, future):
        try:
            title, content = future.result()
            self.master.after(0, self.update_gui, title, content)
        except Exception as e:
            error_message = f"Error getting result from background thread: {e}"
            print(error_message)
            self.master.after(0, self.update_gui, "Error", error_message)

    def clear_results(self):
        for text_widget in [self.title_text, self.content_text]:
            text_widget.config(state=tk.NORMAL)
            text_widget.delete(1.0, tk.END)
            text_widget.config(state=tk.DISABLED)

    def update_gui(self, title, content):
        self.clear_results()
        for text_widget, text in [(self.title_text, title), (self.content_text, content)]:
            text_widget.config(state=tk.NORMAL)
            text_widget.insert(tk.END, text)
            text_widget.config(state=tk.DISABLED)
        self.scrape_button.config(state=tk.NORMAL)
        # The memory log is now inside the async function for better timing

    def on_closing(self):
        print("Closing application...")
        if self.asyncio_thread.is_alive():
            self.asyncio_thread.stop()
        log_memory_usage("Application Closing")
        self.master.destroy()

def main():
    log_memory_usage("Application Start")
    asyncio_thread = AsyncioThread()
    asyncio_thread.start()

    root = tk.Tk()
    app = WebScraperGUI(root, asyncio_thread)
    root.mainloop()

if __name__ == "__main__":
    main()