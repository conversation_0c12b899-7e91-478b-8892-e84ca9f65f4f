"""
强制卸载脚本 (仅供开发和测试使用)
此脚本会彻底移除自律守护者的所有组件。
必须以管理员权限运行！
"""

import os
import sys
import ctypes
import subprocess
import shutil
import time

# 确保能找到common模块
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'common'))

try:
    from common import config_handler, process_utils, system_utils
except ImportError as e:
    print(f"错误: 无法导入common模块，请确保脚本位于项目根目录: {e}")
    sys.exit(1)

def is_admin():
    """检查当前是否为管理员权限"""
    try:
        return ctypes.windll.shell32.IsUserAnAdmin() != 0
    except:
        return False

def run_command(command):
    """执行一个命令并打印输出"""
    try:
        print(f"执行: {' '.join(command)}")
        result = subprocess.run(command, capture_output=True, text=True, check=True, shell=True)
        if result.stdout:
            print(result.stdout)
        if result.stderr:
            print(f"错误输出: {result.stderr}")
        return True
    except subprocess.CalledProcessError as e:
        print(f"命令执行失败: {e}")
        print(f"输出: {e.stdout}")
        print(f"错误: {e.stderr}")
        return False
    except Exception as e:
        print(f"执行命令时发生未知错误: {e}")
        return False

def main():
    """主卸载逻辑 - V6.0 服务版本"""
    if not is_admin():
        print("错误: 此脚本需要管理员权限才能运行。")
        print("请右键点击脚本或在管理员终端中运行。")
        input("按回车键退出...")
        return

    print("="*50)
    print(" 自律守护者 V6.0 - 强制卸载工具 (仅供开发测试)")
    print("="*50)
    print("\n警告: 此操作将彻底从系统中移除程序，不可恢复！")

    confirm = input("确定要继续吗? (y/n): ")
    if confirm.lower() != 'y':
        print("操作已取消。")
        return

    try:
        # 1. 加载配置
        print("\n[步骤 1/6] 加载配置文件...")
        config = config_handler.load_config()
        install_path = config.get('installation_path')
        service_name = config.get('service_name')

        if not install_path or not service_name:
            print("错误: 找不到有效的配置文件或服务名称。可能程序未正确安装或已损坏。")
            # 即使配置不完整，也尝试清理
            if install_path and os.path.exists(install_path):
                 print(f"尝试清理残留目录: {install_path}")
                 shutil.rmtree(install_path, ignore_errors=True)
            return

        print(f"  - 安装路径: {install_path}")
        print(f"  - 服务名称: {service_name}")

        # 2. 停止并删除 Windows 服务
        print("\n[步骤 2/6] 停止并删除 Windows 服务...")
        print(f"  - 正在停止服务: {service_name}")
        run_command(['sc', 'stop', service_name])
        time.sleep(2) # 等待服务完全停止
        print(f"  - 正在删除服务: {service_name}")
        run_command(['sc', 'delete', service_name])

        # 3. 强制终止残留进程 (以防万一)
        print("\n[步骤 3/6] 终止所有守护进程...")
        process_utils.kill_process_by_name("guardian_service.exe")
        print("  - 进程清理完成。")

        # 4. 解锁并删除安装目录
        print("\n[步骤 4/6] 解锁并删除安装目录...")
        if os.path.exists(install_path):
            print(f"  - 正在重置目录权限: {install_path}")
            # 使用 /grant 赋予当前用户完全控制权，比 /reset 更可靠
            user = os.getlogin()
            run_command(['icacls', f'"{install_path}"', '/grant', f'{user}:(F)', '/t', '/c', '/q'])
            time.sleep(1)
            print(f"  - 正在删除目录: {install_path}")
            shutil.rmtree(install_path, ignore_errors=True)
        else:
            print("  - 安装目录不存在，跳过。")

        # 5. 删除配置文件
        print("\n[步骤 5/6] 删除配置文件...")
        config_file_path = config_handler.CONFIG_FILE_PATH
        if os.path.exists(config_file_path):
            os.remove(config_file_path)
            print(f"  - 已删除: {config_file_path}")

        # 6. 删除桌面快捷方式
        print("\n[步骤 6/6] 删除桌面快捷方式...")
        shortcut_path = os.path.join(os.path.expanduser("~"), "Desktop", "自律守护者控制面板.lnk")
        if os.path.exists(shortcut_path):
            os.remove(shortcut_path)
            print(f"  - 已删除: {shortcut_path}")

        print("\n卸载完成！所有组件已被移除。")

    except Exception as e:
        print(f"\n卸载过程中发生严重错误: {e}")
        print("可能部分文件未能完全清理，请手动检查。")

    input("按回车键退出...")

if __name__ == "__main__":
    main()