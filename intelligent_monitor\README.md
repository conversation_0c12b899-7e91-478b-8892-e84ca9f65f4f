# 智能监控系统 - 模块化重构版 (V8.0)

## 项目概述

这是一个智能浏览器监控系统，通过模块化重构实现了高内聚、低耦合的架构设计。系统能够实时监控浏览器窗口，检测违规内容并自动关闭相应标签页。

## 核心特性

- **多阶段生命周期管理**：智能学习和缓存浏览器操作策略
- **AI内容审核**：集成AI审核系统，智能识别违规内容
- **黑名单过滤**：支持标题和URL黑名单关键字过滤
- **自适应策略**：针对不同浏览器自动选择最优策略
- **模块化架构**：清晰的代码结构，易于维护和扩展

## 项目结构

```
intelligent_monitor/
├── main.py                 # 主程序入口
├── config.py               # 配置文件（黑名单、AI配置、生命周期参数等）
├── core/
│   └── uia_base.py         # UIA和WinAPI基础封装
└── modules/
    ├── browser_info_fetcher.py # 浏览器信息获取模块
    ├── tab_closer.py           # 标签页关闭模块
    ├── managers.py             # 生命周期和AI审核管理器
    └── blacklist_checker.py    # 黑名单检查模块
```

## 模块说明

### 1. config.py - 配置模块
- 集中管理所有可调参数和常量
- 包含黑名单关键字、AI配置、生命周期参数等
- 便于统一修改和维护

### 2. core/uia_base.py - 核心依赖模块
- 封装UIAComWrapper和WinAPI的初始化
- 提供基础的窗口操作函数
- 处理底层系统API调用

### 3. modules/managers.py - 管理器模块
- **StrategyLifecycleManager**: 管理浏览器策略的生命周期
- **AIAuditManager**: 管理AI审核的缓存和异步处理

### 4. modules/browser_info_fetcher.py - 信息获取模块
- 实现各种浏览器URL和标题获取策略
- 包含Firefox和Chromium系浏览器的专用策略
- 智能调度和缓存机制

### 5. modules/tab_closer.py - 标签页关闭模块
- 实现多种标签页关闭策略
- 支持按钮点击和键盘快捷键
- 智能匹配和缓存成功策略

### 6. modules/blacklist_checker.py - 黑名单检查模块
- 简单高效的关键字匹配
- 支持标题和URL检查
- 可配置的调试输出

## 使用方法

### 1. 环境要求
- Python 3.6+
- Windows操作系统
- UIAComWrapper.1.1.0.14 依赖包

### 2. 运行程序
```bash
cd intelligent_monitor
python main.py
```

### 3. 配置修改
编辑 `config.py` 文件来修改：
- 黑名单关键字
- AI审核配置
- 生命周期参数
- 调试开关

### 4. AI审核配置
在 `config.py` 中配置AI审核：
```python
AI_AUDIT_CONFIG = {
    "enabled": True,  # 开启/关闭AI审核
    "api_key": "your-api-key",  # 设置API密钥
    "api_endpoint": "your-api-endpoint",  # 设置API端点
    # ... 其他配置
}
```

## 架构优势

### 高内聚，低耦合
- 每个模块只关心自己的核心职责
- 模块间通过明确的接口交互
- 易于单独测试和维护

### 易于扩展
- 新增浏览器支持：在browser_info_fetcher.py中添加策略
- 新增关闭方式：在tab_closer.py中添加策略
- 新增审核方式：扩展managers.py中的AIAuditManager

### 可重用性
- 核心模块可在其他项目中复用
- 管理器类可独立使用
- 策略模式便于算法替换

## 调试和日志

系统提供了详细的调试输出，可通过config.py中的调试开关控制：
- `DEBUG_BLACKLIST`: 黑名单匹配调试
- `DEBUG_CACHE`: 缓存操作调试  
- `DEBUG_LIFECYCLE`: 生命周期管理调试

## 注意事项

1. 首次运行时系统会进入探索阶段，学习各浏览器的最优策略
2. AI审核功能需要有效的API密钥和网络连接
3. 某些操作需要管理员权限才能正常工作
4. 系统会自动创建缓存文件来保存学习到的策略

## 故障排除

如果遇到问题，请检查：
1. UIAComWrapper依赖是否正确安装
2. 是否以管理员权限运行
3. 网络连接是否正常（AI审核功能）
4. 调试日志中的错误信息
