"""
安全启动器 - 隐藏主程序路径的启动器
用于防止用户通过快捷方式找到主程序的安装位置并删除主程序
"""

import os
import sys
import subprocess
import tkinter as tk
from tkinter import messagebox
from pathlib import Path

# 添加项目根目录到路径
project_root = os.path.dirname(os.path.dirname(__file__))
sys.path.insert(0, project_root)

try:
    from common.cryption import decrypt, LAUNCHER_KEY
except ImportError as e:
    print(f"导入加密模块失败: {e}")
    sys.exit(1)

class SecureLauncher:
    def __init__(self):
        self.launcher_dir = os.path.dirname(os.path.abspath(__file__))
        self.path_dat_file = os.path.join(self.launcher_dir, "path.dat")
        
    def load_main_program_path(self) -> str:
        """
        从加密的path.dat文件中加载主程序路径
        
        Returns:
            str: 主程序的完整路径
            
        Raises:
            Exception: 如果文件不存在或解密失败
        """
        try:
            if not os.path.exists(self.path_dat_file):
                raise FileNotFoundError(f"路径配置文件不存在: {self.path_dat_file}")
            
            # 读取加密的路径数据
            with open(self.path_dat_file, 'rb') as f:
                encrypted_data = f.read()
            
            # 使用LAUNCHER_KEY解密
            decrypted_data = decrypt(encrypted_data, key=LAUNCHER_KEY)
            
            # 获取主程序路径
            main_program_path = decrypted_data.get('control_panel_path', '')
            
            if not main_program_path:
                raise ValueError("路径配置文件中未找到主程序路径")
            
            if not os.path.exists(main_program_path):
                raise FileNotFoundError(f"主程序文件不存在: {main_program_path}")
            
            return main_program_path
            
        except Exception as e:
            raise Exception(f"加载主程序路径失败: {str(e)}")
    
    def launch_main_program(self):
        """启动主程序"""
        try:
            # 获取主程序路径
            main_program_path = self.load_main_program_path()
            
            print(f"正在启动主程序: {os.path.basename(main_program_path)}")
            
            # 启动主程序
            subprocess.Popen([main_program_path], 
                           cwd=os.path.dirname(main_program_path),
                           creationflags=subprocess.CREATE_NEW_CONSOLE if os.name == 'nt' else 0)
            
            print("主程序启动成功")
            
        except Exception as e:
            error_msg = f"启动主程序失败: {str(e)}"
            print(error_msg)
            
            # 显示错误对话框
            try:
                root = tk.Tk()
                root.withdraw()  # 隐藏主窗口
                messagebox.showerror("启动失败", error_msg)
                root.destroy()
            except:
                # 如果GUI不可用，只打印错误
                pass
            
            sys.exit(1)
    
    def run(self):
        """运行启动器"""
        try:
            print("自律守护者 - 安全启动器")
            print("正在验证程序完整性...")
            
            # 启动主程序
            self.launch_main_program()
            
        except KeyboardInterrupt:
            print("启动器被用户中断")
            sys.exit(0)
        except Exception as e:
            print(f"启动器运行失败: {str(e)}")
            sys.exit(1)

def main():
    """主函数"""
    launcher = SecureLauncher()
    launcher.run()

if __name__ == "__main__":
    main()
