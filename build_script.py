"""
Build Script - 自动化打包脚本
用于将所有Python组件打包为exe文件
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

class ProjectBuilder:
    def __init__(self):
        self.project_root = Path(__file__).parent
        self.dist_dir = self.project_root / "dist"
        self.build_dir = self.project_root / "build"
        
        # 检查PyInstaller
        self._check_pyinstaller()
    
    def _check_pyinstaller(self):
        """检查PyInstaller是否安装"""
        try:
            subprocess.run([sys.executable, '-m', 'PyInstaller', '--version'],
                         capture_output=True, check=True)
            print("PyInstaller已安装")
        except (subprocess.CalledProcessError, FileNotFoundError):
            print("PyInstaller未安装，正在安装...")
            try:
                subprocess.run([sys.executable, '-m', 'pip', 'install', 'pyinstaller'],
                             check=True)
                print("PyInstaller安装成功")
            except subprocess.CalledProcessError as e:
                print(f"安装PyInstaller失败: {e}")
                sys.exit(1)
    
    def clean_build(self):
        """清理构建目录"""
        print("清理构建目录...")
        
        if self.dist_dir.exists():
            shutil.rmtree(self.dist_dir)
        
        if self.build_dir.exists():
            shutil.rmtree(self.build_dir)
        
        # 清理spec文件
        for spec_file in self.project_root.glob("*.spec"):
            spec_file.unlink()
        
        print("清理完成")
    
    def build_component(self, script_path: Path, output_name: str = None, 
                       additional_args: list = None):
        """构建单个组件"""
        if not script_path.exists():
            print(f"警告: 脚本文件不存在: {script_path}")
            return False
        
        print(f"构建组件: {script_path}")
        
        # 构建PyInstaller命令
        cmd = [
            sys.executable, '-m', 'PyInstaller',
            '--onefile',  # 打包为单个exe文件
            '--noconsole',  # 不显示控制台窗口（GUI程序）
            '--clean',  # 清理临时文件
            '--debug=all', # 输出详细的调试信息
        ]
        
        # 添加额外参数
        if additional_args:
            cmd.extend(additional_args)
        
        # 设置输出名称
        if output_name:
            cmd.extend(['--name', output_name])
        
        # 添加脚本路径
        cmd.append(str(script_path))
        
        try:
            # 执行构建
            result = subprocess.run(cmd, cwd=self.project_root, 
                                  capture_output=True, text=True)
            
            if result.returncode == 0:
                print(f"构建成功: {script_path.name}")
                return True
            else:
                print(f"构建失败: {script_path.name}")
                print(f"错误输出: {result.stderr}")
                return False
                
        except Exception as e:
            print(f"构建异常: {e}")
            return False
    
    def build_backend(self):
        """构建后台组件"""
        print("\n=== 构建后台组件 ===")

        backend_dir = self.project_root / "backend"
        success_count = 0

        # 构建guardian_service.exe (新的Windows服务版本)
        guardian_service_py = backend_dir / "guardian_service.py"
        if self.build_component(guardian_service_py, "guardian_service", ["--noconsole"]):
            success_count += 1

        print(f"后台组件构建完成: {success_count}/1")
        return success_count == 1
    
    def build_frontend(self):
        """构建前端组件"""
        print("\n=== 构建前端组件 ===")

        frontend_dir = self.project_root / "frontend"
        success_count = 0

        # 构建web_control_panel.exe (现代Web界面)
        web_control_panel_py = frontend_dir / "web_control_panel.py"
        # 注意: 蓝图要求安装后叫 control_panel.exe，所以这里直接命名
        if self.build_component(web_control_panel_py, "control_panel", ["--add-data", f"{frontend_dir}/templates;templates", "--add-data", f"{frontend_dir}/static;static"]):
            success_count += 1

        # 构建launcher.exe
        launcher_py = frontend_dir / "launcher.py"
        if self.build_component(launcher_py, "launcher", ["--noconsole"]):
            success_count += 1

        print(f"前端组件构建完成: {success_count}/2")
        return success_count == 2
    
    def build_installer(self):
        """构建安装程序"""
        print("\n=== 构建安装程序 ===")
        
        installer_dir = self.project_root / "installer"
        success_count = 0
        
        # 构建setup.exe
        setup_py = installer_dir / "setup.py"
        if self.build_component(setup_py, "setup"):
            success_count += 1
        
        print(f"安装程序构建完成: {success_count}/1")
        return success_count == 1
    
    def organize_output(self):
        """整理输出文件"""
        print("\n=== 整理输出文件 ===")
        
        if not self.dist_dir.exists():
            print("dist目录不存在")
            return False
        
        try:
            # 创建最终输出目录
            final_dir = self.project_root / "Project-IronContract"
            if final_dir.exists():
                shutil.rmtree(final_dir)
            
            # 创建子目录
            backend_final = final_dir / "backend"
            frontend_final = final_dir / "frontend"
            installer_final = final_dir / "installer"
            
            backend_final.mkdir(parents=True)
            frontend_final.mkdir(parents=True)
            installer_final.mkdir(parents=True)
            
            # 移动文件
            exe_files = list(self.dist_dir.glob("*.exe"))
            
            for exe_file in exe_files:
                if exe_file.name == "guardian_service.exe":
                    shutil.move(str(exe_file), str(backend_final / exe_file.name))
                elif exe_file.name in ["control_panel.exe", "launcher.exe"]:
                    shutil.move(str(exe_file), str(frontend_final / exe_file.name))
                elif exe_file.name == "setup.exe":
                    shutil.move(str(exe_file), str(installer_final / exe_file.name))
            
            # 复制必要的文件
            # 复制blacklist.txt模板
            blacklist_template = final_dir / "blacklist.txt"
            blacklist_template.write_text("# 黑名单规则文件\n# 格式: 进程名.exe 或 进程名.exe,开始时间,结束时间\n# 示例:\n# notepad.exe\n# chrome.exe,09:00,18:00\n")
            
            # 复制README
            readme_file = final_dir / "README.txt"
            readme_content = """
自律守护者 V6.0 (钢铁契约)

安装说明:
1. 以管理员权限运行 installer/setup.exe
2. 输入安装凭证
3. 等待安装完成

使用说明:
1. 运行控制面板管理黑名单规则
2. 设置窗口期用于暂停保护
3. 系统将在后台自动运行

注意事项:
- 需要管理员权限
- 需要网络连接进行时间同步
- 请妥善保管安装凭证
"""
            readme_file.write_text(readme_content.strip())
            
            print(f"文件整理完成，输出目录: {final_dir}")
            return True
            
        except Exception as e:
            print(f"整理文件失败: {e}")
            return False
    
    def create_requirements(self):
        """创建requirements.txt"""
        print("\n=== 创建依赖文件 ===")
        
        requirements = [
            "cryptography>=3.4.8",
            "psutil>=5.8.0",
            "pywin32>=301",
            "ntplib>=0.3.4"
        ]
        
        req_file = self.project_root / "requirements.txt"
        req_file.write_text("\n".join(requirements))
        
        print("requirements.txt已创建")
    
    def build_all(self):
        """构建所有组件"""
        print("开始构建自律守护者项目...")
        
        # 清理
        self.clean_build()
        
        # 创建依赖文件
        self.create_requirements()
        
        # 构建各组件
        backend_success = self.build_backend()
        frontend_success = self.build_frontend()
        installer_success = self.build_installer()
        
        # 整理输出
        organize_success = self.organize_output()
        
        # 清理临时文件
        if self.build_dir.exists():
            shutil.rmtree(self.build_dir)
        
        # 总结
        print("\n=== 构建总结 ===")
        print(f"后台组件: {'成功' if backend_success else '失败'}")
        print(f"前端组件: {'成功' if frontend_success else '失败'}")
        print(f"安装程序: {'成功' if installer_success else '失败'}")
        print(f"文件整理: {'成功' if organize_success else '失败'}")
        
        if all([backend_success, frontend_success, installer_success, organize_success]):
            print("\n✅ 项目构建完成！")
            print("输出目录: Project-IronContract/")
        else:
            print("\n❌ 构建过程中出现错误")
            return False
        
        return True

def main():
    """主函数"""
    builder = ProjectBuilder()
    
    if len(sys.argv) > 1:
        command = sys.argv[1].lower()
        
        if command == "clean":
            builder.clean_build()
        elif command == "backend":
            builder.build_backend()
        elif command == "frontend":
            builder.build_frontend()
        elif command == "installer":
            builder.build_installer()
        elif command == "organize":
            builder.organize_output()
        else:
            print(f"未知命令: {command}")
            print("可用命令: clean, backend, frontend, installer, organize")
    else:
        # 构建所有
        builder.build_all()

if __name__ == "__main__":
    main()
