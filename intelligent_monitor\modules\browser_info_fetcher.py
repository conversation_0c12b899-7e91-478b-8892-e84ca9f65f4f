# intelligent_monitor/modules/browser_info_fetcher.py
# 中文说明：
# 本模块用于浏览器URL和标题的获取，包含多种策略函数和调度逻辑。
# 通过生命周期管理器自动选择最优策略，支持Chromium内核浏览器。
# 供主监控循环调用。

import sys
import os

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 导入核心依赖和配置
import config
from core import uia_base as uia


# --- 辅助函数 ---
def _is_valid_url(url_text):
    """辅助函数：判断字符串是否为有效URL。"""
    if not url_text or len(url_text) < 5: return False
    url_text = url_text.strip().lower()
    return url_text.startswith(('http:', 'https:', 'ftp:', 'file:')) or \
        ('.com' in url_text or '.cn' in url_text or '.org' in url_text or 'about:' in url_text)


def _extract_url_from_element(element):
    """辅助函数：尝试从UIA元素中提取URL。"""
    if not element: return None
    try:
        value_pattern = element.GetCurrentPattern(uia.ValuePattern.Pattern)
        if value_pattern and _is_valid_url(value_pattern.Current.Value):
            return value_pattern.Current.Value.strip()
        legacy_pattern = element.GetCurrentPattern(uia.LegacyIAccessiblePattern.Pattern)
        if legacy_pattern and _is_valid_url(legacy_pattern.Current.Value):
            return legacy_pattern.Current.Value.strip()
        text_pattern = element.GetCurrentPattern(uia.TextPattern.Pattern)
        if text_pattern:
            url = text_pattern.DocumentRange.GetText(-1)
            if _is_valid_url(url): return url.strip()
    except Exception:
        pass
    return None


# --- URL 获取策略 ---
# 已移除 _get_url_firefox_robust 策略

def _get_url_chromium_generic(window_element):
    """Chromium通用策略：遍历控件尝试提取URL和标题，适用于大多数Chromium内核浏览器。"""
    try:
        # 首先尝试从 Edit 或 ComboBox 控件获取URL（这是最常见和高效的方式）
        or_cond = uia.OrCondition(
            uia.PropertyCondition(uia.AutomationElement.ControlTypeProperty, uia.ControlType.Edit),
            uia.PropertyCondition(uia.AutomationElement.ControlTypeProperty, uia.ControlType.ComboBox)
        )
        for elem in list(window_element.FindAll(uia.TreeScope.Descendants, or_cond)):
            url = _extract_url_from_element(elem)
            if url:
                # 找到URL后，使用窗口的整体标题作为页面标题
                return url, window_element.Current.Name
    except Exception:
        pass  # 容忍查找过程中的任何异常

    try:
        # 如果上述方法失败，尝试从 Document 控件获取标题作为备用方案（此时无法获取URL）
        doc_cond = uia.PropertyCondition(uia.AutomationElement.ControlTypeProperty, uia.ControlType.Document)
        doc_element = window_element.FindFirst(uia.TreeScope.Descendants, doc_cond)
        if doc_element and doc_element.Current.Name:
            return None, doc_element.Current.Name
    except Exception:
        pass  # 容忍查找过程中的任何异常

    # 如果所有方法都失败，返回窗口标题作为最后的备用
    return None, window_element.Current.Name


# URL策略库现在只包含Chromium通用策略
URL_STRATEGIES = {
    'get_url_chromium_generic': _get_url_chromium_generic,
}


# --- 核心调度逻辑 ---
def get_browser_info_with_lifecycle(hwnd, process_name, lifecycle_manager):
    """主调度函数：根据生命周期管理器状态，自动选择并执行最优URL/标题获取策略。返回(url, title, 策略描述, 窗口元素, 状态信息)。"""
    if not uia.UIA_LOADED:
        return None, None, "UIA未加载", None, {}
    try:
        window_element = uia.AutomationElement.FromHandle(uia.IntPtr(hwnd))
        if not window_element:
            return None, None, "窗口元素无效", None, {}

        status_info = lifecycle_manager.get_status_info(process_name)
        profile = lifecycle_manager.get_browser_profile(process_name)

        # 策略列表现在只包含通用Chromium策略
        strategies_to_try = [('get_url_chromium_generic', _get_url_chromium_generic)]

        should_full_search = lifecycle_manager.should_perform_full_search(process_name)
        cached_strategy = lifecycle_manager.get_cached_strategy(process_name)

        if config.DEBUG_LIFECYCLE:
            print(
                f"  [调度] {process_name}: 阶段={status_info['phase']}, 全量搜索={should_full_search}, 缓存策略={cached_strategy}")

        if profile['phase'] == config.StrategyPhase.FAILED and not should_full_search:
            if config.DEBUG_LIFECYCLE: print(f"  [调度] {process_name} 处于失效状态，但全局搜索冷却中，本次跳过。")
            return None, window_element.Current.Name, "失效等待冷却", window_element, status_info

        if should_full_search or not cached_strategy:
            return _perform_full_strategy_search(window_element, process_name, strategies_to_try, lifecycle_manager)
        else:
            return _perform_cached_strategy(window_element, process_name, cached_strategy, strategies_to_try,
                                            lifecycle_manager)

    except Exception as e:
        lifecycle_manager.record_strategy_result(process_name, "exception", None, None)
        status_info = lifecycle_manager.get_status_info(process_name)
        return None, None, f"分析异常 {e}", None, status_info


def _perform_full_strategy_search(window_element, process_name, strategies_to_try, manager):
    """全量搜索：遍历所有策略，寻找高质量或次优解，并驱动生命周期状态转变。"""
    if config.DEBUG_LIFECYCLE: print(f"  [调度-全量搜索] {process_name} 开始...")

    profile = manager.get_browser_profile(process_name)
    if profile['phase'] == config.StrategyPhase.FAILED:
        manager._reset_to_exploration(process_name)

    best_result_for_low_quality = None
    for strategy_name, strategy_func in strategies_to_try:
        try:
            url, title = strategy_func(window_element)
            manager.record_strategy_result(process_name, strategy_name, url, title)
            profile = manager.get_browser_profile(process_name)
            if profile['phase'] == config.StrategyPhase.STABLE:
                status_info = manager.get_status_info(process_name)
                return url, title, f"策略({strategy_name})-成功", window_element, status_info
            if title and not url:
                if not best_result_for_low_quality:
                    best_result_for_low_quality = (url, title, f"策略({strategy_name})-低质量", window_element)
        except Exception as e:
            if config.DEBUG_LIFECYCLE: print(f"    策略 {strategy_name} 执行异常: {e}")
            manager.record_strategy_result(process_name, strategy_name, None, None)

    status_info = manager.get_status_info(process_name)
    if best_result_for_low_quality:
        return best_result_for_low_quality + (status_info,)
    try:
        # 如果全量搜索后连标题都拿不到，返回原始窗口标题
        return None, window_element.Current.Name, "全量搜索无可用结果", window_element, status_info
    except:
        return None, None, "全量搜索无可用结果", None, status_info


def _perform_cached_strategy(window_element, process_name, cached_strategy, strategies_to_try, manager):
    """执行已缓存的策略：根据生命周期管理器缓存，仅尝试单一高效策略。"""
    if config.DEBUG_LIFECYCLE: print(f"  [调度-缓存] {process_name} 执行缓存策略: {cached_strategy}")
    strategy_func = dict(strategies_to_try).get(cached_strategy)
    if not strategy_func:
        if config.DEBUG_LIFECYCLE: print(f"  [错误] 缓存策略 {cached_strategy} 未找到，重置到探索阶段")
        manager._reset_to_exploration(process_name)
        status_info = manager.get_status_info(process_name)
        return None, None, "缓存策略无效", None, status_info
    try:
        url, title = strategy_func(window_element)
        manager.record_strategy_result(process_name, cached_strategy, url, title)
        profile = manager.get_browser_profile(process_name)
        status_info = manager.get_status_info(process_name)
        if profile['phase'] == config.StrategyPhase.FAILED:
            result_desc = f"策略({cached_strategy})-已损坏"
        elif profile['phase'] == config.StrategyPhase.ABANDONED:
            result_desc = f"策略({cached_strategy})-放弃(次优解)"
        elif url:
            result_desc = f"策略({cached_strategy})-成功"
        else:
            result_desc = f"策略({cached_strategy})-软失效"
        return url, title, result_desc, window_element, status_info
    except Exception as e:
        if config.DEBUG_LIFECYCLE: print(f"  [错误] 缓存策略执行异常: {e}")
        manager.record_strategy_result(process_name, cached_strategy, None, None)
        status_info = manager.get_status_info(process_name)
        return None, None, f"策略({cached_strategy})-异常", None, status_info