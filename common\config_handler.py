"""
配置处理器 - 作为读写加密配置文件的唯一入口
确保操作的原子性和安全性
黑名单规则现在完全存储在加密配置文件中，不再使用独立的blacklist.txt文件
"""

import os
import tempfile
import shutil
from typing import Dict, Any, List
from . import cryption

# 配置文件路径 - 在安装时会被修改为实际的隐藏路径
# 开发环境使用用户目录，生产环境使用系统目录
if os.path.exists(os.path.join(os.path.dirname(__file__), '..', 'test_system.py')):
    # 开发环境
    CONFIG_DIR = os.path.join(os.path.expanduser('~'), '.iron_contract')
    os.makedirs(CONFIG_DIR, exist_ok=True)
    CONFIG_FILE_PATH = os.path.join(CONFIG_DIR, 'config.dat')
    # 保留旧的blacklist.txt路径用于迁移
    LEGACY_BLACKLIST_FILE_PATH = os.path.join(CONFIG_DIR, 'blacklist.txt')
else:
    # 生产环境
    CONFIG_FILE_PATH = os.path.join(os.environ.get('WINDIR', 'C:\\Windows'), 'SysWOW64', 'config.dat')
    # 保留旧的blacklist.txt路径用于迁移
    LEGACY_BLACKLIST_FILE_PATH = os.path.join(os.path.dirname(CONFIG_FILE_PATH), 'blacklist.txt')

class ConfigHandler:
    def __init__(self):
        self.config_path = CONFIG_FILE_PATH
        self.legacy_blacklist_path = LEGACY_BLACKLIST_FILE_PATH

    def _migrate_legacy_blacklist(self) -> List[str]:
        """
        迁移旧的blacklist.txt文件到配置中

        Returns:
            List[str]: 迁移的黑名单规则列表
        """
        rules = []
        try:
            if os.path.exists(self.legacy_blacklist_path):
                print(f"发现旧的黑名单文件，正在迁移: {self.legacy_blacklist_path}")
                with open(self.legacy_blacklist_path, 'r', encoding='utf-8') as f:
                    for line in f.readlines():
                        line = line.strip()
                        if line and not line.startswith('#'):
                            rules.append(line)

                # 迁移完成后删除旧文件
                try:
                    os.remove(self.legacy_blacklist_path)
                    print(f"已删除旧的黑名单文件: {self.legacy_blacklist_path}")
                except Exception as e:
                    print(f"删除旧黑名单文件失败: {str(e)}")

        except Exception as e:
            print(f"迁移黑名单文件失败: {str(e)}")

        return rules
    
    def load_config(self) -> Dict[str, Any]:
        """
        从加密配置文件加载配置

        Returns:
            dict: 配置字典
        """
        try:
            if not os.path.exists(self.config_path):
                # 如果配置文件不存在，检查是否需要迁移旧的黑名单文件
                config = self._get_default_config()
                legacy_rules = self._migrate_legacy_blacklist()
                if legacy_rules:
                    config['blacklist_rules'] = legacy_rules
                    # 保存迁移后的配置
                    self.save_config(config)
                return config

            with open(self.config_path, 'rb') as f:
                encrypted_data = f.read()

            if not encrypted_data:
                config = self._get_default_config()
                legacy_rules = self._migrate_legacy_blacklist()
                if legacy_rules:
                    config['blacklist_rules'] = legacy_rules
                    self.save_config(config)
                return config

            config_data = cryption.decrypt(encrypted_data)

            # 检查是否需要迁移黑名单规则
            if 'blacklist_rules' not in config_data:
                legacy_rules = self._migrate_legacy_blacklist()
                config_data['blacklist_rules'] = legacy_rules
                # 保存更新后的配置
                self.save_config(config_data)

            return config_data

        except Exception as e:
            # 如果解密失败，返回默认配置
            print(f"加载配置失败，使用默认配置: {str(e)}")
            config = self._get_default_config()
            # 尝试迁移旧的黑名单文件
            legacy_rules = self._migrate_legacy_blacklist()
            if legacy_rules:
                config['blacklist_rules'] = legacy_rules
            return config
    
    def save_config(self, config_data: Dict[str, Any]) -> None:
        """
        保存配置到加密文件

        Args:
            config_data: 要保存的配置字典
        """
        try:
            # 确保黑名单规则字段存在
            if 'blacklist_rules' not in config_data:
                config_data['blacklist_rules'] = []

            # 加密配置数据
            encrypted_data = cryption.encrypt(config_data)

            # 原子性写入：先写入临时文件，再重命名
            temp_path = self.config_path + '.tmp'
            with open(temp_path, 'wb') as f:
                f.write(encrypted_data)

            # 原子性重命名
            shutil.move(temp_path, self.config_path)

        except Exception as e:
            # 清理临时文件
            temp_path = self.config_path + '.tmp'
            if os.path.exists(temp_path):
                try:
                    os.remove(temp_path)
                except:
                    pass
            raise Exception(f"保存配置失败: {str(e)}")
    
    def _get_default_config(self) -> Dict[str, Any]:
        """
        获取默认配置 - 服务版本

        Returns:
            dict: 默认配置字典
        """
        return {
            'version': '2.0',  # 服务版本
            'uninstall_window': {
                'enabled': True,
                'day_of_week': 0,  # 0=周一, 6=周日
                'start_time': '02:00',
                'end_time': '04:00'
            },
            'blacklist_rules': [],  # 黑名单规则现在存储在配置中
            'installation_path': '',
            # 程序可执行文件名（保留用于兼容性）
            'executable_names': {
                'guardian': ''
                # 移除watchdog相关字段
            },
            # Windows服务名（用于服务管理）
            'service_name': ''
            # 移除task_names字段，因为不再使用计划任务
        }
    
    def get_blacklist_rules(self) -> List[str]:
        """
        获取黑名单规则列表

        Returns:
            List[str]: 黑名单规则列表
        """
        try:
            config = self.load_config()
            return config.get('blacklist_rules', [])
        except Exception as e:
            print(f"获取黑名单规则失败: {str(e)}")
            return []

    def save_blacklist_rules(self, rules: List[str]) -> None:
        """
        保存黑名单规则列表

        Args:
            rules: 黑名单规则列表
        """
        try:
            config = self.load_config()
            config['blacklist_rules'] = rules
            self.save_config(config)
        except Exception as e:
            raise Exception(f"保存黑名单规则失败: {str(e)}")

    def add_blacklist_rule(self, rule: str) -> None:
        """
        添加黑名单规则

        Args:
            rule: 要添加的规则
        """
        if not rule.strip():
            raise ValueError("规则不能为空")

        rules = self.get_blacklist_rules()
        if rule in rules:
            raise ValueError("规则已存在")

        rules.append(rule)
        self.save_blacklist_rules(rules)

    def remove_blacklist_rule(self, rule: str) -> None:
        """
        删除黑名单规则

        Args:
            rule: 要删除的规则
        """
        rules = self.get_blacklist_rules()
        if rule not in rules:
            raise ValueError("规则不存在")

        rules.remove(rule)
        self.save_blacklist_rules(rules)

    def verify_blacklist_integrity(self) -> bool:
        """
        验证黑名单完整性（由于现在存储在加密配置中，始终返回True）

        Returns:
            bool: 始终返回True，保持向后兼容
        """
        return True

# 全局配置处理器实例
_config_handler = ConfigHandler()

def load_config() -> Dict[str, Any]:
    """加载配置"""
    return _config_handler.load_config()

def save_config(config_data: Dict[str, Any]) -> None:
    """保存配置"""
    _config_handler.save_config(config_data)

def get_blacklist_rules() -> List[str]:
    """获取黑名单规则"""
    return _config_handler.get_blacklist_rules()

def save_blacklist_rules(rules: List[str]) -> None:
    """保存黑名单规则"""
    _config_handler.save_blacklist_rules(rules)

def add_blacklist_rule(rule: str) -> None:
    """添加黑名单规则"""
    _config_handler.add_blacklist_rule(rule)

def remove_blacklist_rule(rule: str) -> None:
    """删除黑名单规则"""
    _config_handler.remove_blacklist_rule(rule)

def verify_blacklist_integrity() -> bool:
    """验证黑名单完整性"""
    return _config_handler.verify_blacklist_integrity()
