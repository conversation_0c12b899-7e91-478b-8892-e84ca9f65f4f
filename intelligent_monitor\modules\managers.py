# intelligent_monitor/modules/managers.py
# 中文说明：
# 本模块包含核心管理器类，负责浏览器策略生命周期管理和AI内容审核。
# StrategyLifecycleManager 管理每个浏览器的策略状态和缓存。
# AIAuditManager 管理AI审核缓存和异步请求。

import os
import json
import threading
import random
import time
from datetime import datetime, timedelta
import sys

# 添加父目录到路径以便导入config
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
import config

class StrategyLifecycleManager:
    """
    多阶段生命周期管理器:
    统一管理URL获取策略和标签页关闭策略的缓存。
    所有策略信息都持久化到单一JSON文件。
    """
    def __init__(self, cache_file_path=config.CACHE_FILE_PATH):
        """初始化生命周期管理器，加载或创建浏览器档案缓存。"""
        self.cache_file_path = cache_file_path
        self.browser_profiles = {}
        self.last_full_search_time = None
        self.load_cache()

    def load_cache(self):
        """从文件加载浏览器策略缓存。"""
        try:
            if os.path.exists(self.cache_file_path):
                with open(self.cache_file_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    self.browser_profiles = data.get('browser_profiles', {})
                    if config.DEBUG_CACHE:
                        print(f"[缓存] 已从 {self.cache_file_path} 加载 {len(self.browser_profiles)} 个浏览器档案")
                        for process_name, profile in self.browser_profiles.items():
                            url_strat = profile.get('strategy_name', 'N/A')
                            close_strat = profile.get('close_strategy', {}).get('name', 'N/A')
                            print(f"  -> {process_name}: URL策略='{url_strat}', 关闭策略='{close_strat}'")
        except Exception as e:
            if config.DEBUG_CACHE: print(f"[缓存] 加载缓存失败: {e}")
            self.browser_profiles = {}

    def save_cache(self):
        """保存浏览器策略缓存到文件。"""
        try:
            data = {'browser_profiles': self.browser_profiles, 'last_updated': datetime.now().isoformat()}
            with open(self.cache_file_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            if config.DEBUG_CACHE: print(f"[缓存] 已保存档案到 {self.cache_file_path}")
        except Exception as e:
            if config.DEBUG_CACHE: print(f"[缓存] 保存缓存失败: {e}")

    def get_browser_profile(self, process_name):
        """获取或创建指定浏览器进程的档案(profile)。"""
        if process_name not in self.browser_profiles:
            self.browser_profiles[process_name] = {
                'phase': config.StrategyPhase.EXPLORATION,
                'exploration_attempts': 0,
                'hard_failure_count': 0,
                'strategy_name': None,
                'strategy_quality': config.StrategyQuality.NONE,
                'close_strategy': None,
                'last_exploration_title': None,
                'best_low_quality_info': None,
                'created_at': datetime.now().isoformat(),
                'last_updated': datetime.now().isoformat()
            }
            if config.DEBUG_LIFECYCLE: print(f"[生命周期] 为 {process_name} 创建新档案，进入探索阶段")
        return self.browser_profiles[process_name]

    def should_perform_full_search(self, process_name):
        """判断是否需要进行全量策略搜索。"""
        profile = self.get_browser_profile(process_name)
        if profile['phase'] in [config.StrategyPhase.EXPLORATION, config.StrategyPhase.FAILED]:
            return self._check_global_search_interval()
        return False

    def _check_global_search_interval(self):
        """检查全局全量搜索冷却时间。"""
        now = datetime.now()
        if self.last_full_search_time is None:
            self.last_full_search_time = now
            return True
        if (now - self.last_full_search_time).total_seconds() >= config.FULL_SEARCH_INTERVAL:
            self.last_full_search_time = now
            return True
        if config.DEBUG_LIFECYCLE:
            remaining = config.FULL_SEARCH_INTERVAL - (now - self.last_full_search_time).total_seconds()
            print(f"[生命周期] 全局全量搜索冷却中，剩余 {remaining:.1f}s")
        return False

    def _reset_to_exploration(self, process_name):
        """重置指定浏览器进程的策略状态为探索阶段。"""
        profile = self.get_browser_profile(process_name)
        # 不完全删除，保留关闭策略等可能仍然有效的信息
        profile.update({
            'phase': config.StrategyPhase.EXPLORATION,
            'exploration_attempts': 0,
            'hard_failure_count': 0,
            'strategy_name': None,
            'strategy_quality': config.StrategyQuality.NONE,
            'last_exploration_title': None,
            'best_low_quality_info': None
        })
        if config.DEBUG_LIFECYCLE: print(f"[生命周期] {process_name} URL获取策略已重置，将重新开始探索阶段")
        self.save_cache()

    def record_strategy_result(self, process_name, strategy_name, url, title):
        """记录策略执行结果并驱动生命周期状态转变。"""
        profile = self.get_browser_profile(process_name)
        now = datetime.now().isoformat()
        if title is None:
            quality, failure_type = config.StrategyQuality.NONE, config.FailureType.HARD
        elif url is None:
            quality, failure_type = config.StrategyQuality.LOW, config.FailureType.SOFT
        else:
            quality, failure_type = config.StrategyQuality.HIGH, None
        if config.DEBUG_LIFECYCLE:
            f_type_str = f", 失效类型={failure_type}" if failure_type else ""
            print(f"[生命周期] {process_name} 策略 '{strategy_name}' 结果: 质量={quality}{f_type_str}")
        if profile['phase'] == config.StrategyPhase.EXPLORATION:
            self._handle_exploration_result(process_name, strategy_name, quality, title, now)
        elif profile['phase'] == config.StrategyPhase.STABLE:
            self._handle_stable_result(process_name, failure_type, now)
        elif profile['phase'] == config.StrategyPhase.ABANDONED:
            if failure_type == config.FailureType.HARD:
                if config.DEBUG_LIFECYCLE: print(f"[生命周期] {process_name} (已放弃) 出现硬失效，忽略。")
            else:
                profile['hard_failure_count'] = 0
        profile['last_updated'] = now
        self.save_cache()

    def _handle_exploration_result(self, process_name, strategy_name, quality, title, timestamp):
        """处理探索阶段的策略结果。"""
        profile = self.browser_profiles[process_name]
        if quality == config.StrategyQuality.HIGH:
            profile.update({
                'phase': config.StrategyPhase.STABLE, 'strategy_name': strategy_name,
                'strategy_quality': config.StrategyQuality.HIGH, 'hard_failure_count': 0
            })
            if config.DEBUG_LIFECYCLE: print(f"  [探索成功] {process_name}: 找到高质量策略 '{strategy_name}' -> 进入稳定阶段")
            return
        if title and title == profile.get('last_exploration_title'):
            if config.DEBUG_LIFECYCLE: print(f"  [探索中] {process_name}: 页面标题 '{title}' 未变，不消耗探索预算。")
            return
        profile['exploration_attempts'] += 1
        profile['last_exploration_title'] = title
        if quality == config.StrategyQuality.LOW:
            if not profile.get('best_low_quality_info'):
                profile['best_low_quality_info'] = {'name': strategy_name, 'title': title}
                if config.DEBUG_LIFECYCLE: print(f"  [探索中] {process_name}: 发现首个次优解策略 '{strategy_name}' 并暂存。")
            if config.DEBUG_LIFECYCLE: print(
                f"  [探索中] {process_name}: 找到次优解，消耗预算 ({profile['exploration_attempts']}/{config.EXPLORATION_BUDGET})")
        else:
            if config.DEBUG_LIFECYCLE: print(
                f"  [探索中] {process_name}: 策略 '{strategy_name}' 硬失效，消耗预算 ({profile['exploration_attempts']}/{config.EXPLORATION_BUDGET})")
        if profile['exploration_attempts'] >= config.EXPLORATION_BUDGET:
            best_low_info = profile.get('best_low_quality_info')
            if best_low_info:
                profile.update({
                    'phase': config.StrategyPhase.ABANDONED, 'strategy_name': best_low_info['name'],
                    'strategy_quality': config.StrategyQuality.LOW, 'hard_failure_count': 0
                })
                if config.DEBUG_LIFECYCLE: print(
                    f"  [探索耗尽] {process_name}: 预算用尽，采纳最佳次优解 '{best_low_info['name']}' -> 进入放弃阶段")
            else:
                profile['phase'] = config.StrategyPhase.FAILED
                if config.DEBUG_LIFECYCLE: print(
                    f"  [探索失败] {process_name}: 预算用尽且未找到任何可用策略 -> 进入失效阶段，等待重试")

    def _handle_stable_result(self, process_name, failure_type, timestamp):
        """处理稳定阶段的策略结果。"""
        profile = self.browser_profiles[process_name]
        if failure_type == config.FailureType.HARD:
            profile['hard_failure_count'] += 1
            if config.DEBUG_LIFECYCLE: print(
                f"  [稳定运行] {process_name} 硬失效 ({profile['hard_failure_count']}/{config.HARD_FAILURE_THRESHOLD})")
            if profile['hard_failure_count'] >= config.HARD_FAILURE_THRESHOLD:
                profile['phase'] = config.StrategyPhase.FAILED
                if config.DEBUG_LIFECYCLE: print(f"  [策略损坏] {process_name}: 达到硬失效阈值 -> 进入失效阶段，待下次重新探索")
        else:
            if profile['hard_failure_count'] > 0:
                profile['hard_failure_count'] = 0
                if config.DEBUG_LIFECYCLE: print(f"  [稳定运行] {process_name} 策略恢复健康，硬失效计数器重置。")

    def get_cached_strategy(self, process_name):
        """获取已缓存的高效策略名。"""
        profile = self.get_browser_profile(process_name)
        if profile['phase'] in [config.StrategyPhase.STABLE, config.StrategyPhase.ABANDONED] and profile.get('strategy_name'):
            return profile['strategy_name']
        return None

    def get_status_info(self, process_name):
        """获取指定浏览器进程的状态信息。"""
        profile = self.get_browser_profile(process_name)
        return {'phase': profile['phase'], 'strategy': profile.get('strategy_name', 'N/A'),
                'quality': profile.get('strategy_quality', 'N/A'),
                'hard_failures': profile.get('hard_failure_count', 0),
                'exploration_attempts': profile.get('exploration_attempts', 0),
                'is_abandoned': profile['phase'] == config.StrategyPhase.ABANDONED}

    # --- 关闭策略缓存管理 ---
    def get_close_strategy(self, process_name):
        """获取标签页关闭策略缓存。"""
        profile = self.get_browser_profile(process_name)
        return profile.get('close_strategy')

    def set_close_strategy(self, process_name, strategy_info):
        """设置标签页关闭策略缓存。"""
        profile = self.get_browser_profile(process_name)
        profile['close_strategy'] = strategy_info
        profile['last_updated'] = datetime.now().isoformat()
        if config.DEBUG_CACHE:
            print(f"  [缓存] 已为'{process_name}'创建/更新关闭策略缓存: {strategy_info}")
        self.save_cache()

    def clear_close_strategy(self, process_name):
        """清除标签页关闭策略缓存。"""
        profile = self.get_browser_profile(process_name)
        if 'close_strategy' in profile and profile['close_strategy'] is not None:
            if config.DEBUG_CACHE: print(f"  [缓存] 清除'{process_name}'已失效的关闭策略: {profile['close_strategy']}")
            profile['close_strategy'] = None
            self.save_cache()


class AIAuditManager:
    """
    AI内容审核管理器
    负责管理AI审核决策的缓存、异步请求处理和策略管理
    """
    def __init__(self, ai_config=config.AI_AUDIT_CONFIG):
        """初始化AI审核管理器，加载审核缓存。"""
        self.config = ai_config
        self.cache_file_path = self.config["cache_file_path"]
        self.ttl = timedelta(seconds=self.config["cache_ttl_seconds"])
        self.audit_cache = {}
        self.pending_urls = set()
        self.lock = threading.Lock()
        self.load_cache()

    def set_result_callback(self, callback):
        """设置一个在AI审核完成后调用的回调函数。"""
        self.result_callback = callback
    def load_cache(self):
        """从文件加载AI审核缓存。"""
        try:
            if os.path.exists(self.cache_file_path):
                with open(self.cache_file_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    self.audit_cache = data.get('audit_cache', {})
                    if config.DEBUG_CACHE:
                        print(f"[AI缓存] 已从 {self.cache_file_path} 加载 {len(self.audit_cache)} 个审核记录")
        except Exception as e:
            if config.DEBUG_CACHE:
                print(f"[AI缓存] 加载缓存失败: {e}")
            self.audit_cache = {}
        # 加载后清理过期条目
        self._prune_cache(force_save=False)

    def save_cache(self):
        """保存AI审核缓存到文件。"""
        try:
            data = {
                'audit_cache': self.audit_cache,
                'last_updated': datetime.now().isoformat()
            }
            with open(self.cache_file_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            if config.DEBUG_CACHE:
                print(f"[AI缓存] 已保存 {len(self.audit_cache)} 个审核记录到 {self.cache_file_path}")
        except Exception as e:
            if config.DEBUG_CACHE:
                print(f"[AI缓存] 保存缓存失败: {e}")

    def _prune_cache(self, force_save=True):
        """清理过期的缓存条目"""
        with self.lock:
            now = datetime.now()
            expired_urls = [
                url for url, data in self.audit_cache.items()
                if now - datetime.fromisoformat(data['timestamp']) > self.ttl
            ]
            if not expired_urls:
                return
            print(f"[AI缓存] 清理 {len(expired_urls)} 个过期条目...")
            for url in expired_urls:
                del self.audit_cache[url]
        if force_save:
            self.save_cache()

    def check_and_audit(self, url, title):
        """
        检查并审核URL和标题
        返回: "allow", "deny", "pending"
        """
        if not self.config['enabled'] or not url:
            return "allow"  # 如果禁用或URL无效，则默认允许

        with self.lock:
            # 1. 检查缓存
            if url in self.audit_cache:
                return self.audit_cache[url]['decision']
            # 2. 检查是否正在审核中
            if url in self.pending_urls:
                return "pending"
            # 3. 发起新审核
            print(f"[AI审核] 新页面: {title} ({url})，已提交后台审核。")
            self.pending_urls.add(url)

        # 启动后台线程执行AI请求
        thread = threading.Thread(target=self._perform_ai_request, args=(url, title))
        thread.daemon = True
        thread.start()
        return "pending"

    def _perform_ai_request(self, url, title):
        """
        在后台线程中执行AI请求
        """
        try:
            # 在这里实现真正的AI API调用
            # 以下为模拟实现
            print(f"[AI线程] 正在为 {url} 请求AI...")
            time.sleep(5)  # 模拟网络延迟

            prompt = self.config['prompt_template'].format(title=title, url=url)

            # TODO: 实际的AI API调用
            # response = requests.post(
            #     self.config['api_endpoint'],
            #     headers={'Authorization': f'Bearer {self.config["api_key"]}'},
            #     json={
            #         'model': self.config['model'],
            #         'messages': [{'role': 'user', 'content': prompt}],
            #         'max_tokens': 10
            #     }
            # )
            # decision_text = response.json()['choices'][0]['message']['content'].strip().lower()

            # 模拟返回 - 在实际使用时请替换为真实的API调用
            decision_text = random.choice(["allow", "deny"])

            if decision_text not in ["allow", "deny"]:
                print(f"[AI警告] AI返回了无效内容: '{decision_text}'，本次审核默认为允许。")
                decision = "allow"
            else:
                decision = decision_text
            print(f"[AI线程] {url} 审核结果: {decision.upper()}")

        except Exception as e:
            print(f"[AI错误] AI请求失败: {e}，本次审核默认为允许。")
            decision = "allow"

        # 更新缓存并保存
        with self.lock:
            self.audit_cache[url] = {
                "decision": decision,
                "timestamp": datetime.now().isoformat(),
                "title": title
            }
            if url in self.pending_urls:
                self.pending_urls.remove(url)
        # --- 新增代码 ---
        # 调用回调函数，将结果通知给GUI
        if self.result_callback:
            try:
                # 附上完整的prompt，方便GUI显示
                full_prompt = self.config['prompt_template'].format(title=title, url=url)
                self.result_callback(url, decision, full_prompt)
            except Exception as e:
                print(f"[AI回调错误] 调用回调函数时出错: {e}")
        # --- 新增代码结束 ---
        self.save_cache()
