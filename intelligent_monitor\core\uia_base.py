# intelligent_monitor/core/uia_base.py
# 中文说明：
# 本模块封装了 UIAComWrapper 组件的加载、常用 UIA 类导出、WinAPI 函数及结构体，
# 并提供窗口进程名和标题的基础辅助函数。供上层模块调用底层自动化和窗口操作。

import sys
import os
import ctypes
from ctypes import wintypes
import traceback

# --- 1. UIAComWrapper 初始化 ---
UIA_LOADED = False
AutomationElement = None
TreeScope, PropertyCondition, OrCondition, AndCondition = None, None, None, None
TreeWalker, PropertyConditionFlags = None, None
ControlType, ValuePattern, LegacyIAccessiblePattern = None, None, None
TextPattern, InvokePattern, SelectionItemPattern = None, None, None
Process, IntPtr, DotNetException = None, None, None

try:
    script_dir = os.path.dirname(sys.executable) if getattr(sys, 'frozen', False) else os.path.dirname(os.path.realpath(__file__))
    # 向上追溯两层找到项目根目录
    project_root = os.path.abspath(os.path.join(script_dir, '..', '..'))
    uiacomwrapper_path = os.path.join(project_root, "UIAComWrapper.********/lib/net40")

    if not os.path.exists(uiacomwrapper_path):
        # 兼容直接运行脚本的情况
        uiacomwrapper_path = os.path.join(os.getcwd(), "UIAComWrapper.********/lib/net40")
        if not os.path.exists(uiacomwrapper_path):
             raise FileNotFoundError(f"致命错误: 无法找到UIA依赖路径")

    sys.path.append(uiacomwrapper_path)
    import clr

    clr.AddReference("UIAComWrapper")
    clr.AddReference("Interop.UIAutomationClient")
    from System.Windows.Automation import AutomationElement, TreeScope, PropertyCondition, OrCondition, AndCondition, \
        TreeWalker, PropertyConditionFlags
    from System.Windows.Automation import ControlType, ValuePattern, LegacyIAccessiblePattern, TextPattern, \
        InvokePattern, SelectionItemPattern
    from System.Diagnostics import Process
    from System import IntPtr, Exception as DotNetException

    UIA_LOADED = True
    print("UIAComWrapper 加载成功!")
except Exception as e:
    print(f"警告: UIAComWrapper 加载失败: {e}")
    traceback.print_exc()

# --- 2. WinAPI 定义 ---
user32 = ctypes.windll.user32
wintypes.ULONG_PTR = wintypes.WPARAM
INPUT_KEYBOARD = 1
KEYEVENTF_KEYUP = 0x0002
VK_CONTROL = 0x11
VK_W = 0x57

class KEYBDINPUT(ctypes.Structure):
    """WinAPI 键盘输入结构体，模拟键盘事件用。"""
    _fields_ = (("wVk", wintypes.WORD),
                ("wScan", wintypes.WORD),
                ("dwFlags", wintypes.DWORD),
                ("time", wintypes.DWORD),
                ("dwExtraInfo", wintypes.ULONG_PTR))

class INPUT(ctypes.Structure):
    """WinAPI 输入结构体，支持键盘、鼠标、硬件输入。"""
    class _INPUT(ctypes.Union):
        _fields_ = (("ki", KEYBDINPUT),
                    ("mi", ctypes.c_void_p),
                    ("hi", ctypes.c_void_p))
    _anonymous_ = ("_input",)
    _fields_ = (("type", wintypes.DWORD),
                ("_input", _INPUT))

# --- 3. 基础辅助函数 ---
def get_window_process_name_and_title(hwnd):
    """获取给定窗口句柄的进程名和标题。用于主监控循环识别当前窗口类型。"""
    process_name, window_title = "", ""
    try:
        process_id = wintypes.DWORD()
        user32.GetWindowThreadProcessId(hwnd, ctypes.byref(process_id))
        if UIA_LOADED and process_id.value:
            process = Process.GetProcessById(process_id.value)
            process_name = process.ProcessName.lower()
        length = user32.GetWindowTextLengthW(hwnd)
        if length > 0:
            buffer = ctypes.create_unicode_buffer(length + 1)
            user32.GetWindowTextW(hwnd, buffer, length + 1)
            window_title = buffer.value
    except Exception:
        pass # 某些系统进程可能拒绝访问
    return process_name, window_title
def get_pid_from_hwnd(hwnd):
    """
    通过窗口句柄(hwnd)获取进程ID(pid)。
    """
    pid = ctypes.c_ulong()
    user32.GetWindowThreadProcessId(hwnd, ctypes.byref(pid))
    return pid.value