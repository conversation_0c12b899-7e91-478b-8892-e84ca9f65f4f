<?xml version="1.0" encoding="utf-8"?>
<Project xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Target Name="EmbeddingAssemblyInteropUIAutomationClient" AfterTargets="ResolveReferences" BeforeTargets="FindReferenceAssembliesForReferences">
    <PropertyGroup>
      <_InteropAssemblyFileName>Interop.UIAutomationClient</_InteropAssemblyFileName>
    </PropertyGroup>
    <ItemGroup>
      <ReferencePath Condition=" '%(FileName)' == '$(_InteropAssemblyFileName)' AND '%(ReferencePath.NuGetPackageId)' == '$(MSBuildThisFileName)' ">
        <EmbedInteropTypes>false</EmbedInteropTypes>
      </ReferencePath>
    </ItemGroup>
  </Target>
</Project>
