"""
Web Control Panel - 基于Flask的现代化Web界面
用户与系统交互的Web图形界面，具备状态感知的智能控制器
"""

import os
import sys
import json
import socket
import threading
import webbrowser
from datetime import datetime
from typing import Dict, List, Any, Optional
from flask import Flask, render_template, request, jsonify, send_from_directory
from werkzeug.serving import make_server

# 添加项目根目录到路径
project_root = os.path.dirname(os.path.dirname(__file__))
sys.path.insert(0, project_root)

import common

class WebControlPanel:
    def __init__(self):
        self.app = Flask(__name__, 
                        template_folder=os.path.join(os.path.dirname(__file__), 'templates'),
                        static_folder=os.path.join(os.path.dirname(__file__), 'static'))
        
        # 状态变量
        self.system_status = "unknown"  # "protected", "hibernating", "unknown"
        self.tcp_port = 54321
        self.web_port = 8080
        self.server = None
        
        self._setup_routes()
        
    def _setup_routes(self):
        """设置Flask路由"""
        
        @self.app.route('/')
        def index():
            """主页"""
            return render_template('index.html')
        
        @self.app.route('/api/status')
        def get_status():
            """获取系统状态API"""
            try:
                status_info = self._detect_system_status()
                return jsonify(status_info)
            except Exception as e:
                return jsonify({
                    'status': 'error',
                    'message': str(e),
                    'system_status': 'unknown'
                }), 500
        
        @self.app.route('/api/rules')
        def get_rules():
            """获取黑名单规则API"""
            try:
                response = self._send_command('get_rules')
                if response and response.get('status') == 'success':
                    rules = response.get('rules', [])
                    parsed_rules = []
                    
                    for rule in rules:
                        parsed_rule = self._parse_rule(rule)
                        parsed_rules.append(parsed_rule)
                    
                    return jsonify({
                        'status': 'success',
                        'rules': parsed_rules,
                        'count': len(parsed_rules)
                    })
                else:
                    return jsonify({
                        'status': 'error',
                        'message': '无法连接到后台服务',
                        'rules': [],
                        'count': 0
                    })
            except Exception as e:
                return jsonify({
                    'status': 'error',
                    'message': str(e),
                    'rules': [],
                    'count': 0
                }), 500
        
        @self.app.route('/api/rules', methods=['POST'])
        def add_rule():
            """添加规则API"""
            try:
                data = request.get_json()
                process_name = data.get('process_name', '').strip()
                rule_type = data.get('rule_type', 'all_day')
                start_time = data.get('start_time', '09:00')
                end_time = data.get('end_time', '18:00')
                
                if not process_name:
                    return jsonify({
                        'status': 'error',
                        'message': '请输入进程名称'
                    }), 400
                
                # 构建规则字符串
                if rule_type == 'all_day':
                    rule = process_name
                else:
                    rule = f"{process_name},{start_time},{end_time}"
                
                # 发送添加命令
                response = self._send_command('add_rule', {'rule': rule})
                
                if response and response.get('status') == 'success':
                    return jsonify({
                        'status': 'success',
                        'message': '规则添加成功'
                    })
                else:
                    error_msg = response.get('message', '未知错误') if response else '连接失败'
                    return jsonify({
                        'status': 'error',
                        'message': f'添加规则失败: {error_msg}'
                    }), 500
                    
            except Exception as e:
                return jsonify({
                    'status': 'error',
                    'message': f'添加规则失败: {str(e)}'
                }), 500
        
        @self.app.route('/api/rules', methods=['DELETE'])
        def delete_rule():
            """删除规则API"""
            try:
                data = request.get_json()
                rule = data.get('rule', '').strip()
                
                if not rule:
                    return jsonify({
                        'status': 'error',
                        'message': '请提供要删除的规则'
                    }), 400
                
                response = self._send_command('remove_rule', {'rule': rule})
                
                if response and response.get('status') == 'success':
                    return jsonify({
                        'status': 'success',
                        'message': '规则删除成功'
                    })
                else:
                    error_msg = response.get('message', '未知错误') if response else '连接失败'
                    return jsonify({
                        'status': 'error',
                        'message': f'删除规则失败: {error_msg}'
                    }), 500
                    
            except Exception as e:
                return jsonify({
                    'status': 'error',
                    'message': f'删除规则失败: {str(e)}'
                }), 500
        
        @self.app.route('/api/processes')
        def get_processes():
            """获取进程列表API"""
            try:
                response = self._send_command('get_process_list')
                processes = []
                
                if response and response.get('status') == 'success':
                    processes = response.get('processes', [])
                else:
                    # 如果后台服务不可用，使用本地进程获取
                    processes = self._get_local_processes()
                
                return jsonify({
                    'status': 'success',
                    'processes': processes,
                    'count': len(processes)
                })
                
            except Exception as e:
                return jsonify({
                    'status': 'error',
                    'message': str(e),
                    'processes': [],
                    'count': 0
                }), 500
        
        @self.app.route('/api/protection/activate', methods=['POST'])
        def activate_protection():
            """激活保护API (新版：启用并启动服务)"""
            try:
                if not common.is_admin():
                    return jsonify({
                        'status': 'error',
                        'message': '需要管理员权限才能激活保护。'
                    }), 403

                # 从配置加载服务名称
                config = common.load_config()
                service_name = config.get('service_name')

                if not service_name:
                    return jsonify({
                        'status': 'error',
                        'message': '无法找到服务名称，请尝试重新安装。'
                    }), 400

                # 步骤1: 启用服务自启动
                if not common.enable_service_autostart(service_name):
                    return jsonify({
                        'status': 'error',
                        'message': '启用服务自启动失败，请检查系统日志。'
                    }), 500

                # 步骤2: 立即启动服务
                # 给一点时间让系统处理配置变更
                import time
                time.sleep(1)
                if not common.start_service(service_name):
                    return jsonify({
                        'status': 'error',
                        'message': '启动守护服务失败，请检查系统日志。'
                    }), 500

                return jsonify({
                    'status': 'success',
                    'message': '守护已成功激活！页面将在几秒后刷新。'
                })

            except Exception as e:
                return jsonify({
                    'status': 'error',
                    'message': f'激活保护失败: {str(e)}'
                }), 500
        
        @self.app.route('/api/protection/deactivate', methods=['POST'])
        def deactivate_protection():
            """停用保护API"""
            try:
                response = self._send_command('deactivate_protection')
                
                if response and response.get('status') == 'success':
                    return jsonify({
                        'status': 'success',
                        'message': '保护已暂停'
                    })
                else:
                    error_msg = response.get('message', '未知错误') if response else '连接失败'
                    return jsonify({
                        'status': 'error',
                        'message': f'暂停保护失败: {error_msg}'
                    }), 500
                    
            except Exception as e:
                return jsonify({
                    'status': 'error',
                    'message': f'停用保护失败: {str(e)}'
                }), 500
        
        @self.app.route('/api/window-config', methods=['POST'])
        def set_window_config():
            """设置窗口期配置API"""
            try:
                data = request.get_json()
                
                config_data = {
                    'enabled': data.get('enabled', True),
                    'day_of_week': data.get('day_of_week', 0),
                    'start_time': data.get('start_time', '02:00'),
                    'end_time': data.get('end_time', '04:00')
                }
                
                response = self._send_command('set_uninstall_window', config_data)
                
                if response and response.get('status') == 'success':
                    return jsonify({
                        'status': 'success',
                        'message': '窗口期配置已保存'
                    })
                else:
                    error_msg = response.get('message', '未知错误') if response else '连接失败'
                    return jsonify({
                        'status': 'error',
                        'message': f'保存配置失败: {error_msg}'
                    }), 500
                    
            except Exception as e:
                return jsonify({
                    'status': 'error',
                    'message': f'保存配置失败: {str(e)}'
                }), 500

        # ==================== 融合版新增API路由 ====================

        @self.app.route('/api/features')
        def get_features():
            """获取功能开关状态API"""
            try:
                config = common.load_config()
                features = config.get('features', {})
                return jsonify({
                    'status': 'success',
                    'features': features
                })
            except Exception as e:
                return jsonify({
                    'status': 'error',
                    'message': str(e),
                    'features': {}
                }), 500

        @self.app.route('/api/features', methods=['POST'])
        def set_features():
            """设置功能开关状态API"""
            try:
                data = request.get_json()
                feature_name = data.get('feature_name')
                enabled = data.get('enabled', False)

                if not feature_name:
                    return jsonify({
                        'status': 'error',
                        'message': '请提供功能名称'
                    }), 400

                common.set_feature_config(feature_name, enabled)
                return jsonify({
                    'status': 'success',
                    'message': f'功能 {feature_name} 已{"启用" if enabled else "禁用"}'
                })
            except Exception as e:
                return jsonify({
                    'status': 'error',
                    'message': str(e)
                }), 500

        @self.app.route('/api/ai-config')
        def get_ai_config():
            """获取AI配置API"""
            try:
                text_config = common.get_ai_config('text_audit')
                vision_config = common.get_ai_config('vision_audit')
                return jsonify({
                    'status': 'success',
                    'text_audit': text_config,
                    'vision_audit': vision_config
                })
            except Exception as e:
                return jsonify({
                    'status': 'error',
                    'message': str(e)
                }), 500

        @self.app.route('/api/ai-config', methods=['POST'])
        def set_ai_config():
            """设置AI配置API"""
            try:
                data = request.get_json()
                ai_type = data.get('ai_type')  # 'text_audit' or 'vision_audit'
                config_data = data.get('config', {})

                if not ai_type or ai_type not in ['text_audit', 'vision_audit']:
                    return jsonify({
                        'status': 'error',
                        'message': '请提供有效的AI类型'
                    }), 400

                common.set_ai_config(ai_type, config_data)
                return jsonify({
                    'status': 'success',
                    'message': f'{ai_type} 配置已保存'
                })
            except Exception as e:
                return jsonify({
                    'status': 'error',
                    'message': str(e)
                }), 500

        @self.app.route('/api/time-limits')
        def get_time_limits():
            """获取时间限制规则API"""
            try:
                rules = common.get_time_limit_rules()
                return jsonify({
                    'status': 'success',
                    'rules': rules
                })
            except Exception as e:
                return jsonify({
                    'status': 'error',
                    'message': str(e),
                    'rules': {}
                }), 500

        @self.app.route('/api/time-limits', methods=['POST'])
        def set_time_limit():
            """设置时间限制规则API"""
            try:
                data = request.get_json()
                category = data.get('category')
                limit_minutes = data.get('limit_minutes', 60)
                enabled = data.get('enabled', True)

                if not category:
                    return jsonify({
                        'status': 'error',
                        'message': '请提供分类名称'
                    }), 400

                common.set_time_limit_rule(category, limit_minutes, enabled)
                return jsonify({
                    'status': 'success',
                    'message': f'分类 {category} 的时间限制已设置'
                })
            except Exception as e:
                return jsonify({
                    'status': 'error',
                    'message': str(e)
                }), 500

        @self.app.route('/api/browser-blacklist')
        def get_browser_blacklist():
            """获取浏览器内容黑名单API"""
            try:
                blacklist = common.get_browser_content_blacklist()
                return jsonify({
                    'status': 'success',
                    'blacklist': blacklist
                })
            except Exception as e:
                return jsonify({
                    'status': 'error',
                    'message': str(e),
                    'blacklist': {'keywords': [], 'urls': []}
                }), 500

        @self.app.route('/api/browser-blacklist', methods=['POST'])
        def add_browser_blacklist():
            """添加浏览器内容黑名单API"""
            try:
                data = request.get_json()
                content_type = data.get('content_type')  # 'keywords' or 'urls'
                value = data.get('value', '').strip()

                if not content_type or content_type not in ['keywords', 'urls']:
                    return jsonify({
                        'status': 'error',
                        'message': '请提供有效的内容类型'
                    }), 400

                if not value:
                    return jsonify({
                        'status': 'error',
                        'message': '请提供要添加的内容'
                    }), 400

                common.add_browser_content_blacklist(content_type, value)
                return jsonify({
                    'status': 'success',
                    'message': f'{content_type} 黑名单项已添加'
                })
            except Exception as e:
                return jsonify({
                    'status': 'error',
                    'message': str(e)
                }), 500

        @self.app.route('/api/browser-blacklist', methods=['DELETE'])
        def remove_browser_blacklist():
            """删除浏览器内容黑名单API"""
            try:
                data = request.get_json()
                content_type = data.get('content_type')
                value = data.get('value', '').strip()

                if not content_type or not value:
                    return jsonify({
                        'status': 'error',
                        'message': '请提供完整的删除信息'
                    }), 400

                common.remove_browser_content_blacklist(content_type, value)
                return jsonify({
                    'status': 'success',
                    'message': f'{content_type} 黑名单项已删除'
                })
            except Exception as e:
                return jsonify({
                    'status': 'error',
                    'message': str(e)
                }), 500

        @self.app.route('/api/database-stats')
        def get_database_stats():
            """获取数据库统计信息API"""
            try:
                # 这里需要通过后台服务获取数据库统计信息
                # 暂时返回模拟数据
                stats = {
                    'app_classifications': 0,
                    'focus_logs': 0,
                    'temp_whitelist_entries': 0
                }
                return jsonify({
                    'status': 'success',
                    'stats': stats
                })
            except Exception as e:
                return jsonify({
                    'status': 'error',
                    'message': str(e),
                    'stats': {}
                }), 500

        @self.app.route('/api/app-classifications')
        def get_app_classifications():
            """获取应用分类信息API"""
            try:
                # 这里需要通过后台服务获取应用分类信息
                # 暂时返回模拟数据
                classifications = []
                return jsonify({
                    'status': 'success',
                    'classifications': classifications
                })
            except Exception as e:
                return jsonify({
                    'status': 'error',
                    'message': str(e),
                    'classifications': []
                }), 500

    def _send_command(self, command: str, data: Dict[str, Any] = None) -> Optional[Dict[str, Any]]:
        """向后台发送命令"""
        try:
            # 创建socket连接
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(5.0)
            sock.connect(('127.0.0.1', self.tcp_port))

            # 发送请求
            request = {'command': command, 'data': data or {}}
            request_json = json.dumps(request, ensure_ascii=False)
            sock.send(request_json.encode('utf-8'))

            # 接收响应
            response_data = sock.recv(4096).decode('utf-8')
            response = json.loads(response_data)

            sock.close()
            return response

        except Exception as e:
            print(f"发送命令失败: {str(e)}")
            return None

    def _detect_system_status(self) -> Dict[str, Any]:
        """检测系统状态 (新版：基于Windows服务)"""
        try:
            # 优先尝试TCP连接，这是最快的方式
            response = self._send_command('get_status')

            if response and response.get('status') == 'success':
                self.system_status = "protected"
                return {
                    'system_status': 'protected',
                    'status_text': '守护中',
                    'status_detail': '系统正在保护中，所有规则生效。',
                    'status_color': 'success',
                    'in_window': response.get('in_uninstall_window', False),
                    'window_config': response.get('uninstall_window', {})
                }

            # 如果TCP连接失败，查询服务状态来判断是休眠还是未知错误
            config = common.load_config()
            service_name = config.get('service_name')

            if not service_name:
                self.system_status = "uninstalled"
                return {
                    'system_status': 'uninstalled',
                    'status_text': '未安装',
                    'status_detail': '系统似乎尚未安装或配置已损坏。',
                    'status_color': 'danger'
                }

            # 使用 sc qc 查询服务配置 (能同时获取状态和启动类型)
            import subprocess
            result = subprocess.run(['sc', 'qc', service_name], capture_output=True, text=True, creationflags=subprocess.CREATE_NO_WINDOW)

            if result.returncode == 0:
                output = result.stdout
                is_disabled = "DISABLED" in output

                if is_disabled:
                    self.system_status = "hibernating"
                    return {
                        'system_status': 'hibernating',
                        'status_text': '休眠中',
                        'status_detail': '守护已暂停，您可以随时重新启动保护。',
                        'status_color': 'warning',
                    }

            # 如果服务存在但未禁用，或服务不存在，都视为未知状态
            self.system_status = "unknown"
            return {
                'system_status': 'unknown',
                'status_text': '状态未知',
                'status_detail': '无法连接到后台服务，且服务状态异常。',
                'status_color': 'danger',
            }

        except Exception as e:
            print(f"检测系统状态失败: {str(e)}")
            self.system_status = "unknown"
            return {
                'system_status': 'unknown',
                'status_text': '状态未知',
                'status_detail': f'检测失败: {str(e)}',
                'status_color': 'danger',
                'in_window': False,
                'window_config': {}
            }



    def _parse_rule(self, rule: str) -> Dict[str, Any]:
        """解析规则字符串"""
        try:
            if ',' in rule:
                # 时间段规则: process_name.exe,start_time,end_time
                parts = rule.split(',')
                if len(parts) >= 3:
                    return {
                        'raw': rule,
                        'process_name': parts[0],
                        'rule_type': 'time_range',
                        'start_time': parts[1],
                        'end_time': parts[2],
                        'time_range': f"{parts[1]} - {parts[2]}"
                    }
                else:
                    return {
                        'raw': rule,
                        'process_name': rule,
                        'rule_type': 'all_day',
                        'start_time': '',
                        'end_time': '',
                        'time_range': '全天禁止'
                    }
            else:
                # 全天规则: process_name.exe
                return {
                    'raw': rule,
                    'process_name': rule,
                    'rule_type': 'all_day',
                    'start_time': '',
                    'end_time': '',
                    'time_range': '全天禁止'
                }

        except Exception as e:
            print(f"解析规则失败: {rule}, 错误: {str(e)}")
            return {
                'raw': rule,
                'process_name': rule,
                'rule_type': 'unknown',
                'start_time': '',
                'end_time': '',
                'time_range': '解析失败'
            }

    def _get_local_processes(self):
        """直接获取本地进程列表（离线模式）"""
        try:
            import psutil
            processes = []

            for proc in psutil.process_iter(['pid', 'name', 'exe']):
                try:
                    proc_info = proc.info
                    processes.append({
                        'pid': proc_info['pid'],
                        'name': proc_info['name'] or 'Unknown',
                        'exe': proc_info['exe'] or 'Unknown'
                    })
                except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
                    # 跳过无法访问的进程
                    continue

            return processes

        except ImportError:
            print("psutil库未安装，无法获取进程列表")
            return []
        except Exception as e:
            print(f"获取本地进程失败: {str(e)}")
            return []

    def start_server(self):
        """启动Flask服务器"""
        try:
            # 查找可用端口
            for port in range(8080, 8090):
                try:
                    self.server = make_server('127.0.0.1', port, self.app, threaded=True)
                    self.web_port = port
                    print(f"Web服务器启动在端口: {port}")
                    break
                except OSError:
                    continue

            if self.server:
                # 在单独线程中启动服务器
                server_thread = threading.Thread(target=self.server.serve_forever, daemon=True)
                server_thread.start()
                return True
            else:
                print("无法找到可用端口启动Web服务器")
                return False

        except Exception as e:
            print(f"启动Web服务器失败: {str(e)}")
            return False

    def stop_server(self):
        """停止Flask服务器"""
        if self.server:
            self.server.shutdown()
            self.server = None

    def get_url(self):
        """获取Web界面URL"""
        return f"http://127.0.0.1:{self.web_port}"

def create_webview_app():
    """创建webview应用"""
    try:
        import webview
    except ImportError:
        # 如果 pywebview 未安装，这是致命错误，无法提供GUI
        # 使用 tkinter 显示一个错误消息框
        import tkinter as tk
        from tkinter import messagebox
        root = tk.Tk()
        root.withdraw()
        messagebox.showerror(
            "依赖缺失",
            "启动失败：缺少必要的 'pywebview' 库。\n\n请使用 'pip install pywebview' 命令安装后重试。"
        )
        root.destroy()
        print("错误: 缺少 pywebview 库。")
        return # 直接退出

    try:
        # 创建Web控制面板
        web_panel = WebControlPanel()

        # 启动Flask服务器
        if not web_panel.start_server():
            messagebox.showerror("启动失败", "无法启动本地Web服务器，请检查端口是否被占用。")
            print("无法启动Web服务器")
            return

        # 创建webview窗口
        webview.create_window(
            title="自律守护者 - 控制面板",
            url=web_panel.get_url(),
            width=1000,
            height=700,
            min_size=(800, 600),
            resizable=True,
            shadow=True,
            on_top=False
        )

        # 启动webview
        webview.start(debug=False)

        # 清理
        web_panel.stop_server()

    except Exception as e:
        # 捕获其他可能的错误
        import tkinter as tk
        from tkinter import messagebox
        root = tk.Tk()
        root.withdraw()
        messagebox.showerror("运行时错误", f"启动webview应用失败: {str(e)}")
        root.destroy()
        print(f"启动webview应用失败: {str(e)}")

def main():
    """主函数"""
    create_webview_app()

if __name__ == "__main__":
    main()

