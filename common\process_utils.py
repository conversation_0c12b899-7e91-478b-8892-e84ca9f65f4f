"""
进程工具箱 - 提供进程枚举、检查和管理功能
使用psutil库进行跨平台的进程操作
"""

import os
import time
import subprocess
from typing import List, Dict, Optional, Any

try:
    import psutil
    PSUTIL_AVAILABLE = True
except ImportError:
    PSUTIL_AVAILABLE = False
    print("警告: psutil库未安装，进程功能可能受限")

class ProcessUtils:
    def __init__(self):
        self.psutil_available = PSUTIL_AVAILABLE
    
    def get_running_processes(self) -> List[Dict[str, Any]]:
        """
        获取所有运行中的进程信息
        
        Returns:
            list: 包含进程信息字典的列表
        """
        processes = []
        
        if not self.psutil_available:
            print("psutil不可用，无法获取进程列表")
            return processes
        
        try:
            for proc in psutil.process_iter(['pid', 'name', 'exe', 'create_time']):
                try:
                    proc_info = proc.info
                    if proc_info['name']:
                        processes.append({
                            'pid': proc_info['pid'],
                            'name': proc_info['name'],
                            'exe': proc_info['exe'] or '',
                            'create_time': proc_info['create_time'] or 0
                        })
                except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
                    # 忽略无法访问的进程
                    continue
                    
        except Exception as e:
            print(f"获取进程列表失败: {str(e)}")
        
        return processes
    
    def is_process_running(self, process_name: str) -> bool:
        """
        检查指定名称的进程是否正在运行
        
        Args:
            process_name: 进程名称（如 "notepad.exe"）
            
        Returns:
            bool: 进程运行中返回True，否则返回False
        """
        if not self.psutil_available:
            return self._is_process_running_fallback(process_name)
        
        try:
            for proc in psutil.process_iter(['name']):
                try:
                    if proc.info['name'] and proc.info['name'].lower() == process_name.lower():
                        return True
                except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
                    continue
            return False
            
        except Exception as e:
            print(f"检查进程失败: {str(e)}")
            return False
    
    def _is_process_running_fallback(self, process_name: str) -> bool:
        """
        备用的进程检查方法（不使用psutil）
        
        Args:
            process_name: 进程名称
            
        Returns:
            bool: 进程运行中返回True，否则返回False
        """
        try:
            # 使用tasklist命令
            result = subprocess.run(
                ['tasklist', '/FI', f'IMAGENAME eq {process_name}'],
                capture_output=True, text=True, creationflags=subprocess.CREATE_NO_WINDOW
            )
            
            if result.returncode == 0:
                return process_name.lower() in result.stdout.lower()
            return False
            
        except Exception as e:
            print(f"备用进程检查失败: {str(e)}")
            return False
    
    def kill_process_by_name(self, process_name: str) -> int:
        """
        根据进程名称终止进程
        
        Args:
            process_name: 进程名称
            
        Returns:
            int: 被终止的进程数量
        """
        killed_count = 0
        
        if not self.psutil_available:
            return self._kill_process_fallback(process_name)
        
        try:
            for proc in psutil.process_iter(['pid', 'name']):
                try:
                    if proc.info['name'] and proc.info['name'].lower() == process_name.lower():
                        proc.kill()
                        killed_count += 1
                        print(f"已终止进程: {proc.info['name']} (PID: {proc.info['pid']})")
                except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
                    continue
                except Exception as e:
                    print(f"终止进程失败: {str(e)}")
                    
        except Exception as e:
            print(f"批量终止进程失败: {str(e)}")
        
        return killed_count
    
    def _kill_process_fallback(self, process_name: str) -> int:
        """
        备用的进程终止方法（不使用psutil）
        
        Args:
            process_name: 进程名称
            
        Returns:
            int: 被终止的进程数量（估计值）
        """
        try:
            # 使用taskkill命令
            result = subprocess.run(
                ['taskkill', '/F', '/IM', process_name],
                capture_output=True, text=True, creationflags=subprocess.CREATE_NO_WINDOW
            )
            
            if result.returncode == 0:
                # 简单估计：如果成功则认为终止了至少1个进程
                return 1
            return 0
            
        except Exception as e:
            print(f"备用进程终止失败: {str(e)}")
            return 0
    
    def start_process(self, exe_path: str, args: List[str] = None) -> Optional[int]:
        """
        启动新进程
        
        Args:
            exe_path: 可执行文件路径
            args: 命令行参数列表
            
        Returns:
            int: 新进程的PID，失败返回None
        """
        try:
            cmd = [exe_path]
            if args:
                cmd.extend(args)
            
            # 在Windows上隐藏窗口
            creation_flags = subprocess.CREATE_NO_WINDOW if os.name == 'nt' else 0
            
            proc = subprocess.Popen(
                cmd,
                creationflags=creation_flags,
                stdout=subprocess.DEVNULL,
                stderr=subprocess.DEVNULL
            )
            
            print(f"已启动进程: {exe_path} (PID: {proc.pid})")
            return proc.pid
            
        except Exception as e:
            print(f"启动进程失败: {str(e)}")
            return None
    
    def get_process_info(self, pid: int) -> Optional[Dict[str, Any]]:
        """
        获取指定PID的进程信息
        
        Args:
            pid: 进程ID
            
        Returns:
            dict: 进程信息字典，失败返回None
        """
        if not self.psutil_available:
            return None
        
        try:
            proc = psutil.Process(pid)
            return {
                'pid': proc.pid,
                'name': proc.name(),
                'exe': proc.exe(),
                'create_time': proc.create_time(),
                'status': proc.status(),
                'memory_info': proc.memory_info()._asdict()
            }
            
        except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
            return None
        except Exception as e:
            print(f"获取进程信息失败: {str(e)}")
            return None

# 全局进程工具实例
_process_utils = ProcessUtils()

def get_running_processes() -> List[Dict[str, Any]]:
    """获取运行中的进程列表"""
    return _process_utils.get_running_processes()

def is_process_running(process_name: str) -> bool:
    """检查进程是否运行"""
    return _process_utils.is_process_running(process_name)

def kill_process_by_name(process_name: str) -> int:
    """根据名称终止进程"""
    return _process_utils.kill_process_by_name(process_name)

def start_process(exe_path: str, args: List[str] = None) -> Optional[int]:
    """启动新进程"""
    return _process_utils.start_process(exe_path, args)

def get_process_info(pid: int) -> Optional[Dict[str, Any]]:
    """获取进程信息"""
    return _process_utils.get_process_info(pid)
