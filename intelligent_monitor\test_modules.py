# intelligent_monitor/test_modules.py

"""
模块化重构后的测试脚本
用于验证各个模块的基本功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_config_module():
    """测试配置模块"""
    print("=== 测试配置模块 ===")
    try:
        import config
        print(f"✓ 配置模块加载成功")
        print(f"  - 黑名单关键字数量: {len(config.BLACKLIST_KEYWORDS)}")
        print(f"  - URL黑名单关键字数量: {len(config.URL_BLACKLIST_KEYWORDS)}")
        print(f"  - 支持的浏览器进程数量: {len(config.BROWSER_PROCESSES)}")
        print(f"  - AI审核功能: {'开启' if config.AI_AUDIT_CONFIG['enabled'] else '关闭'}")
        print(f"  - 探索预算: {config.EXPLORATION_BUDGET}")
        return True
    except Exception as e:
        print(f"✗ 配置模块测试失败: {e}")
        return False

def test_uia_base_module():
    """测试UIA基础模块"""
    print("\n=== 测试UIA基础模块 ===")
    try:
        from core import uia_base
        print(f"✓ UIA基础模块加载成功")
        print(f"  - UIA加载状态: {'成功' if uia_base.UIA_LOADED else '失败'}")
        if uia_base.UIA_LOADED:
            print(f"  - AutomationElement: {uia_base.AutomationElement is not None}")
            print(f"  - ControlType: {uia_base.ControlType is not None}")
        
        # 测试获取当前窗口信息
        hwnd = uia_base.user32.GetForegroundWindow()
        if hwnd:
            process_name, window_title = uia_base.get_window_process_name_and_title(hwnd)
            print(f"  - 当前窗口: {process_name} - {window_title}")
        return True
    except Exception as e:
        print(f"✗ UIA基础模块测试失败: {e}")
        return False

def test_blacklist_checker_module():
    """测试黑名单检查模块"""
    print("\n=== 测试黑名单检查模块 ===")
    try:
        from modules.blacklist_checker import check_title_against_blacklist, check_url_against_blacklist
        print(f"✓ 黑名单检查模块加载成功")
        
        # 测试标题检查
        test_titles = ["正常标题", "游戏网站", "广告页面"]
        test_blacklist = ["游戏", "广告"]
        
        for title in test_titles:
            result = check_title_against_blacklist(title, test_blacklist)
            print(f"  - 标题 '{title}': {'命中' if result else '通过'}")
        
        # 测试URL检查
        test_urls = ["https://normal-site.com", "https://gambling-site.com/page"]
        url_blacklist = ["gambling-site.com"]
        
        for url in test_urls:
            result = check_url_against_blacklist(url, url_blacklist)
            print(f"  - URL '{url}': {'命中' if result else '通过'}")
        
        return True
    except Exception as e:
        print(f"✗ 黑名单检查模块测试失败: {e}")
        return False

def test_managers_module():
    """测试管理器模块"""
    print("\n=== 测试管理器模块 ===")
    try:
        from modules.managers import StrategyLifecycleManager, AIAuditManager
        print(f"✓ 管理器模块加载成功")
        
        # 测试生命周期管理器
        lifecycle_manager = StrategyLifecycleManager()
        print(f"  - 生命周期管理器创建成功")
        
        # 测试获取浏览器档案
        profile = lifecycle_manager.get_browser_profile("test_browser")
        print(f"  - 测试浏览器档案: 阶段={profile['phase']}")
        
        # 测试AI审核管理器
        ai_manager = AIAuditManager()
        print(f"  - AI审核管理器创建成功")
        print(f"  - AI审核缓存大小: {len(ai_manager.audit_cache)}")
        
        return True
    except Exception as e:
        print(f"✗ 管理器模块测试失败: {e}")
        return False

def test_browser_info_fetcher_module():
    """测试浏览器信息获取模块"""
    print("\n=== 测试浏览器信息获取模块 ===")
    try:
        from modules.browser_info_fetcher import URL_STRATEGIES
        print(f"✓ 浏览器信息获取模块加载成功")
        print(f"  - 可用策略数量: {len(URL_STRATEGIES)}")
        for strategy_name in URL_STRATEGIES.keys():
            print(f"    * {strategy_name}")
        return True
    except Exception as e:
        print(f"✗ 浏览器信息获取模块测试失败: {e}")
        return False

def test_tab_closer_module():
    """测试标签页关闭模块"""
    print("\n=== 测试标签页关闭模块 ===")
    try:
        from modules.tab_closer import CLOSE_STRATEGIES_TO_TRY
        print(f"✓ 标签页关闭模块加载成功")
        print(f"  - 可用关闭策略数量: {len(CLOSE_STRATEGIES_TO_TRY)}")
        for strategy in CLOSE_STRATEGIES_TO_TRY:
            print(f"    * {strategy['name']}")
        return True
    except Exception as e:
        print(f"✗ 标签页关闭模块测试失败: {e}")
        return False

def main():
    """运行所有测试"""
    print("智能监控系统 - 模块化重构测试")
    print("=" * 50)
    
    tests = [
        test_config_module,
        test_uia_base_module,
        test_blacklist_checker_module,
        test_managers_module,
        test_browser_info_fetcher_module,
        test_tab_closer_module
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
    
    print(f"\n=== 测试结果 ===")
    print(f"通过: {passed}/{total}")
    print(f"成功率: {passed/total*100:.1f}%")
    
    if passed == total:
        print("🎉 所有模块测试通过！重构成功！")
    else:
        print("⚠️  部分模块测试失败，请检查相关问题。")

if __name__ == "__main__":
    main()
