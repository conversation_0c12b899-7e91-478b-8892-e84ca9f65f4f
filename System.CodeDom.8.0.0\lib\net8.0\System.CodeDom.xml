﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.CodeDom</name>
  </assembly>
  <members>
    <member name="T:Microsoft.CSharp.CSharpCodeProvider">
      <summary>Provides access to instances of the C# code generator and code compiler.</summary>
    </member>
    <member name="M:Microsoft.CSharp.CSharpCodeProvider.#ctor">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.CSharp.CSharpCodeProvider" /> class.</summary>
    </member>
    <member name="M:Microsoft.CSharp.CSharpCodeProvider.#ctor(System.Collections.Generic.IDictionary{System.String,System.String})">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.CSharp.CSharpCodeProvider" /> class by using the specified provider options.</summary>
      <param name="providerOptions">A <see cref="T:System.Collections.Generic.IDictionary`2" /> object that contains the provider options.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="providerOptions" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:Microsoft.CSharp.CSharpCodeProvider.CreateCompiler">
      <summary>Gets an instance of the C# code compiler.</summary>
      <returns>An instance of the C# <see cref="T:System.CodeDom.Compiler.ICodeCompiler" /> implementation.</returns>
    </member>
    <member name="M:Microsoft.CSharp.CSharpCodeProvider.CreateGenerator">
      <summary>Gets an instance of the C# code generator.</summary>
      <returns>An instance of the C# <see cref="T:System.CodeDom.Compiler.ICodeGenerator" /> implementation.</returns>
    </member>
    <member name="M:Microsoft.CSharp.CSharpCodeProvider.GenerateCodeFromMember(System.CodeDom.CodeTypeMember,System.IO.TextWriter,System.CodeDom.Compiler.CodeGeneratorOptions)">
      <summary>Generates code for the specified class member using the specified text writer and code generator options.</summary>
      <param name="member">A <see cref="T:System.CodeDom.CodeTypeMember" /> to generate code for.</param>
      <param name="writer">The <see cref="T:System.IO.TextWriter" /> to write to.</param>
      <param name="options">The <see cref="T:System.CodeDom.Compiler.CodeGeneratorOptions" /> to use when generating the code.</param>
    </member>
    <member name="M:Microsoft.CSharp.CSharpCodeProvider.GetConverter(System.Type)">
      <summary>Gets a <see cref="T:System.ComponentModel.TypeConverter" /> for the specified type of object.</summary>
      <param name="type">The type of object to retrieve a type converter for.</param>
      <returns>A <see cref="T:System.ComponentModel.TypeConverter" /> for the specified type.</returns>
    </member>
    <member name="P:Microsoft.CSharp.CSharpCodeProvider.FileExtension">
      <summary>Gets the file name extension to use when creating source code files.</summary>
      <returns>The file name extension to use for generated source code files.</returns>
    </member>
    <member name="T:Microsoft.VisualBasic.VBCodeProvider">
      <summary>Provides access to instances of the Visual Basic code generator and code compiler.</summary>
    </member>
    <member name="M:Microsoft.VisualBasic.VBCodeProvider.#ctor">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.VisualBasic.VBCodeProvider" /> class.</summary>
    </member>
    <member name="M:Microsoft.VisualBasic.VBCodeProvider.#ctor(System.Collections.Generic.IDictionary{System.String,System.String})">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.VisualBasic.VBCodeProvider" /> class by using the specified provider options.</summary>
      <param name="providerOptions">A <see cref="T:System.Collections.Generic.IDictionary`2" /> object that contains the provider options.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="providerOptions" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:Microsoft.VisualBasic.VBCodeProvider.CreateCompiler">
      <summary>Gets an instance of the Visual Basic code compiler.</summary>
      <returns>An instance of the Visual Basic <see cref="T:System.CodeDom.Compiler.ICodeCompiler" /> implementation.</returns>
    </member>
    <member name="M:Microsoft.VisualBasic.VBCodeProvider.CreateGenerator">
      <summary>Gets an instance of the Visual Basic code generator.</summary>
      <returns>An instance of the Visual Basic <see cref="T:System.CodeDom.Compiler.ICodeGenerator" /> implementation.</returns>
    </member>
    <member name="M:Microsoft.VisualBasic.VBCodeProvider.GenerateCodeFromMember(System.CodeDom.CodeTypeMember,System.IO.TextWriter,System.CodeDom.Compiler.CodeGeneratorOptions)">
      <summary>Generates code for the specified class member using the specified text writer and code generator options.</summary>
      <param name="member">A <see cref="T:System.CodeDom.CodeTypeMember" /> to generate code for.</param>
      <param name="writer">The <see cref="T:System.IO.TextWriter" /> to write to.</param>
      <param name="options">The <see cref="T:System.CodeDom.Compiler.CodeGeneratorOptions" /> to use when generating the code.</param>
    </member>
    <member name="M:Microsoft.VisualBasic.VBCodeProvider.GetConverter(System.Type)">
      <summary>Gets a <see cref="T:System.ComponentModel.TypeConverter" /> for the specified type of object.</summary>
      <param name="type">The type of object to retrieve a type converter for.</param>
      <returns>A <see cref="T:System.ComponentModel.TypeConverter" /> for the specified type.</returns>
    </member>
    <member name="P:Microsoft.VisualBasic.VBCodeProvider.FileExtension">
      <summary>Gets the file name extension to use when creating source code files.</summary>
      <returns>The file name extension to use for generated source code files.</returns>
    </member>
    <member name="P:Microsoft.VisualBasic.VBCodeProvider.LanguageOptions">
      <summary>Gets a language features identifier.</summary>
      <returns>A <see cref="T:System.CodeDom.Compiler.LanguageOptions" /> that indicates special features of the language.</returns>
    </member>
    <member name="T:System.CodeDom.CodeArgumentReferenceExpression">
      <summary>Represents a reference to the value of an argument passed to a method.</summary>
    </member>
    <member name="M:System.CodeDom.CodeArgumentReferenceExpression.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.CodeDom.CodeArgumentReferenceExpression" /> class.</summary>
    </member>
    <member name="M:System.CodeDom.CodeArgumentReferenceExpression.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.CodeDom.CodeArgumentReferenceExpression" /> class using the specified parameter name.</summary>
      <param name="parameterName">The name of the parameter to reference.</param>
    </member>
    <member name="P:System.CodeDom.CodeArgumentReferenceExpression.ParameterName">
      <summary>Gets or sets the name of the parameter this expression references.</summary>
      <returns>The name of the parameter to reference.</returns>
    </member>
    <member name="T:System.CodeDom.CodeArrayCreateExpression">
      <summary>Represents an expression that creates an array.</summary>
    </member>
    <member name="M:System.CodeDom.CodeArrayCreateExpression.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.CodeDom.CodeArrayCreateExpression" /> class.</summary>
    </member>
    <member name="M:System.CodeDom.CodeArrayCreateExpression.#ctor(System.CodeDom.CodeTypeReference,System.CodeDom.CodeExpression)">
      <summary>Initializes a new instance of the <see cref="T:System.CodeDom.CodeArrayCreateExpression" /> class using the specified array data type and code expression indicating the number of indexes for the array.</summary>
      <param name="createType">A <see cref="T:System.CodeDom.CodeTypeReference" /> indicating the data type of the array to create.</param>
      <param name="size">An expression that indicates the number of indexes of the array to create.</param>
    </member>
    <member name="M:System.CodeDom.CodeArrayCreateExpression.#ctor(System.CodeDom.CodeTypeReference,System.CodeDom.CodeExpression[])">
      <summary>Initializes a new instance of the <see cref="T:System.CodeDom.CodeArrayCreateExpression" /> class using the specified array data type and initialization expressions.</summary>
      <param name="createType">A <see cref="T:System.CodeDom.CodeTypeReference" /> that indicates the data type of the array to create.</param>
      <param name="initializers">An array of expressions to use to initialize the array.</param>
    </member>
    <member name="M:System.CodeDom.CodeArrayCreateExpression.#ctor(System.CodeDom.CodeTypeReference,System.Int32)">
      <summary>Initializes a new instance of the <see cref="T:System.CodeDom.CodeArrayCreateExpression" /> class using the specified array data type and number of indexes for the array.</summary>
      <param name="createType">A <see cref="T:System.CodeDom.CodeTypeReference" /> indicating the data type of the array to create.</param>
      <param name="size">The number of indexes of the array to create.</param>
    </member>
    <member name="M:System.CodeDom.CodeArrayCreateExpression.#ctor(System.String,System.CodeDom.CodeExpression)">
      <summary>Initializes a new instance of the <see cref="T:System.CodeDom.CodeArrayCreateExpression" /> class using the specified array data type name and code expression indicating the number of indexes for the array.</summary>
      <param name="createType">The name of the data type of the array to create.</param>
      <param name="size">An expression that indicates the number of indexes of the array to create.</param>
    </member>
    <member name="M:System.CodeDom.CodeArrayCreateExpression.#ctor(System.String,System.CodeDom.CodeExpression[])">
      <summary>Initializes a new instance of the <see cref="T:System.CodeDom.CodeArrayCreateExpression" /> class using the specified array data type name and initializers.</summary>
      <param name="createType">The name of the data type of the array to create.</param>
      <param name="initializers">An array of expressions to use to initialize the array.</param>
    </member>
    <member name="M:System.CodeDom.CodeArrayCreateExpression.#ctor(System.String,System.Int32)">
      <summary>Initializes a new instance of the <see cref="T:System.CodeDom.CodeArrayCreateExpression" /> class using the specified array data type name and number of indexes for the array.</summary>
      <param name="createType">The name of the data type of the array to create.</param>
      <param name="size">The number of indexes of the array to create.</param>
    </member>
    <member name="M:System.CodeDom.CodeArrayCreateExpression.#ctor(System.Type,System.CodeDom.CodeExpression)">
      <summary>Initializes a new instance of the <see cref="T:System.CodeDom.CodeArrayCreateExpression" /> class using the specified array data type and code expression indicating the number of indexes for the array.</summary>
      <param name="createType">The data type of the array to create.</param>
      <param name="size">An expression that indicates the number of indexes of the array to create.</param>
    </member>
    <member name="M:System.CodeDom.CodeArrayCreateExpression.#ctor(System.Type,System.CodeDom.CodeExpression[])">
      <summary>Initializes a new instance of the <see cref="T:System.CodeDom.CodeArrayCreateExpression" /> class using the specified array data type and initializers.</summary>
      <param name="createType">The data type of the array to create.</param>
      <param name="initializers">An array of expressions to use to initialize the array.</param>
    </member>
    <member name="M:System.CodeDom.CodeArrayCreateExpression.#ctor(System.Type,System.Int32)">
      <summary>Initializes a new instance of the <see cref="T:System.CodeDom.CodeArrayCreateExpression" /> class using the specified array data type and number of indexes for the array.</summary>
      <param name="createType">The data type of the array to create.</param>
      <param name="size">The number of indexes of the array to create.</param>
    </member>
    <member name="P:System.CodeDom.CodeArrayCreateExpression.CreateType">
      <summary>Gets or sets the type of array to create.</summary>
      <returns>A <see cref="T:System.CodeDom.CodeTypeReference" /> that indicates the type of the array.</returns>
    </member>
    <member name="P:System.CodeDom.CodeArrayCreateExpression.Initializers">
      <summary>Gets the initializers with which to initialize the array.</summary>
      <returns>A <see cref="T:System.CodeDom.CodeExpressionCollection" /> that indicates the initialization values.</returns>
    </member>
    <member name="P:System.CodeDom.CodeArrayCreateExpression.Size">
      <summary>Gets or sets the number of indexes in the array.</summary>
      <returns>The number of indexes in the array.</returns>
    </member>
    <member name="P:System.CodeDom.CodeArrayCreateExpression.SizeExpression">
      <summary>Gets or sets the expression that indicates the size of the array.</summary>
      <returns>A <see cref="T:System.CodeDom.CodeExpression" /> that indicates the size of the array.</returns>
    </member>
    <member name="T:System.CodeDom.CodeArrayIndexerExpression">
      <summary>Represents a reference to an index of an array.</summary>
    </member>
    <member name="M:System.CodeDom.CodeArrayIndexerExpression.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.CodeDom.CodeArrayIndexerExpression" /> class.</summary>
    </member>
    <member name="M:System.CodeDom.CodeArrayIndexerExpression.#ctor(System.CodeDom.CodeExpression,System.CodeDom.CodeExpression[])">
      <summary>Initializes a new instance of the <see cref="T:System.CodeDom.CodeArrayIndexerExpression" /> class using the specified target object and indexes.</summary>
      <param name="targetObject">A <see cref="T:System.CodeDom.CodeExpression" /> that indicates the array the indexer targets.</param>
      <param name="indices">The index or indexes to reference.</param>
    </member>
    <member name="P:System.CodeDom.CodeArrayIndexerExpression.Indices">
      <summary>Gets or sets the index or indexes of the indexer expression.</summary>
      <returns>A <see cref="T:System.CodeDom.CodeExpressionCollection" /> that indicates the index or indexes of the indexer expression.</returns>
    </member>
    <member name="P:System.CodeDom.CodeArrayIndexerExpression.TargetObject">
      <summary>Gets or sets the target object of the array indexer.</summary>
      <returns>A <see cref="T:System.CodeDom.CodeExpression" /> that represents the array being indexed.</returns>
    </member>
    <member name="T:System.CodeDom.CodeAssignStatement">
      <summary>Represents a simple assignment statement.</summary>
    </member>
    <member name="M:System.CodeDom.CodeAssignStatement.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.CodeDom.CodeAssignStatement" /> class.</summary>
    </member>
    <member name="M:System.CodeDom.CodeAssignStatement.#ctor(System.CodeDom.CodeExpression,System.CodeDom.CodeExpression)">
      <summary>Initializes a new instance of the <see cref="T:System.CodeDom.CodeAssignStatement" /> class using the specified expressions.</summary>
      <param name="left">The variable to assign to.</param>
      <param name="right">The value to assign.</param>
    </member>
    <member name="P:System.CodeDom.CodeAssignStatement.Left">
      <summary>Gets or sets the expression representing the object or reference to assign to.</summary>
      <returns>A <see cref="T:System.CodeDom.CodeExpression" /> that indicates the object or reference to assign to.</returns>
    </member>
    <member name="P:System.CodeDom.CodeAssignStatement.Right">
      <summary>Gets or sets the expression representing the object or reference to assign.</summary>
      <returns>A <see cref="T:System.CodeDom.CodeExpression" /> that indicates the object or reference to assign.</returns>
    </member>
    <member name="T:System.CodeDom.CodeAttachEventStatement">
      <summary>Represents a statement that attaches an event-handler delegate to an event.</summary>
    </member>
    <member name="M:System.CodeDom.CodeAttachEventStatement.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.CodeDom.CodeAttachEventStatement" /> class.</summary>
    </member>
    <member name="M:System.CodeDom.CodeAttachEventStatement.#ctor(System.CodeDom.CodeEventReferenceExpression,System.CodeDom.CodeExpression)">
      <summary>Initializes a new instance of the <see cref="T:System.CodeDom.CodeAttachEventStatement" /> class using the specified event and delegate.</summary>
      <param name="eventRef">A <see cref="T:System.CodeDom.CodeEventReferenceExpression" /> that indicates the event to attach an event handler to.</param>
      <param name="listener">A <see cref="T:System.CodeDom.CodeExpression" /> that indicates the new event handler.</param>
    </member>
    <member name="M:System.CodeDom.CodeAttachEventStatement.#ctor(System.CodeDom.CodeExpression,System.String,System.CodeDom.CodeExpression)">
      <summary>Initializes a new instance of the <see cref="T:System.CodeDom.CodeAttachEventStatement" /> class using the specified object containing the event, event name, and event-handler delegate.</summary>
      <param name="targetObject">A <see cref="T:System.CodeDom.CodeExpression" /> that indicates the object that contains the event.</param>
      <param name="eventName">The name of the event to attach an event handler to.</param>
      <param name="listener">A <see cref="T:System.CodeDom.CodeExpression" /> that indicates the new event handler.</param>
    </member>
    <member name="P:System.CodeDom.CodeAttachEventStatement.Event">
      <summary>Gets or sets the event to attach an event-handler delegate to.</summary>
      <returns>A <see cref="T:System.CodeDom.CodeEventReferenceExpression" /> that indicates the event to attach an event handler to.</returns>
    </member>
    <member name="P:System.CodeDom.CodeAttachEventStatement.Listener">
      <summary>Gets or sets the new event-handler delegate to attach to the event.</summary>
      <returns>A <see cref="T:System.CodeDom.CodeExpression" /> that indicates the new event handler to attach.</returns>
    </member>
    <member name="T:System.CodeDom.CodeAttributeArgument">
      <summary>Represents an argument used in a metadata attribute declaration.</summary>
    </member>
    <member name="M:System.CodeDom.CodeAttributeArgument.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.CodeDom.CodeAttributeArgument" /> class.</summary>
    </member>
    <member name="M:System.CodeDom.CodeAttributeArgument.#ctor(System.CodeDom.CodeExpression)">
      <summary>Initializes a new instance of the <see cref="T:System.CodeDom.CodeAttributeArgument" /> class using the specified value.</summary>
      <param name="value">A <see cref="T:System.CodeDom.CodeExpression" /> that represents the value of the argument.</param>
    </member>
    <member name="M:System.CodeDom.CodeAttributeArgument.#ctor(System.String,System.CodeDom.CodeExpression)">
      <summary>Initializes a new instance of the <see cref="T:System.CodeDom.CodeAttributeArgument" /> class using the specified name and value.</summary>
      <param name="name">The name of the attribute property the argument applies to.</param>
      <param name="value">A <see cref="T:System.CodeDom.CodeExpression" /> that represents the value of the argument.</param>
    </member>
    <member name="P:System.CodeDom.CodeAttributeArgument.Name">
      <summary>Gets or sets the name of the attribute.</summary>
      <returns>The name of the attribute property the argument is for.</returns>
    </member>
    <member name="P:System.CodeDom.CodeAttributeArgument.Value">
      <summary>Gets or sets the value for the attribute argument.</summary>
      <returns>A <see cref="T:System.CodeDom.CodeExpression" /> that indicates the value for the attribute argument.</returns>
    </member>
    <member name="T:System.CodeDom.CodeAttributeArgumentCollection">
      <summary>Represents a collection of <see cref="T:System.CodeDom.CodeAttributeArgument" /> objects.</summary>
    </member>
    <member name="M:System.CodeDom.CodeAttributeArgumentCollection.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.CodeDom.CodeAttributeArgumentCollection" /> class.</summary>
    </member>
    <member name="M:System.CodeDom.CodeAttributeArgumentCollection.#ctor(System.CodeDom.CodeAttributeArgument[])">
      <summary>Initializes a new instance of the <see cref="T:System.CodeDom.CodeAttributeArgumentCollection" /> class containing the specified array of <see cref="T:System.CodeDom.CodeAttributeArgument" /> objects.</summary>
      <param name="value">An array of <see cref="T:System.CodeDom.CodeAttributeArgument" /> objects with which to initialize the collection.</param>
      <exception cref="T:System.ArgumentNullException">One or more objects in the array are <see langword="null" />.</exception>
    </member>
    <member name="M:System.CodeDom.CodeAttributeArgumentCollection.#ctor(System.CodeDom.CodeAttributeArgumentCollection)">
      <summary>Initializes a new instance of the <see cref="T:System.CodeDom.CodeAttributeArgumentCollection" /> class containing the elements of the specified source collection.</summary>
      <param name="value">A <see cref="T:System.CodeDom.CodeAttributeArgumentCollection" /> with which to initialize the collection.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.CodeDom.CodeAttributeArgumentCollection.Add(System.CodeDom.CodeAttributeArgument)">
      <summary>Adds the specified <see cref="T:System.CodeDom.CodeAttributeArgument" /> object to the collection.</summary>
      <param name="value">The <see cref="T:System.CodeDom.CodeAttributeArgument" /> object to add.</param>
      <returns>The index at which the new element was inserted.</returns>
    </member>
    <member name="M:System.CodeDom.CodeAttributeArgumentCollection.AddRange(System.CodeDom.CodeAttributeArgument[])">
      <summary>Copies the elements of the specified <see cref="T:System.CodeDom.CodeAttributeArgument" /> array to the end of the collection.</summary>
      <param name="value">An array of type <see cref="T:System.CodeDom.CodeAttributeArgument" /> that contains the objects to add to the collection.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.CodeDom.CodeAttributeArgumentCollection.AddRange(System.CodeDom.CodeAttributeArgumentCollection)">
      <summary>Copies the contents of another <see cref="T:System.CodeDom.CodeAttributeArgumentCollection" /> object to the end of the collection.</summary>
      <param name="value">A <see cref="T:System.CodeDom.CodeAttributeArgumentCollection" /> that contains the objects to add to the collection.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.CodeDom.CodeAttributeArgumentCollection.Contains(System.CodeDom.CodeAttributeArgument)">
      <summary>Gets a value that indicates whether the collection contains the specified <see cref="T:System.CodeDom.CodeAttributeArgument" /> object.</summary>
      <param name="value">The <see cref="T:System.CodeDom.CodeAttributeArgument" /> object to locate in the collection.</param>
      <returns>
        <see langword="true" /> if the collection contains the specified object; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.CodeDom.CodeAttributeArgumentCollection.CopyTo(System.CodeDom.CodeAttributeArgument[],System.Int32)">
      <summary>Copies the collection objects to a one-dimensional <see cref="T:System.Array" /> instance beginning at the specified index.</summary>
      <param name="array">The one-dimensional <see cref="T:System.Array" /> that is the destination of the values copied from the collection.</param>
      <param name="index">The index of the array at which to begin inserting.</param>
      <exception cref="T:System.ArgumentException">The destination array is multidimensional.  
  
 -or-  
  
 The number of elements in the <see cref="T:System.CodeDom.CodeAttributeArgumentCollection" /> is greater than the available space between the index of the target array specified by the <paramref name="index" /> parameter and the end of the target array.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="array" /> parameter is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="index" /> parameter is less than the target array's minimum index.</exception>
    </member>
    <member name="M:System.CodeDom.CodeAttributeArgumentCollection.IndexOf(System.CodeDom.CodeAttributeArgument)">
      <summary>Gets the index of the specified <see cref="T:System.CodeDom.CodeAttributeArgument" /> object in the collection, if it exists in the collection.</summary>
      <param name="value">The <see cref="T:System.CodeDom.CodeAttributeArgument" /> object to locate in the collection.</param>
      <returns>The index of the specified object, if found, in the collection; otherwise, -1.</returns>
    </member>
    <member name="M:System.CodeDom.CodeAttributeArgumentCollection.Insert(System.Int32,System.CodeDom.CodeAttributeArgument)">
      <summary>Inserts the specified <see cref="T:System.CodeDom.CodeAttributeArgument" /> object into the collection at the specified index.</summary>
      <param name="index">The zero-based index where the specified object should be inserted.</param>
      <param name="value">The <see cref="T:System.CodeDom.CodeAttributeArgument" /> object to insert.</param>
    </member>
    <member name="M:System.CodeDom.CodeAttributeArgumentCollection.Remove(System.CodeDom.CodeAttributeArgument)">
      <summary>Removes the specified <see cref="T:System.CodeDom.CodeAttributeArgument" /> object from the collection.</summary>
      <param name="value">The <see cref="T:System.CodeDom.CodeAttributeArgument" /> object to remove from the collection.</param>
      <exception cref="T:System.ArgumentException">The specified object is not found in the collection.</exception>
    </member>
    <member name="P:System.CodeDom.CodeAttributeArgumentCollection.Item(System.Int32)">
      <summary>Gets or sets the <see cref="T:System.CodeDom.CodeAttributeArgument" /> object at the specified index in the collection.</summary>
      <param name="index">The index of the collection to access.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="index" /> parameter is outside the valid range of indexes for the collection.</exception>
      <returns>A <see cref="T:System.CodeDom.CodeAttributeArgument" /> at each valid index.</returns>
    </member>
    <member name="T:System.CodeDom.CodeAttributeDeclaration">
      <summary>Represents an attribute declaration.</summary>
    </member>
    <member name="M:System.CodeDom.CodeAttributeDeclaration.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.CodeDom.CodeAttributeDeclaration" /> class.</summary>
    </member>
    <member name="M:System.CodeDom.CodeAttributeDeclaration.#ctor(System.CodeDom.CodeTypeReference)">
      <summary>Initializes a new instance of the <see cref="T:System.CodeDom.CodeAttributeDeclaration" /> class using the specified code type reference.</summary>
      <param name="attributeType">The <see cref="T:System.CodeDom.CodeTypeReference" /> that identifies the attribute.</param>
    </member>
    <member name="M:System.CodeDom.CodeAttributeDeclaration.#ctor(System.CodeDom.CodeTypeReference,System.CodeDom.CodeAttributeArgument[])">
      <summary>Initializes a new instance of the <see cref="T:System.CodeDom.CodeAttributeDeclaration" /> class using the specified code type reference and arguments.</summary>
      <param name="attributeType">The <see cref="T:System.CodeDom.CodeTypeReference" /> that identifies the attribute.</param>
      <param name="arguments">An array of type <see cref="T:System.CodeDom.CodeAttributeArgument" /> that contains the arguments for the attribute.</param>
    </member>
    <member name="M:System.CodeDom.CodeAttributeDeclaration.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.CodeDom.CodeAttributeDeclaration" /> class using the specified name.</summary>
      <param name="name">The name of the attribute.</param>
    </member>
    <member name="M:System.CodeDom.CodeAttributeDeclaration.#ctor(System.String,System.CodeDom.CodeAttributeArgument[])">
      <summary>Initializes a new instance of the <see cref="T:System.CodeDom.CodeAttributeDeclaration" /> class using the specified name and arguments.</summary>
      <param name="name">The name of the attribute.</param>
      <param name="arguments">An array of type <see cref="T:System.CodeDom.CodeAttributeArgument" /> that contains the arguments for the attribute.</param>
    </member>
    <member name="P:System.CodeDom.CodeAttributeDeclaration.Arguments">
      <summary>Gets the arguments for the attribute.</summary>
      <returns>A <see cref="T:System.CodeDom.CodeAttributeArgumentCollection" /> that contains the arguments for the attribute.</returns>
    </member>
    <member name="P:System.CodeDom.CodeAttributeDeclaration.AttributeType">
      <summary>Gets the code type reference for the code attribute declaration.</summary>
      <returns>A <see cref="T:System.CodeDom.CodeTypeReference" /> that identifies the <see cref="T:System.CodeDom.CodeAttributeDeclaration" />.</returns>
    </member>
    <member name="P:System.CodeDom.CodeAttributeDeclaration.Name">
      <summary>Gets or sets the name of the attribute being declared.</summary>
      <returns>The name of the attribute.</returns>
    </member>
    <member name="T:System.CodeDom.CodeAttributeDeclarationCollection">
      <summary>Represents a collection of <see cref="T:System.CodeDom.CodeAttributeDeclaration" /> objects.</summary>
    </member>
    <member name="M:System.CodeDom.CodeAttributeDeclarationCollection.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.CodeDom.CodeAttributeDeclarationCollection" /> class.</summary>
    </member>
    <member name="M:System.CodeDom.CodeAttributeDeclarationCollection.#ctor(System.CodeDom.CodeAttributeDeclaration[])">
      <summary>Initializes a new instance of the <see cref="T:System.CodeDom.CodeAttributeDeclarationCollection" /> class containing the specified array of <see cref="T:System.CodeDom.CodeAttributeDeclaration" /> objects.</summary>
      <param name="value">An array of <see cref="T:System.CodeDom.CodeAttributeDeclaration" /> objects with which to initialize the collection.</param>
      <exception cref="T:System.ArgumentNullException">One or more objects in the array are <see langword="null" />.</exception>
    </member>
    <member name="M:System.CodeDom.CodeAttributeDeclarationCollection.#ctor(System.CodeDom.CodeAttributeDeclarationCollection)">
      <summary>Initializes a new instance of the <see cref="T:System.CodeDom.CodeAttributeDeclarationCollection" /> class containing the elements of the specified source collection.</summary>
      <param name="value">A <see cref="T:System.CodeDom.CodeAttributeDeclarationCollection" /> with which to initialize the collection.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.CodeDom.CodeAttributeDeclarationCollection.Add(System.CodeDom.CodeAttributeDeclaration)">
      <summary>Adds a <see cref="T:System.CodeDom.CodeAttributeDeclaration" /> object with the specified value to the collection.</summary>
      <param name="value">The <see cref="T:System.CodeDom.CodeAttributeDeclaration" /> object to add.</param>
      <returns>The index at which the new element was inserted.</returns>
    </member>
    <member name="M:System.CodeDom.CodeAttributeDeclarationCollection.AddRange(System.CodeDom.CodeAttributeDeclaration[])">
      <summary>Copies the elements of the specified <see cref="T:System.CodeDom.CodeAttributeDeclaration" /> array to the end of the collection.</summary>
      <param name="value">An array of type <see cref="T:System.CodeDom.CodeAttributeDeclaration" /> that contains the objects to add to the collection.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.CodeDom.CodeAttributeDeclarationCollection.AddRange(System.CodeDom.CodeAttributeDeclarationCollection)">
      <summary>Copies the contents of another <see cref="T:System.CodeDom.CodeAttributeDeclarationCollection" /> object to the end of the collection.</summary>
      <param name="value">A <see cref="T:System.CodeDom.CodeAttributeDeclarationCollection" /> that contains the objects to add to the collection.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.CodeDom.CodeAttributeDeclarationCollection.Contains(System.CodeDom.CodeAttributeDeclaration)">
      <summary>Gets or sets a value that indicates whether the collection contains the specified <see cref="T:System.CodeDom.CodeAttributeDeclaration" /> object.</summary>
      <param name="value">The <see cref="T:System.CodeDom.CodeAttributeDeclaration" /> object to locate.</param>
      <returns>
        <see langword="true" /> if the collection contains the specified object; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.CodeDom.CodeAttributeDeclarationCollection.CopyTo(System.CodeDom.CodeAttributeDeclaration[],System.Int32)">
      <summary>Copies the collection objects to a one-dimensional <see cref="T:System.Array" /> instance beginning at the specified index.</summary>
      <param name="array">The one-dimensional <see cref="T:System.Array" /> that is the destination of the values copied from the collection.</param>
      <param name="index">The index of the array at which to begin inserting.</param>
      <exception cref="T:System.ArgumentException">The destination array is multidimensional.  
  
 -or-  
  
 The number of elements in the <see cref="T:System.CodeDom.CodeAttributeDeclarationCollection" /> is greater than the available space between the index of the target array specified by the <paramref name="index" /> parameter and the end of the target array.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="array" /> parameter is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="index" /> parameter is less than the target array's minimum index.</exception>
    </member>
    <member name="M:System.CodeDom.CodeAttributeDeclarationCollection.IndexOf(System.CodeDom.CodeAttributeDeclaration)">
      <summary>Gets the index of the specified <see cref="T:System.CodeDom.CodeAttributeDeclaration" /> object in the collection, if it exists in the collection.</summary>
      <param name="value">The <see cref="T:System.CodeDom.CodeAttributeDeclaration" /> object to locate in the collection.</param>
      <returns>The index in the collection of the specified object, if found; otherwise, -1.</returns>
    </member>
    <member name="M:System.CodeDom.CodeAttributeDeclarationCollection.Insert(System.Int32,System.CodeDom.CodeAttributeDeclaration)">
      <summary>Inserts the specified <see cref="T:System.CodeDom.CodeAttributeDeclaration" /> object into the collection at the specified index.</summary>
      <param name="index">The zero-based index where the specified object should be inserted.</param>
      <param name="value">The <see cref="T:System.CodeDom.CodeAttributeDeclaration" /> object to insert.</param>
    </member>
    <member name="M:System.CodeDom.CodeAttributeDeclarationCollection.Remove(System.CodeDom.CodeAttributeDeclaration)">
      <summary>Removes the specified <see cref="T:System.CodeDom.CodeAttributeDeclaration" /> object from the collection.</summary>
      <param name="value">The <see cref="T:System.CodeDom.CodeAttributeDeclaration" /> object to remove from the collection.</param>
      <exception cref="T:System.ArgumentException">The specified object is not found in the collection.</exception>
    </member>
    <member name="P:System.CodeDom.CodeAttributeDeclarationCollection.Item(System.Int32)">
      <summary>Gets or sets the <see cref="T:System.CodeDom.CodeAttributeDeclaration" /> object at the specified index.</summary>
      <param name="index">The index of the collection to access.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="index" /> parameter is outside the valid range of indexes for the collection.</exception>
      <returns>A <see cref="T:System.CodeDom.CodeAttributeDeclaration" /> at each valid index.</returns>
    </member>
    <member name="T:System.CodeDom.CodeBaseReferenceExpression">
      <summary>Represents a reference to the base class.</summary>
    </member>
    <member name="M:System.CodeDom.CodeBaseReferenceExpression.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.CodeDom.CodeBaseReferenceExpression" /> class.</summary>
    </member>
    <member name="T:System.CodeDom.CodeBinaryOperatorExpression">
      <summary>Represents an expression that consists of a binary operation between two expressions.</summary>
    </member>
    <member name="M:System.CodeDom.CodeBinaryOperatorExpression.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.CodeDom.CodeBinaryOperatorExpression" /> class.</summary>
    </member>
    <member name="M:System.CodeDom.CodeBinaryOperatorExpression.#ctor(System.CodeDom.CodeExpression,System.CodeDom.CodeBinaryOperatorType,System.CodeDom.CodeExpression)">
      <summary>Initializes a new instance of the <see cref="T:System.CodeDom.CodeBinaryOperatorExpression" /> class using the specified parameters.</summary>
      <param name="left">The <see cref="T:System.CodeDom.CodeExpression" /> on the left of the operator.</param>
      <param name="op">A <see cref="T:System.CodeDom.CodeBinaryOperatorType" /> indicating the type of operator.</param>
      <param name="right">The <see cref="T:System.CodeDom.CodeExpression" /> on the right of the operator.</param>
    </member>
    <member name="P:System.CodeDom.CodeBinaryOperatorExpression.Left">
      <summary>Gets or sets the code expression on the left of the operator.</summary>
      <returns>A <see cref="T:System.CodeDom.CodeExpression" /> that indicates the left operand.</returns>
    </member>
    <member name="P:System.CodeDom.CodeBinaryOperatorExpression.Operator">
      <summary>Gets or sets the operator in the binary operator expression.</summary>
      <returns>A <see cref="T:System.CodeDom.CodeBinaryOperatorType" /> that indicates the type of operator in the expression.</returns>
    </member>
    <member name="P:System.CodeDom.CodeBinaryOperatorExpression.Right">
      <summary>Gets or sets the code expression on the right of the operator.</summary>
      <returns>A <see cref="T:System.CodeDom.CodeExpression" /> that indicates the right operand.</returns>
    </member>
    <member name="T:System.CodeDom.CodeBinaryOperatorType">
      <summary>Defines identifiers for supported binary operators.</summary>
    </member>
    <member name="F:System.CodeDom.CodeBinaryOperatorType.Add">
      <summary>Addition operator.</summary>
    </member>
    <member name="F:System.CodeDom.CodeBinaryOperatorType.Assign">
      <summary>Assignment operator.</summary>
    </member>
    <member name="F:System.CodeDom.CodeBinaryOperatorType.BitwiseAnd">
      <summary>Bitwise and operator.</summary>
    </member>
    <member name="F:System.CodeDom.CodeBinaryOperatorType.BitwiseOr">
      <summary>Bitwise or operator.</summary>
    </member>
    <member name="F:System.CodeDom.CodeBinaryOperatorType.BooleanAnd">
      <summary>Boolean and operator. This represents a short circuiting operator. A short circuiting operator will evaluate only as many expressions as necessary before returning a correct value.</summary>
    </member>
    <member name="F:System.CodeDom.CodeBinaryOperatorType.BooleanOr">
      <summary>Boolean or operator. This represents a short circuiting operator. A short circuiting operator will evaluate only as many expressions as necessary before returning a correct value.</summary>
    </member>
    <member name="F:System.CodeDom.CodeBinaryOperatorType.Divide">
      <summary>Division operator.</summary>
    </member>
    <member name="F:System.CodeDom.CodeBinaryOperatorType.GreaterThan">
      <summary>Greater than operator.</summary>
    </member>
    <member name="F:System.CodeDom.CodeBinaryOperatorType.GreaterThanOrEqual">
      <summary>Greater than or equal operator.</summary>
    </member>
    <member name="F:System.CodeDom.CodeBinaryOperatorType.IdentityEquality">
      <summary>Identity equal operator.</summary>
    </member>
    <member name="F:System.CodeDom.CodeBinaryOperatorType.IdentityInequality">
      <summary>Identity not equal operator.</summary>
    </member>
    <member name="F:System.CodeDom.CodeBinaryOperatorType.LessThan">
      <summary>Less than operator.</summary>
    </member>
    <member name="F:System.CodeDom.CodeBinaryOperatorType.LessThanOrEqual">
      <summary>Less than or equal operator.</summary>
    </member>
    <member name="F:System.CodeDom.CodeBinaryOperatorType.Modulus">
      <summary>Modulus operator.</summary>
    </member>
    <member name="F:System.CodeDom.CodeBinaryOperatorType.Multiply">
      <summary>Multiplication operator.</summary>
    </member>
    <member name="F:System.CodeDom.CodeBinaryOperatorType.Subtract">
      <summary>Subtraction operator.</summary>
    </member>
    <member name="F:System.CodeDom.CodeBinaryOperatorType.ValueEquality">
      <summary>Value equal operator.</summary>
    </member>
    <member name="T:System.CodeDom.CodeCastExpression">
      <summary>Represents an expression cast to a data type or interface.</summary>
    </member>
    <member name="M:System.CodeDom.CodeCastExpression.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.CodeDom.CodeCastExpression" /> class.</summary>
    </member>
    <member name="M:System.CodeDom.CodeCastExpression.#ctor(System.CodeDom.CodeTypeReference,System.CodeDom.CodeExpression)">
      <summary>Initializes a new instance of the <see cref="T:System.CodeDom.CodeCastExpression" /> class using the specified destination type and expression.</summary>
      <param name="targetType">A <see cref="T:System.CodeDom.CodeTypeReference" /> that indicates the destination type of the cast.</param>
      <param name="expression">The <see cref="T:System.CodeDom.CodeExpression" /> to cast.</param>
    </member>
    <member name="M:System.CodeDom.CodeCastExpression.#ctor(System.String,System.CodeDom.CodeExpression)">
      <summary>Initializes a new instance of the <see cref="T:System.CodeDom.CodeCastExpression" /> class using the specified destination type and expression.</summary>
      <param name="targetType">The name of the destination type of the cast.</param>
      <param name="expression">The <see cref="T:System.CodeDom.CodeExpression" /> to cast.</param>
    </member>
    <member name="M:System.CodeDom.CodeCastExpression.#ctor(System.Type,System.CodeDom.CodeExpression)">
      <summary>Initializes a new instance of the <see cref="T:System.CodeDom.CodeCastExpression" /> class using the specified destination type and expression.</summary>
      <param name="targetType">The destination data type of the cast.</param>
      <param name="expression">The <see cref="T:System.CodeDom.CodeExpression" /> to cast.</param>
    </member>
    <member name="P:System.CodeDom.CodeCastExpression.Expression">
      <summary>Gets or sets the expression to cast.</summary>
      <returns>A <see cref="T:System.CodeDom.CodeExpression" /> that indicates the code to cast.</returns>
    </member>
    <member name="P:System.CodeDom.CodeCastExpression.TargetType">
      <summary>Gets or sets the destination type of the cast.</summary>
      <returns>A <see cref="T:System.CodeDom.CodeTypeReference" /> that indicates the destination type to cast to.</returns>
    </member>
    <member name="T:System.CodeDom.CodeCatchClause">
      <summary>Represents a <see langword="catch" /> exception block of a <see langword="try/catch" /> statement.</summary>
    </member>
    <member name="M:System.CodeDom.CodeCatchClause.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.CodeDom.CodeCatchClause" /> class.</summary>
    </member>
    <member name="M:System.CodeDom.CodeCatchClause.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.CodeDom.CodeCatchClause" /> class using the specified local variable name for the exception.</summary>
      <param name="localName">The name of the local variable declared in the catch clause for the exception. This is optional.</param>
    </member>
    <member name="M:System.CodeDom.CodeCatchClause.#ctor(System.String,System.CodeDom.CodeTypeReference)">
      <summary>Initializes a new instance of the <see cref="T:System.CodeDom.CodeCatchClause" /> class using the specified local variable name for the exception and exception type.</summary>
      <param name="localName">The name of the local variable declared in the catch clause for the exception. This is optional.</param>
      <param name="catchExceptionType">A <see cref="T:System.CodeDom.CodeTypeReference" /> that indicates the type of exception to catch.</param>
    </member>
    <member name="M:System.CodeDom.CodeCatchClause.#ctor(System.String,System.CodeDom.CodeTypeReference,System.CodeDom.CodeStatement[])">
      <summary>Initializes a new instance of the <see cref="T:System.CodeDom.CodeCatchClause" /> class using the specified local variable name for the exception, exception type and statement collection.</summary>
      <param name="localName">The name of the local variable declared in the catch clause for the exception. This is optional.</param>
      <param name="catchExceptionType">A <see cref="T:System.CodeDom.CodeTypeReference" /> that indicates the type of exception to catch.</param>
      <param name="statements">An array of <see cref="T:System.CodeDom.CodeStatement" /> objects that represent the contents of the catch block.</param>
    </member>
    <member name="P:System.CodeDom.CodeCatchClause.CatchExceptionType">
      <summary>Gets or sets the type of the exception to handle with the catch block.</summary>
      <returns>A <see cref="T:System.CodeDom.CodeTypeReference" /> that indicates the type of the exception to handle.</returns>
    </member>
    <member name="P:System.CodeDom.CodeCatchClause.LocalName">
      <summary>Gets or sets the variable name of the exception that the <see langword="catch" /> clause handles.</summary>
      <returns>The name for the exception variable that the <see langword="catch" /> clause handles.</returns>
    </member>
    <member name="P:System.CodeDom.CodeCatchClause.Statements">
      <summary>Gets the statements within the catch block.</summary>
      <returns>A <see cref="T:System.CodeDom.CodeStatementCollection" /> containing the statements within the catch block.</returns>
    </member>
    <member name="T:System.CodeDom.CodeCatchClauseCollection">
      <summary>Represents a collection of <see cref="T:System.CodeDom.CodeCatchClause" /> objects.</summary>
    </member>
    <member name="M:System.CodeDom.CodeCatchClauseCollection.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.CodeDom.CodeCatchClauseCollection" /> class.</summary>
    </member>
    <member name="M:System.CodeDom.CodeCatchClauseCollection.#ctor(System.CodeDom.CodeCatchClause[])">
      <summary>Initializes a new instance of the <see cref="T:System.CodeDom.CodeCatchClauseCollection" /> class containing the specified array of <see cref="T:System.CodeDom.CodeCatchClause" /> objects.</summary>
      <param name="value">An array of <see cref="T:System.CodeDom.CodeCatchClause" /> objects with which to initialize the collection.</param>
      <exception cref="T:System.ArgumentNullException">One or more objects in the array are <see langword="null" />.</exception>
    </member>
    <member name="M:System.CodeDom.CodeCatchClauseCollection.#ctor(System.CodeDom.CodeCatchClauseCollection)">
      <summary>Initializes a new instance of the <see cref="T:System.CodeDom.CodeCatchClauseCollection" /> class containing the elements of the specified source collection.</summary>
      <param name="value">A <see cref="T:System.CodeDom.CodeCatchClauseCollection" /> with which to initialize the collection.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.CodeDom.CodeCatchClauseCollection.Add(System.CodeDom.CodeCatchClause)">
      <summary>Adds the specified <see cref="T:System.CodeDom.CodeCatchClause" /> object to the collection.</summary>
      <param name="value">The <see cref="T:System.CodeDom.CodeCatchClause" /> object to add.</param>
      <returns>The index at which the new element was inserted.</returns>
    </member>
    <member name="M:System.CodeDom.CodeCatchClauseCollection.AddRange(System.CodeDom.CodeCatchClause[])">
      <summary>Copies the elements of the specified <see cref="T:System.CodeDom.CodeCatchClause" /> array to the end of the collection.</summary>
      <param name="value">An array of type <see cref="T:System.CodeDom.CodeCatchClause" /> that contains the objects to add to the collection.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.CodeDom.CodeCatchClauseCollection.AddRange(System.CodeDom.CodeCatchClauseCollection)">
      <summary>Copies the contents of another <see cref="T:System.CodeDom.CodeCatchClauseCollection" /> object to the end of the collection.</summary>
      <param name="value">A <see cref="T:System.CodeDom.CodeCatchClauseCollection" /> that contains the objects to add to the collection.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.CodeDom.CodeCatchClauseCollection.Contains(System.CodeDom.CodeCatchClause)">
      <summary>Gets a value that indicates whether the collection contains the specified <see cref="T:System.CodeDom.CodeCatchClause" /> object.</summary>
      <param name="value">The <see cref="T:System.CodeDom.CodeCatchClause" /> object to locate in the collection.</param>
      <returns>
        <see langword="true" /> if the collection contains the specified object; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.CodeDom.CodeCatchClauseCollection.CopyTo(System.CodeDom.CodeCatchClause[],System.Int32)">
      <summary>Copies the collection objects to a one-dimensional <see cref="T:System.Array" /> instance beginning at the specified index.</summary>
      <param name="array">The one-dimensional <see cref="T:System.Array" /> that is the destination of the values copied from the collection.</param>
      <param name="index">The index of the array at which to begin inserting.</param>
      <exception cref="T:System.ArgumentException">The destination array is multidimensional.  
  
 -or-  
  
 The number of elements in the <see cref="T:System.CodeDom.CodeCatchClauseCollection" /> is greater than the available space between the index of the target array specified by the <paramref name="index" /> parameter and the end of the target array.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="array" /> parameter is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="index" /> parameter is less than the target array's minimum index.</exception>
    </member>
    <member name="M:System.CodeDom.CodeCatchClauseCollection.IndexOf(System.CodeDom.CodeCatchClause)">
      <summary>Gets the index of the specified <see cref="T:System.CodeDom.CodeCatchClause" /> object in the collection, if it exists in the collection.</summary>
      <param name="value">The <see cref="T:System.CodeDom.CodeCatchClause" /> object to locate in the collection.</param>
      <returns>The index of the specified object, if found, in the collection; otherwise, -1.</returns>
    </member>
    <member name="M:System.CodeDom.CodeCatchClauseCollection.Insert(System.Int32,System.CodeDom.CodeCatchClause)">
      <summary>Inserts the specified <see cref="T:System.CodeDom.CodeCatchClause" /> object into the collection at the specified index.</summary>
      <param name="index">The zero-based index where the specified object should be inserted.</param>
      <param name="value">The <see cref="T:System.CodeDom.CodeCatchClause" /> object to insert.</param>
    </member>
    <member name="M:System.CodeDom.CodeCatchClauseCollection.Remove(System.CodeDom.CodeCatchClause)">
      <summary>Removes the specified <see cref="T:System.CodeDom.CodeCatchClause" /> object from the collection.</summary>
      <param name="value">The <see cref="T:System.CodeDom.CodeCatchClause" /> object to remove from the collection.</param>
      <exception cref="T:System.ArgumentException">The specified object is not found in the collection.</exception>
    </member>
    <member name="P:System.CodeDom.CodeCatchClauseCollection.Item(System.Int32)">
      <summary>Gets or sets the <see cref="T:System.CodeDom.CodeCatchClause" /> object at the specified index in the collection.</summary>
      <param name="index">The index of the collection to access.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="index" /> parameter is outside the valid range of indexes for the collection.</exception>
      <returns>A <see cref="T:System.CodeDom.CodeCatchClause" /> object at each valid index.</returns>
    </member>
    <member name="T:System.CodeDom.CodeChecksumPragma">
      <summary>Represents a code checksum pragma code entity.</summary>
    </member>
    <member name="M:System.CodeDom.CodeChecksumPragma.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.CodeDom.CodeChecksumPragma" /> class.</summary>
    </member>
    <member name="M:System.CodeDom.CodeChecksumPragma.#ctor(System.String,System.Guid,System.Byte[])">
      <summary>Initializes a new instance of the <see cref="T:System.CodeDom.CodeChecksumPragma" /> class using a file name, a GUID representing the checksum algorithm, and a byte stream representing the checksum data.</summary>
      <param name="fileName">The path to the checksum file.</param>
      <param name="checksumAlgorithmId">A <see cref="T:System.Guid" /> that identifies the checksum algorithm to use.</param>
      <param name="checksumData">A byte array that contains the checksum data.</param>
    </member>
    <member name="P:System.CodeDom.CodeChecksumPragma.ChecksumAlgorithmId">
      <summary>Gets or sets a GUID that identifies the checksum algorithm to use.</summary>
      <returns>A <see cref="T:System.Guid" /> that identifies the checksum algorithm to use.</returns>
    </member>
    <member name="P:System.CodeDom.CodeChecksumPragma.ChecksumData">
      <summary>Gets or sets the value of the data for the checksum calculation.</summary>
      <returns>A byte array that contains the data for the checksum calculation.</returns>
    </member>
    <member name="P:System.CodeDom.CodeChecksumPragma.FileName">
      <summary>Gets or sets the path to the checksum file.</summary>
      <returns>The path to the checksum file.</returns>
    </member>
    <member name="T:System.CodeDom.CodeComment">
      <summary>Represents a comment.</summary>
    </member>
    <member name="M:System.CodeDom.CodeComment.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.CodeDom.CodeComment" /> class.</summary>
    </member>
    <member name="M:System.CodeDom.CodeComment.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.CodeDom.CodeComment" /> class with the specified text as contents.</summary>
      <param name="text">The contents of the comment.</param>
    </member>
    <member name="M:System.CodeDom.CodeComment.#ctor(System.String,System.Boolean)">
      <summary>Initializes a new instance of the <see cref="T:System.CodeDom.CodeComment" /> class using the specified text and documentation comment flag.</summary>
      <param name="text">The contents of the comment.</param>
      <param name="docComment">
        <see langword="true" /> if the comment is a documentation comment; otherwise, <see langword="false" />.</param>
    </member>
    <member name="P:System.CodeDom.CodeComment.DocComment">
      <summary>Gets or sets a value that indicates whether the comment is a documentation comment.</summary>
      <returns>
        <see langword="true" /> if the comment is a documentation comment; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.CodeDom.CodeComment.Text">
      <summary>Gets or sets the text of the comment.</summary>
      <returns>A string containing the comment text.</returns>
    </member>
    <member name="T:System.CodeDom.CodeCommentStatement">
      <summary>Represents a statement consisting of a single comment.</summary>
    </member>
    <member name="M:System.CodeDom.CodeCommentStatement.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.CodeDom.CodeCommentStatement" /> class.</summary>
    </member>
    <member name="M:System.CodeDom.CodeCommentStatement.#ctor(System.CodeDom.CodeComment)">
      <summary>Initializes a new instance of the <see cref="T:System.CodeDom.CodeCommentStatement" /> class using the specified comment.</summary>
      <param name="comment">A <see cref="T:System.CodeDom.CodeComment" /> that indicates the comment.</param>
    </member>
    <member name="M:System.CodeDom.CodeCommentStatement.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.CodeDom.CodeCommentStatement" /> class using the specified text as contents.</summary>
      <param name="text">The contents of the comment.</param>
    </member>
    <member name="M:System.CodeDom.CodeCommentStatement.#ctor(System.String,System.Boolean)">
      <summary>Initializes a new instance of the <see cref="T:System.CodeDom.CodeCommentStatement" /> class using the specified text and documentation comment flag.</summary>
      <param name="text">The contents of the comment.</param>
      <param name="docComment">
        <see langword="true" /> if the comment is a documentation comment; otherwise, <see langword="false" />.</param>
    </member>
    <member name="P:System.CodeDom.CodeCommentStatement.Comment">
      <summary>Gets or sets the contents of the comment.</summary>
      <returns>A <see cref="T:System.CodeDom.CodeComment" /> that indicates the comment.</returns>
    </member>
    <member name="T:System.CodeDom.CodeCommentStatementCollection">
      <summary>Represents a collection of <see cref="T:System.CodeDom.CodeCommentStatement" /> objects.</summary>
    </member>
    <member name="M:System.CodeDom.CodeCommentStatementCollection.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.CodeDom.CodeCommentStatementCollection" /> class.</summary>
    </member>
    <member name="M:System.CodeDom.CodeCommentStatementCollection.#ctor(System.CodeDom.CodeCommentStatement[])">
      <summary>Initializes a new instance of the <see cref="T:System.CodeDom.CodeCommentStatementCollection" /> class containing the specified array of <see cref="T:System.CodeDom.CodeCommentStatement" /> objects.</summary>
      <param name="value">An array of <see cref="T:System.CodeDom.CodeCommentStatement" /> objects with which to initialize the collection.</param>
      <exception cref="T:System.ArgumentNullException">One or more objects in the array are <see langword="null" />.</exception>
    </member>
    <member name="M:System.CodeDom.CodeCommentStatementCollection.#ctor(System.CodeDom.CodeCommentStatementCollection)">
      <summary>Initializes a new instance of the <see cref="T:System.CodeDom.CodeCommentStatementCollection" /> class containing the elements of the specified source collection.</summary>
      <param name="value">A <see cref="T:System.CodeDom.CodeCommentStatementCollection" /> with which to initialize the collection.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.CodeDom.CodeCommentStatementCollection.Add(System.CodeDom.CodeCommentStatement)">
      <summary>Adds the specified <see cref="T:System.CodeDom.CodeCommentStatement" /> object to the collection.</summary>
      <param name="value">The <see cref="T:System.CodeDom.CodeCommentStatement" /> object to add.</param>
      <returns>The index at which the new element was inserted.</returns>
    </member>
    <member name="M:System.CodeDom.CodeCommentStatementCollection.AddRange(System.CodeDom.CodeCommentStatement[])">
      <summary>Copies the elements of the specified <see cref="T:System.CodeDom.CodeCommentStatement" /> array to the end of the collection.</summary>
      <param name="value">An array of type <see cref="T:System.CodeDom.CodeCommentStatement" /> that contains the objects to add to the collection.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.CodeDom.CodeCommentStatementCollection.AddRange(System.CodeDom.CodeCommentStatementCollection)">
      <summary>Copies the contents of another <see cref="T:System.CodeDom.CodeCommentStatementCollection" /> object to the end of the collection.</summary>
      <param name="value">A <see cref="T:System.CodeDom.CodeCommentStatementCollection" /> that contains the objects to add to the collection.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.CodeDom.CodeCommentStatementCollection.Contains(System.CodeDom.CodeCommentStatement)">
      <summary>Gets a value that indicates whether the collection contains the specified <see cref="T:System.CodeDom.CodeCommentStatement" /> object.</summary>
      <param name="value">The <see cref="T:System.CodeDom.CodeCommentStatement" /> to search for in the collection.</param>
      <returns>
        <see langword="true" /> if the collection contains the specified object; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.CodeDom.CodeCommentStatementCollection.CopyTo(System.CodeDom.CodeCommentStatement[],System.Int32)">
      <summary>Copies the collection objects to the specified one-dimensional <see cref="T:System.Array" /> beginning at the specified index.</summary>
      <param name="array">The one-dimensional <see cref="T:System.Array" /> that is the destination of the values copied from the collection.</param>
      <param name="index">The index of the array at which to begin inserting.</param>
      <exception cref="T:System.ArgumentException">The destination array is multidimensional.  
  
 -or-  
  
 The number of elements in the <see cref="T:System.CodeDom.CodeCommentStatementCollection" /> is greater than the available space between the index of the target array specified by the <paramref name="index" /> parameter and the end of the target array.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="array" /> parameter is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="index" /> parameter is less than the target array's minimum index.</exception>
    </member>
    <member name="M:System.CodeDom.CodeCommentStatementCollection.IndexOf(System.CodeDom.CodeCommentStatement)">
      <summary>Gets the index of the specified <see cref="T:System.CodeDom.CodeCommentStatement" /> object in the collection, if it exists in the collection.</summary>
      <param name="value">The <see cref="T:System.CodeDom.CodeCommentStatement" /> object to locate.</param>
      <returns>The index of the specified object, if found, in the collection; otherwise, -1.</returns>
    </member>
    <member name="M:System.CodeDom.CodeCommentStatementCollection.Insert(System.Int32,System.CodeDom.CodeCommentStatement)">
      <summary>Inserts a <see cref="T:System.CodeDom.CodeCommentStatement" /> object into the collection at the specified index.</summary>
      <param name="index">The zero-based index where the item should be inserted.</param>
      <param name="value">The <see cref="T:System.CodeDom.CodeCommentStatement" /> object to insert.</param>
    </member>
    <member name="M:System.CodeDom.CodeCommentStatementCollection.Remove(System.CodeDom.CodeCommentStatement)">
      <summary>Removes the specified <see cref="T:System.CodeDom.CodeCommentStatement" /> object from the collection.</summary>
      <param name="value">The <see cref="T:System.CodeDom.CodeCommentStatement" /> object to remove from the collection.</param>
      <exception cref="T:System.ArgumentException">The specified object is not found in the collection.</exception>
    </member>
    <member name="P:System.CodeDom.CodeCommentStatementCollection.Item(System.Int32)">
      <summary>Gets or sets the <see cref="T:System.CodeDom.CodeCommentStatement" /> object at the specified index in the collection.</summary>
      <param name="index">The index of the collection to access.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="index" /> parameter is outside the valid range of indexes for the collection.</exception>
      <returns>A <see cref="T:System.CodeDom.CodeCommentStatement" /> object at each valid index.</returns>
    </member>
    <member name="T:System.CodeDom.CodeCompileUnit">
      <summary>Provides a container for a CodeDOM program graph.</summary>
    </member>
    <member name="M:System.CodeDom.CodeCompileUnit.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.CodeDom.CodeCompileUnit" /> class.</summary>
    </member>
    <member name="P:System.CodeDom.CodeCompileUnit.AssemblyCustomAttributes">
      <summary>Gets a collection of custom attributes for the generated assembly.</summary>
      <returns>A <see cref="T:System.CodeDom.CodeAttributeDeclarationCollection" /> that indicates the custom attributes for the generated assembly.</returns>
    </member>
    <member name="P:System.CodeDom.CodeCompileUnit.EndDirectives">
      <summary>Gets a <see cref="T:System.CodeDom.CodeDirectiveCollection" /> object containing end directives.</summary>
      <returns>A <see cref="T:System.CodeDom.CodeDirectiveCollection" /> object containing end directives.</returns>
    </member>
    <member name="P:System.CodeDom.CodeCompileUnit.Namespaces">
      <summary>Gets the collection of namespaces.</summary>
      <returns>A <see cref="T:System.CodeDom.CodeNamespaceCollection" /> that indicates the namespaces that the compile unit uses.</returns>
    </member>
    <member name="P:System.CodeDom.CodeCompileUnit.ReferencedAssemblies">
      <summary>Gets the referenced assemblies.</summary>
      <returns>A <see cref="T:System.Collections.Specialized.StringCollection" /> that contains the file names of the referenced assemblies.</returns>
    </member>
    <member name="P:System.CodeDom.CodeCompileUnit.StartDirectives">
      <summary>Gets a <see cref="T:System.CodeDom.CodeDirectiveCollection" /> object containing start directives.</summary>
      <returns>A <see cref="T:System.CodeDom.CodeDirectiveCollection" /> object containing start directives.</returns>
    </member>
    <member name="T:System.CodeDom.CodeConditionStatement">
      <summary>Represents a conditional branch statement, typically represented as an <see langword="if" /> statement.</summary>
    </member>
    <member name="M:System.CodeDom.CodeConditionStatement.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.CodeDom.CodeConditionStatement" /> class.</summary>
    </member>
    <member name="M:System.CodeDom.CodeConditionStatement.#ctor(System.CodeDom.CodeExpression,System.CodeDom.CodeStatement[])">
      <summary>Initializes a new instance of the <see cref="T:System.CodeDom.CodeConditionStatement" /> class using the specified condition and statements.</summary>
      <param name="condition">A <see cref="T:System.CodeDom.CodeExpression" /> that indicates the expression to evaluate.</param>
      <param name="trueStatements">An array of type <see cref="T:System.CodeDom.CodeStatement" /> containing the statements to execute if the condition is <see langword="true" />.</param>
    </member>
    <member name="M:System.CodeDom.CodeConditionStatement.#ctor(System.CodeDom.CodeExpression,System.CodeDom.CodeStatement[],System.CodeDom.CodeStatement[])">
      <summary>Initializes a new instance of the <see cref="T:System.CodeDom.CodeConditionStatement" /> class using the specified condition and statements.</summary>
      <param name="condition">A <see cref="T:System.CodeDom.CodeExpression" /> that indicates the condition to evaluate.</param>
      <param name="trueStatements">An array of type <see cref="T:System.CodeDom.CodeStatement" /> containing the statements to execute if the condition is <see langword="true" />.</param>
      <param name="falseStatements">An array of type <see cref="T:System.CodeDom.CodeStatement" /> containing the statements to execute if the condition is <see langword="false" />.</param>
    </member>
    <member name="P:System.CodeDom.CodeConditionStatement.Condition">
      <summary>Gets or sets the expression to evaluate <see langword="true" /> or <see langword="false" />.</summary>
      <returns>A <see cref="T:System.CodeDom.CodeExpression" /> to evaluate <see langword="true" /> or <see langword="false" />.</returns>
    </member>
    <member name="P:System.CodeDom.CodeConditionStatement.FalseStatements">
      <summary>Gets the collection of statements to execute if the conditional expression evaluates to <see langword="false" />.</summary>
      <returns>A <see cref="T:System.CodeDom.CodeStatementCollection" /> containing the statements to execute if the conditional expression evaluates to <see langword="false" />.</returns>
    </member>
    <member name="P:System.CodeDom.CodeConditionStatement.TrueStatements">
      <summary>Gets the collection of statements to execute if the conditional expression evaluates to <see langword="true" />.</summary>
      <returns>A <see cref="T:System.CodeDom.CodeStatementCollection" /> containing the statements to execute if the conditional expression evaluates to <see langword="true" />.</returns>
    </member>
    <member name="T:System.CodeDom.CodeConstructor">
      <summary>Represents a declaration for an instance constructor of a type.</summary>
    </member>
    <member name="M:System.CodeDom.CodeConstructor.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.CodeDom.CodeConstructor" /> class.</summary>
    </member>
    <member name="P:System.CodeDom.CodeConstructor.BaseConstructorArgs">
      <summary>Gets the collection of base constructor arguments.</summary>
      <returns>A <see cref="T:System.CodeDom.CodeExpressionCollection" /> that contains the base constructor arguments.</returns>
    </member>
    <member name="P:System.CodeDom.CodeConstructor.ChainedConstructorArgs">
      <summary>Gets the collection of chained constructor arguments.</summary>
      <returns>A <see cref="T:System.CodeDom.CodeExpressionCollection" /> that contains the chained constructor arguments.</returns>
    </member>
    <member name="T:System.CodeDom.CodeDefaultValueExpression">
      <summary>Represents a reference to a default value.</summary>
    </member>
    <member name="M:System.CodeDom.CodeDefaultValueExpression.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.CodeDom.CodeDefaultValueExpression" /> class.</summary>
    </member>
    <member name="M:System.CodeDom.CodeDefaultValueExpression.#ctor(System.CodeDom.CodeTypeReference)">
      <summary>Initializes a new instance of the <see cref="T:System.CodeDom.CodeDefaultValueExpression" /> class using the specified code type reference.</summary>
      <param name="type">A <see cref="T:System.CodeDom.CodeTypeReference" /> that specifies the reference to a value type.</param>
    </member>
    <member name="P:System.CodeDom.CodeDefaultValueExpression.Type">
      <summary>Gets or sets the data type reference for a default value.</summary>
      <returns>A <see cref="T:System.CodeDom.CodeTypeReference" /> object representing a data type that has a default value.</returns>
    </member>
    <member name="T:System.CodeDom.CodeDelegateCreateExpression">
      <summary>Represents an expression that creates a delegate.</summary>
    </member>
    <member name="M:System.CodeDom.CodeDelegateCreateExpression.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.CodeDom.CodeDelegateCreateExpression" /> class.</summary>
    </member>
    <member name="M:System.CodeDom.CodeDelegateCreateExpression.#ctor(System.CodeDom.CodeTypeReference,System.CodeDom.CodeExpression,System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.CodeDom.CodeDelegateCreateExpression" /> class.</summary>
      <param name="delegateType">A <see cref="T:System.CodeDom.CodeTypeReference" /> that indicates the data type of the delegate.</param>
      <param name="targetObject">A <see cref="T:System.CodeDom.CodeExpression" /> that indicates the object containing the event-handler method.</param>
      <param name="methodName">The name of the event-handler method.</param>
    </member>
    <member name="P:System.CodeDom.CodeDelegateCreateExpression.DelegateType">
      <summary>Gets or sets the data type of the delegate.</summary>
      <returns>A <see cref="T:System.CodeDom.CodeTypeReference" /> that indicates the data type of the delegate.</returns>
    </member>
    <member name="P:System.CodeDom.CodeDelegateCreateExpression.MethodName">
      <summary>Gets or sets the name of the event handler method.</summary>
      <returns>The name of the event handler method.</returns>
    </member>
    <member name="P:System.CodeDom.CodeDelegateCreateExpression.TargetObject">
      <summary>Gets or sets the object that contains the event-handler method.</summary>
      <returns>A <see cref="T:System.CodeDom.CodeExpression" /> that indicates the object containing the event-handler method.</returns>
    </member>
    <member name="T:System.CodeDom.CodeDelegateInvokeExpression">
      <summary>Represents an expression that raises an event.</summary>
    </member>
    <member name="M:System.CodeDom.CodeDelegateInvokeExpression.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.CodeDom.CodeDelegateInvokeExpression" /> class.</summary>
    </member>
    <member name="M:System.CodeDom.CodeDelegateInvokeExpression.#ctor(System.CodeDom.CodeExpression)">
      <summary>Initializes a new instance of the <see cref="T:System.CodeDom.CodeDelegateInvokeExpression" /> class using the specified target object.</summary>
      <param name="targetObject">A <see cref="T:System.CodeDom.CodeExpression" /> that indicates the target object.</param>
    </member>
    <member name="M:System.CodeDom.CodeDelegateInvokeExpression.#ctor(System.CodeDom.CodeExpression,System.CodeDom.CodeExpression[])">
      <summary>Initializes a new instance of the <see cref="T:System.CodeDom.CodeDelegateInvokeExpression" /> class using the specified target object and parameters.</summary>
      <param name="targetObject">A <see cref="T:System.CodeDom.CodeExpression" /> that indicates the target object.</param>
      <param name="parameters">An array of <see cref="T:System.CodeDom.CodeExpression" /> objects that indicate the parameters.</param>
    </member>
    <member name="P:System.CodeDom.CodeDelegateInvokeExpression.Parameters">
      <summary>Gets or sets the parameters to pass to the event handling methods attached to the event.</summary>
      <returns>A <see cref="T:System.CodeDom.CodeExpression" /> that indicates the parameters to pass to the event handling methods attached to the event.</returns>
    </member>
    <member name="P:System.CodeDom.CodeDelegateInvokeExpression.TargetObject">
      <summary>Gets or sets the event to invoke.</summary>
      <returns>A <see cref="T:System.CodeDom.CodeExpression" /> that indicates the event to invoke.</returns>
    </member>
    <member name="T:System.CodeDom.CodeDirectionExpression">
      <summary>Represents an expression used as a method invoke parameter along with a reference direction indicator.</summary>
    </member>
    <member name="M:System.CodeDom.CodeDirectionExpression.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.CodeDom.CodeDirectionExpression" /> class.</summary>
    </member>
    <member name="M:System.CodeDom.CodeDirectionExpression.#ctor(System.CodeDom.FieldDirection,System.CodeDom.CodeExpression)">
      <summary>Initializes a new instance of the <see cref="T:System.CodeDom.CodeDirectionExpression" /> class using the specified field direction and expression.</summary>
      <param name="direction">A <see cref="T:System.CodeDom.FieldDirection" /> that indicates the field direction of the expression.</param>
      <param name="expression">A <see cref="T:System.CodeDom.CodeExpression" /> that indicates the code expression to represent.</param>
    </member>
    <member name="P:System.CodeDom.CodeDirectionExpression.Direction">
      <summary>Gets or sets the field direction for this direction expression.</summary>
      <returns>A <see cref="T:System.CodeDom.FieldDirection" /> that indicates the field direction for this direction expression.</returns>
    </member>
    <member name="P:System.CodeDom.CodeDirectionExpression.Expression">
      <summary>Gets or sets the code expression to represent.</summary>
      <returns>A <see cref="T:System.CodeDom.CodeExpression" /> that indicates the expression to represent.</returns>
    </member>
    <member name="T:System.CodeDom.CodeDirective">
      <summary>Serves as the base class for code directive classes.</summary>
    </member>
    <member name="M:System.CodeDom.CodeDirective.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.CodeDom.CodeDirective" /> class.</summary>
    </member>
    <member name="T:System.CodeDom.CodeDirectiveCollection">
      <summary>Represents a collection of <see cref="T:System.CodeDom.CodeDirective" /> objects.</summary>
    </member>
    <member name="M:System.CodeDom.CodeDirectiveCollection.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.CodeDom.CodeDirectiveCollection" /> class.</summary>
    </member>
    <member name="M:System.CodeDom.CodeDirectiveCollection.#ctor(System.CodeDom.CodeDirective[])">
      <summary>Initializes a new instance of the <see cref="T:System.CodeDom.CodeDirectiveCollection" /> class with the code directive objects in the specified array.</summary>
      <param name="value">An array of <see cref="T:System.CodeDom.CodeDirective" /> objects with which to initialize the collection.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.CodeDom.CodeDirectiveCollection.#ctor(System.CodeDom.CodeDirectiveCollection)">
      <summary>Initializes a new instance of the <see cref="T:System.CodeDom.CodeDirectiveCollection" /> class with the elements in the specified code directive collection.</summary>
      <param name="value">A <see cref="T:System.CodeDom.CodeDirectiveCollection" /> object with which to initialize the collection.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.CodeDom.CodeDirectiveCollection.Add(System.CodeDom.CodeDirective)">
      <summary>Adds the specified <see cref="T:System.CodeDom.CodeDirective" /> object to the collection.</summary>
      <param name="value">The <see cref="T:System.CodeDom.CodeDirective" /> object to add.</param>
      <returns>The index position at which the new element was inserted.</returns>
    </member>
    <member name="M:System.CodeDom.CodeDirectiveCollection.AddRange(System.CodeDom.CodeDirective[])">
      <summary>Adds an array of <see cref="T:System.CodeDom.CodeDirective" /> objects to the end of the collection.</summary>
      <param name="value">An array of <see cref="T:System.CodeDom.CodeDirective" /> objects to add to the collection.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.CodeDom.CodeDirectiveCollection.AddRange(System.CodeDom.CodeDirectiveCollection)">
      <summary>Adds the contents of the specified <see cref="T:System.CodeDom.CodeDirectiveCollection" /> object to the end of the collection.</summary>
      <param name="value">A <see cref="T:System.CodeDom.CodeDirectiveCollection" /> object containing the <see cref="T:System.CodeDom.CodeDirective" /> objects to add to the collection.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.CodeDom.CodeDirectiveCollection.Contains(System.CodeDom.CodeDirective)">
      <summary>Gets a value indicating whether the collection contains the specified <see cref="T:System.CodeDom.CodeDirective" /> object.</summary>
      <param name="value">The <see cref="T:System.CodeDom.CodeDirective" /> object to search for in the collection.</param>
      <returns>
        <see langword="true" /> if the collection contains the specified object; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.CodeDom.CodeDirectiveCollection.CopyTo(System.CodeDom.CodeDirective[],System.Int32)">
      <summary>Copies the contents of the collection to a one-dimensional array beginning at the specified index.</summary>
      <param name="array">An array of type <see cref="T:System.CodeDom.CodeDirective" /> that is the destination of the values copied from the collection.</param>
      <param name="index">The index in the array at which to begin inserting collection objects.</param>
      <exception cref="T:System.ArgumentException">The destination array is multidimensional.  
  
 -or-  
  
 The number of elements in the <see cref="T:System.CodeDom.CodeDirectiveCollection" /> is greater than the available space between the index of the target array specified by <paramref name="index" /> and the end of the target array.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> is less than the target array's minimum index.</exception>
    </member>
    <member name="M:System.CodeDom.CodeDirectiveCollection.IndexOf(System.CodeDom.CodeDirective)">
      <summary>Gets the index in the collection of the specified <see cref="T:System.CodeDom.CodeDirective" /> object, if it exists in the collection.</summary>
      <param name="value">The <see cref="T:System.CodeDom.CodeDirective" /> object to locate in the collection.</param>
      <returns>The index position in the collection of the specified object, if found; otherwise, -1.</returns>
    </member>
    <member name="M:System.CodeDom.CodeDirectiveCollection.Insert(System.Int32,System.CodeDom.CodeDirective)">
      <summary>Inserts the specified <see cref="T:System.CodeDom.CodeDirective" /> object into the collection at the specified index.</summary>
      <param name="index">The zero-based index position where the specified object should be inserted.</param>
      <param name="value">The <see cref="T:System.CodeDom.CodeDirective" /> object to insert.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> is less than 0.  
  
 -or-  
  
 <paramref name="index" /> is greater than <see cref="P:System.Collections.CollectionBase.Count" />.</exception>
    </member>
    <member name="M:System.CodeDom.CodeDirectiveCollection.Remove(System.CodeDom.CodeDirective)">
      <summary>Removes the specified <see cref="T:System.CodeDom.CodeDirective" /> object from the collection.</summary>
      <param name="value">The <see cref="T:System.CodeDom.CodeDirective" /> object to remove from the collection.</param>
      <exception cref="T:System.ArgumentException">The specified object is not found in the collection.</exception>
    </member>
    <member name="P:System.CodeDom.CodeDirectiveCollection.Item(System.Int32)">
      <summary>Gets or sets the <see cref="T:System.CodeDom.CodeDirective" /> object at the specified index in the collection.</summary>
      <param name="index">The index position to access.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> is outside the valid range of index positions for the collection.</exception>
      <returns>The <see cref="T:System.CodeDom.CodeDirective" /> at the index position.</returns>
    </member>
    <member name="T:System.CodeDom.CodeEntryPointMethod">
      <summary>Represents the entry point method of an executable.</summary>
    </member>
    <member name="M:System.CodeDom.CodeEntryPointMethod.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.CodeDom.CodeEntryPointMethod" /> class.</summary>
    </member>
    <member name="T:System.CodeDom.CodeEventReferenceExpression">
      <summary>Represents a reference to an event.</summary>
    </member>
    <member name="M:System.CodeDom.CodeEventReferenceExpression.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.CodeDom.CodeEventReferenceExpression" /> class.</summary>
    </member>
    <member name="M:System.CodeDom.CodeEventReferenceExpression.#ctor(System.CodeDom.CodeExpression,System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.CodeDom.CodeEventReferenceExpression" /> class using the specified target object and event name.</summary>
      <param name="targetObject">A <see cref="T:System.CodeDom.CodeExpression" /> that indicates the object that contains the event.</param>
      <param name="eventName">The name of the event to reference.</param>
    </member>
    <member name="P:System.CodeDom.CodeEventReferenceExpression.EventName">
      <summary>Gets or sets the name of the event.</summary>
      <returns>The name of the event.</returns>
    </member>
    <member name="P:System.CodeDom.CodeEventReferenceExpression.TargetObject">
      <summary>Gets or sets the object that contains the event.</summary>
      <returns>A <see cref="T:System.CodeDom.CodeExpression" /> that indicates the object that contains the event.</returns>
    </member>
    <member name="T:System.CodeDom.CodeExpression">
      <summary>Represents a code expression. This is a base class for other code expression objects that is never instantiated.</summary>
    </member>
    <member name="M:System.CodeDom.CodeExpression.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.CodeDom.CodeExpression" /> class.</summary>
    </member>
    <member name="T:System.CodeDom.CodeExpressionCollection">
      <summary>Represents a collection of <see cref="T:System.CodeDom.CodeExpression" /> objects.</summary>
    </member>
    <member name="M:System.CodeDom.CodeExpressionCollection.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.CodeDom.CodeExpressionCollection" /> class.</summary>
    </member>
    <member name="M:System.CodeDom.CodeExpressionCollection.#ctor(System.CodeDom.CodeExpression[])">
      <summary>Initializes a new instance of the <see cref="T:System.CodeDom.CodeExpressionCollection" /> class containing the specified array of <see cref="T:System.CodeDom.CodeExpression" /> objects.</summary>
      <param name="value">An array of <see cref="T:System.CodeDom.CodeExpression" /> objects with which to initialize the collection.</param>
      <exception cref="T:System.ArgumentNullException">One or more objects in the array are <see langword="null" />.</exception>
    </member>
    <member name="M:System.CodeDom.CodeExpressionCollection.#ctor(System.CodeDom.CodeExpressionCollection)">
      <summary>Initializes a new instance of the <see cref="T:System.CodeDom.CodeExpressionCollection" /> class containing the elements of the specified source collection.</summary>
      <param name="value">A <see cref="T:System.CodeDom.CodeExpressionCollection" /> with which to initialize the collection.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.CodeDom.CodeExpressionCollection.Add(System.CodeDom.CodeExpression)">
      <summary>Adds the specified <see cref="T:System.CodeDom.CodeExpression" /> object to the collection.</summary>
      <param name="value">The <see cref="T:System.CodeDom.CodeExpression" /> object to add.</param>
      <returns>The index at which the new element was inserted.</returns>
    </member>
    <member name="M:System.CodeDom.CodeExpressionCollection.AddRange(System.CodeDom.CodeExpression[])">
      <summary>Copies the elements of the specified array to the end of the collection.</summary>
      <param name="value">An array of type <see cref="T:System.CodeDom.CodeExpression" /> that contains the objects to add to the collection.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.CodeDom.CodeExpressionCollection.AddRange(System.CodeDom.CodeExpressionCollection)">
      <summary>Copies the contents of another <see cref="T:System.CodeDom.CodeExpressionCollection" /> object to the end of the collection.</summary>
      <param name="value">A <see cref="T:System.CodeDom.CodeExpressionCollection" /> that contains the objects to add to the collection.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.CodeDom.CodeExpressionCollection.Contains(System.CodeDom.CodeExpression)">
      <summary>Gets a value that indicates whether the collection contains the specified <see cref="T:System.CodeDom.CodeExpression" /> object.</summary>
      <param name="value">The <see cref="T:System.CodeDom.CodeExpression" /> object to locate in the collection.</param>
      <returns>
        <see langword="true" /> if the collection contains the specified object; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.CodeDom.CodeExpressionCollection.CopyTo(System.CodeDom.CodeExpression[],System.Int32)">
      <summary>Copies the collection objects to a one-dimensional <see cref="T:System.Array" /> instance beginning at the specified index.</summary>
      <param name="array">The one-dimensional <see cref="T:System.Array" /> that is the destination of the values copied from the collection.</param>
      <param name="index">The index of the array at which to begin inserting.</param>
      <exception cref="T:System.ArgumentException">The destination array is multidimensional.  
  
 -or-  
  
 The number of elements in the <see cref="T:System.CodeDom.CodeExpressionCollection" /> is greater than the available space between the index of the target array specified by the <paramref name="index" /> parameter and the end of the target array.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="array" /> parameter is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="index" /> parameter is less than the target array's minimum index.</exception>
    </member>
    <member name="M:System.CodeDom.CodeExpressionCollection.IndexOf(System.CodeDom.CodeExpression)">
      <summary>Gets the index of the specified <see cref="T:System.CodeDom.CodeExpression" /> object in the collection, if it exists in the collection.</summary>
      <param name="value">The <see cref="T:System.CodeDom.CodeExpression" /> object to locate in the collection.</param>
      <returns>The index of the specified object, if found, in the collection; otherwise, -1.</returns>
    </member>
    <member name="M:System.CodeDom.CodeExpressionCollection.Insert(System.Int32,System.CodeDom.CodeExpression)">
      <summary>Inserts the specified <see cref="T:System.CodeDom.CodeExpression" /> object into the collection at the specified index.</summary>
      <param name="index">The zero-based index where the specified object should be inserted.</param>
      <param name="value">The <see cref="T:System.CodeDom.CodeExpression" /> object to insert.</param>
    </member>
    <member name="M:System.CodeDom.CodeExpressionCollection.Remove(System.CodeDom.CodeExpression)">
      <summary>Removes the specified <see cref="T:System.CodeDom.CodeExpression" /> object from the collection.</summary>
      <param name="value">The <see cref="T:System.CodeDom.CodeExpression" /> object to remove from the collection.</param>
      <exception cref="T:System.ArgumentException">The specified object is not found in the collection.</exception>
    </member>
    <member name="P:System.CodeDom.CodeExpressionCollection.Item(System.Int32)">
      <summary>Gets or sets the <see cref="T:System.CodeDom.CodeExpression" /> object at the specified index in the collection.</summary>
      <param name="index">The zero-based index of the collection to access.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="index" /> parameter is outside the valid range of indexes for the collection.</exception>
      <returns>A <see cref="T:System.CodeDom.CodeExpression" /> object at each valid index.</returns>
    </member>
    <member name="T:System.CodeDom.CodeExpressionStatement">
      <summary>Represents a statement that consists of a single expression.</summary>
    </member>
    <member name="M:System.CodeDom.CodeExpressionStatement.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.CodeDom.CodeExpressionStatement" /> class.</summary>
    </member>
    <member name="M:System.CodeDom.CodeExpressionStatement.#ctor(System.CodeDom.CodeExpression)">
      <summary>Initializes a new instance of the <see cref="T:System.CodeDom.CodeExpressionStatement" /> class by using the specified expression.</summary>
      <param name="expression">A <see cref="T:System.CodeDom.CodeExpression" /> for the statement.</param>
    </member>
    <member name="P:System.CodeDom.CodeExpressionStatement.Expression">
      <summary>Gets or sets the expression for the statement.</summary>
      <returns>A <see cref="T:System.CodeDom.CodeExpression" /> that indicates the expression for the statement.</returns>
    </member>
    <member name="T:System.CodeDom.CodeFieldReferenceExpression">
      <summary>Represents a reference to a field.</summary>
    </member>
    <member name="M:System.CodeDom.CodeFieldReferenceExpression.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.CodeDom.CodeFieldReferenceExpression" /> class.</summary>
    </member>
    <member name="M:System.CodeDom.CodeFieldReferenceExpression.#ctor(System.CodeDom.CodeExpression,System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.CodeDom.CodeFieldReferenceExpression" /> class using the specified target object and field name.</summary>
      <param name="targetObject">A <see cref="T:System.CodeDom.CodeExpression" /> that indicates the object that contains the field.</param>
      <param name="fieldName">The name of the field.</param>
    </member>
    <member name="P:System.CodeDom.CodeFieldReferenceExpression.FieldName">
      <summary>Gets or sets the name of the field to reference.</summary>
      <returns>A string containing the field name.</returns>
    </member>
    <member name="P:System.CodeDom.CodeFieldReferenceExpression.TargetObject">
      <summary>Gets or sets the object that contains the field to reference.</summary>
      <returns>A <see cref="T:System.CodeDom.CodeExpression" /> that indicates the object that contains the field to reference.</returns>
    </member>
    <member name="T:System.CodeDom.CodeGotoStatement">
      <summary>Represents a <see langword="goto" /> statement.</summary>
    </member>
    <member name="M:System.CodeDom.CodeGotoStatement.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.CodeDom.CodeGotoStatement" /> class.</summary>
    </member>
    <member name="M:System.CodeDom.CodeGotoStatement.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.CodeDom.CodeGotoStatement" /> class using the specified label name.</summary>
      <param name="label">The name of the label at which to continue program execution.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="Label" /> is <see langword="null" />.</exception>
    </member>
    <member name="P:System.CodeDom.CodeGotoStatement.Label">
      <summary>Gets or sets the name of the label at which to continue program execution.</summary>
      <exception cref="T:System.ArgumentNullException">The label cannot be set because <paramref name="value" /> is <see langword="null" /> or an empty string.</exception>
      <returns>A string that indicates the name of the label at which to continue program execution.</returns>
    </member>
    <member name="T:System.CodeDom.CodeIndexerExpression">
      <summary>Represents a reference to an indexer property of an object.</summary>
    </member>
    <member name="M:System.CodeDom.CodeIndexerExpression.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.CodeDom.CodeIndexerExpression" /> class.</summary>
    </member>
    <member name="M:System.CodeDom.CodeIndexerExpression.#ctor(System.CodeDom.CodeExpression,System.CodeDom.CodeExpression[])">
      <summary>Initializes a new instance of the <see cref="T:System.CodeDom.CodeIndexerExpression" /> class using the specified target object and index.</summary>
      <param name="targetObject">The target object.</param>
      <param name="indices">The index or indexes of the indexer expression.</param>
    </member>
    <member name="P:System.CodeDom.CodeIndexerExpression.Indices">
      <summary>Gets the collection of indexes of the indexer expression.</summary>
      <returns>A <see cref="T:System.CodeDom.CodeExpressionCollection" /> that indicates the index or indexes of the indexer expression.</returns>
    </member>
    <member name="P:System.CodeDom.CodeIndexerExpression.TargetObject">
      <summary>Gets or sets the target object that can be indexed.</summary>
      <returns>A <see cref="T:System.CodeDom.CodeExpression" /> that indicates the indexer object.</returns>
    </member>
    <member name="T:System.CodeDom.CodeIterationStatement">
      <summary>Represents a <see langword="for" /> statement, or a loop through a block of statements, using a test expression as a condition for continuing to loop.</summary>
    </member>
    <member name="M:System.CodeDom.CodeIterationStatement.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.CodeDom.CodeIterationStatement" /> class.</summary>
    </member>
    <member name="M:System.CodeDom.CodeIterationStatement.#ctor(System.CodeDom.CodeStatement,System.CodeDom.CodeExpression,System.CodeDom.CodeStatement,System.CodeDom.CodeStatement[])">
      <summary>Initializes a new instance of the <see cref="T:System.CodeDom.CodeIterationStatement" /> class using the specified parameters.</summary>
      <param name="initStatement">A <see cref="T:System.CodeDom.CodeStatement" /> containing the loop initialization statement.</param>
      <param name="testExpression">A <see cref="T:System.CodeDom.CodeExpression" /> containing the expression to test for exit condition.</param>
      <param name="incrementStatement">A <see cref="T:System.CodeDom.CodeStatement" /> containing the per-cycle increment statement.</param>
      <param name="statements">An array of type <see cref="T:System.CodeDom.CodeStatement" /> containing the statements within the loop.</param>
    </member>
    <member name="P:System.CodeDom.CodeIterationStatement.IncrementStatement">
      <summary>Gets or sets the statement that is called after each loop cycle.</summary>
      <returns>A <see cref="T:System.CodeDom.CodeStatement" /> that indicates the per cycle increment statement.</returns>
    </member>
    <member name="P:System.CodeDom.CodeIterationStatement.InitStatement">
      <summary>Gets or sets the loop initialization statement.</summary>
      <returns>A <see cref="T:System.CodeDom.CodeStatement" /> that indicates the loop initialization statement.</returns>
    </member>
    <member name="P:System.CodeDom.CodeIterationStatement.Statements">
      <summary>Gets the collection of statements to be executed within the loop.</summary>
      <returns>An array of type <see cref="T:System.CodeDom.CodeStatement" /> that indicates the statements within the loop.</returns>
    </member>
    <member name="P:System.CodeDom.CodeIterationStatement.TestExpression">
      <summary>Gets or sets the expression to test as the condition that continues the loop.</summary>
      <returns>A <see cref="T:System.CodeDom.CodeExpression" /> that indicates the expression to test.</returns>
    </member>
    <member name="T:System.CodeDom.CodeLabeledStatement">
      <summary>Represents a labeled statement or a stand-alone label.</summary>
    </member>
    <member name="M:System.CodeDom.CodeLabeledStatement.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.CodeDom.CodeLabeledStatement" /> class.</summary>
    </member>
    <member name="M:System.CodeDom.CodeLabeledStatement.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.CodeDom.CodeLabeledStatement" /> class using the specified label name.</summary>
      <param name="label">The name of the label.</param>
    </member>
    <member name="M:System.CodeDom.CodeLabeledStatement.#ctor(System.String,System.CodeDom.CodeStatement)">
      <summary>Initializes a new instance of the <see cref="T:System.CodeDom.CodeLabeledStatement" /> class using the specified label name and statement.</summary>
      <param name="label">The name of the label.</param>
      <param name="statement">The <see cref="T:System.CodeDom.CodeStatement" /> to associate with the label.</param>
    </member>
    <member name="P:System.CodeDom.CodeLabeledStatement.Label">
      <summary>Gets or sets the name of the label.</summary>
      <returns>The name of the label.</returns>
    </member>
    <member name="P:System.CodeDom.CodeLabeledStatement.Statement">
      <summary>Gets or sets the optional associated statement.</summary>
      <returns>A <see cref="T:System.CodeDom.CodeStatement" /> that indicates the statement associated with the label.</returns>
    </member>
    <member name="T:System.CodeDom.CodeLinePragma">
      <summary>Represents a specific location within a specific file.</summary>
    </member>
    <member name="M:System.CodeDom.CodeLinePragma.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.CodeDom.CodeLinePragma" /> class.</summary>
    </member>
    <member name="M:System.CodeDom.CodeLinePragma.#ctor(System.String,System.Int32)">
      <summary>Initializes a new instance of the <see cref="T:System.CodeDom.CodeLinePragma" /> class.</summary>
      <param name="fileName">The file name of the associated file.</param>
      <param name="lineNumber">The line number to store a reference to.</param>
    </member>
    <member name="P:System.CodeDom.CodeLinePragma.FileName">
      <summary>Gets or sets the name of the associated file.</summary>
      <returns>The file name of the associated file.</returns>
    </member>
    <member name="P:System.CodeDom.CodeLinePragma.LineNumber">
      <summary>Gets or sets the line number of the associated reference.</summary>
      <returns>The line number.</returns>
    </member>
    <member name="T:System.CodeDom.CodeMemberEvent">
      <summary>Represents a declaration for an event of a type.</summary>
    </member>
    <member name="M:System.CodeDom.CodeMemberEvent.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.CodeDom.CodeMemberEvent" /> class.</summary>
    </member>
    <member name="P:System.CodeDom.CodeMemberEvent.ImplementationTypes">
      <summary>Gets or sets the data type that the member event implements.</summary>
      <returns>A <see cref="T:System.CodeDom.CodeTypeReferenceCollection" /> that indicates the data type or types that the member event implements.</returns>
    </member>
    <member name="P:System.CodeDom.CodeMemberEvent.PrivateImplementationType">
      <summary>Gets or sets the privately implemented data type, if any.</summary>
      <returns>A <see cref="T:System.CodeDom.CodeTypeReference" /> that indicates the data type that the event privately implements.</returns>
    </member>
    <member name="P:System.CodeDom.CodeMemberEvent.Type">
      <summary>Gets or sets the data type of the delegate type that handles the event.</summary>
      <returns>A <see cref="T:System.CodeDom.CodeTypeReference" /> that indicates the delegate type that handles the event.</returns>
    </member>
    <member name="T:System.CodeDom.CodeMemberField">
      <summary>Represents a declaration for a field of a type.</summary>
    </member>
    <member name="M:System.CodeDom.CodeMemberField.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.CodeDom.CodeMemberField" /> class.</summary>
    </member>
    <member name="M:System.CodeDom.CodeMemberField.#ctor(System.CodeDom.CodeTypeReference,System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.CodeDom.CodeMemberField" /> class using the specified field type and field name.</summary>
      <param name="type">An object that indicates the type of the field.</param>
      <param name="name">The name of the field.</param>
    </member>
    <member name="M:System.CodeDom.CodeMemberField.#ctor(System.String,System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.CodeDom.CodeMemberField" /> class using the specified field type and field name.</summary>
      <param name="type">The type of the field.</param>
      <param name="name">The name of the field.</param>
    </member>
    <member name="M:System.CodeDom.CodeMemberField.#ctor(System.Type,System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.CodeDom.CodeMemberField" /> class using the specified field type and field name.</summary>
      <param name="type">The type of the field.</param>
      <param name="name">The name of the field.</param>
    </member>
    <member name="P:System.CodeDom.CodeMemberField.InitExpression">
      <summary>Gets or sets the initialization expression for the field.</summary>
      <returns>The initialization expression for the field.</returns>
    </member>
    <member name="P:System.CodeDom.CodeMemberField.Type">
      <summary>Gets or sets the type of the field.</summary>
      <returns>The type of the field.</returns>
    </member>
    <member name="T:System.CodeDom.CodeMemberMethod">
      <summary>Represents a declaration for a method of a type.</summary>
    </member>
    <member name="E:System.CodeDom.CodeMemberMethod.PopulateImplementationTypes">
      <summary>An event that will be raised the first time the <see cref="P:System.CodeDom.CodeMemberMethod.ImplementationTypes" /> collection is accessed.</summary>
    </member>
    <member name="E:System.CodeDom.CodeMemberMethod.PopulateParameters">
      <summary>An event that will be raised the first time the <see cref="P:System.CodeDom.CodeMemberMethod.Parameters" /> collection is accessed.</summary>
    </member>
    <member name="E:System.CodeDom.CodeMemberMethod.PopulateStatements">
      <summary>An event that will be raised the first time the <see cref="P:System.CodeDom.CodeMemberMethod.Statements" /> collection is accessed.</summary>
    </member>
    <member name="M:System.CodeDom.CodeMemberMethod.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.CodeDom.CodeMemberMethod" /> class.</summary>
    </member>
    <member name="P:System.CodeDom.CodeMemberMethod.ImplementationTypes">
      <summary>Gets the data types of the interfaces implemented by this method, unless it is a private method implementation, which is indicated by the <see cref="P:System.CodeDom.CodeMemberMethod.PrivateImplementationType" /> property.</summary>
      <returns>A <see cref="T:System.CodeDom.CodeTypeReferenceCollection" /> that indicates the interfaces implemented by this method.</returns>
    </member>
    <member name="P:System.CodeDom.CodeMemberMethod.Parameters">
      <summary>Gets the parameter declarations for the method.</summary>
      <returns>A <see cref="T:System.CodeDom.CodeParameterDeclarationExpressionCollection" /> that indicates the method parameters.</returns>
    </member>
    <member name="P:System.CodeDom.CodeMemberMethod.PrivateImplementationType">
      <summary>Gets or sets the data type of the interface this method, if private, implements a method of, if any.</summary>
      <returns>A <see cref="T:System.CodeDom.CodeTypeReference" /> that indicates the data type of the interface with the method that the private method whose declaration is represented by this <see cref="T:System.CodeDom.CodeMemberMethod" /> implements.</returns>
    </member>
    <member name="P:System.CodeDom.CodeMemberMethod.ReturnType">
      <summary>Gets or sets the data type of the return value of the method.</summary>
      <returns>A <see cref="T:System.CodeDom.CodeTypeReference" /> that indicates the data type of the value returned by the method.</returns>
    </member>
    <member name="P:System.CodeDom.CodeMemberMethod.ReturnTypeCustomAttributes">
      <summary>Gets the custom attributes of the return type of the method.</summary>
      <returns>A <see cref="T:System.CodeDom.CodeAttributeDeclarationCollection" /> that indicates the custom attributes.</returns>
    </member>
    <member name="P:System.CodeDom.CodeMemberMethod.Statements">
      <summary>Gets the statements within the method.</summary>
      <returns>A <see cref="T:System.CodeDom.CodeStatementCollection" /> that indicates the statements within the method.</returns>
    </member>
    <member name="P:System.CodeDom.CodeMemberMethod.TypeParameters">
      <summary>Gets the type parameters for the current generic method.</summary>
      <returns>A <see cref="T:System.CodeDom.CodeTypeParameterCollection" /> that contains the type parameters for the generic method.</returns>
    </member>
    <member name="T:System.CodeDom.CodeMemberProperty">
      <summary>Represents a declaration for a property of a type.</summary>
    </member>
    <member name="M:System.CodeDom.CodeMemberProperty.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.CodeDom.CodeMemberProperty" /> class.</summary>
    </member>
    <member name="P:System.CodeDom.CodeMemberProperty.GetStatements">
      <summary>Gets the collection of <see langword="get" /> statements for the property.</summary>
      <returns>A <see cref="T:System.CodeDom.CodeStatementCollection" /> that contains the <see langword="get" /> statements for the member property.</returns>
    </member>
    <member name="P:System.CodeDom.CodeMemberProperty.HasGet">
      <summary>Gets or sets a value indicating whether the property has a <see langword="get" /> method accessor.</summary>
      <returns>
        <see langword="true" /> if the <see langword="Count" /> property of the <see cref="P:System.CodeDom.CodeMemberProperty.GetStatements" /> collection is non-zero, or if the value of this property has been set to <see langword="true" />; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.CodeDom.CodeMemberProperty.HasSet">
      <summary>Gets or sets a value indicating whether the property has a <see langword="set" /> method accessor.</summary>
      <returns>
        <see langword="true" /> if the <see cref="P:System.Collections.CollectionBase.Count" /> property of the <see cref="P:System.CodeDom.CodeMemberProperty.SetStatements" /> collection is non-zero; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.CodeDom.CodeMemberProperty.ImplementationTypes">
      <summary>Gets the data types of any interfaces that the property implements.</summary>
      <returns>A <see cref="T:System.CodeDom.CodeTypeReferenceCollection" /> that indicates the data types the property implements.</returns>
    </member>
    <member name="P:System.CodeDom.CodeMemberProperty.Parameters">
      <summary>Gets the collection of declaration expressions for the property.</summary>
      <returns>A <see cref="T:System.CodeDom.CodeParameterDeclarationExpressionCollection" /> that indicates the declaration expressions for the property.</returns>
    </member>
    <member name="P:System.CodeDom.CodeMemberProperty.PrivateImplementationType">
      <summary>Gets or sets the data type of the interface, if any, this property, if private, implements.</summary>
      <returns>A <see cref="T:System.CodeDom.CodeTypeReference" /> that indicates the data type of the interface, if any, the property, if private, implements.</returns>
    </member>
    <member name="P:System.CodeDom.CodeMemberProperty.SetStatements">
      <summary>Gets the collection of <see langword="set" /> statements for the property.</summary>
      <returns>A <see cref="T:System.CodeDom.CodeStatementCollection" /> that contains the <see langword="set" /> statements for the member property.</returns>
    </member>
    <member name="P:System.CodeDom.CodeMemberProperty.Type">
      <summary>Gets or sets the data type of the property.</summary>
      <returns>A <see cref="T:System.CodeDom.CodeTypeReference" /> that indicates the data type of the property.</returns>
    </member>
    <member name="T:System.CodeDom.CodeMethodInvokeExpression">
      <summary>Represents an expression that invokes a method.</summary>
    </member>
    <member name="M:System.CodeDom.CodeMethodInvokeExpression.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.CodeDom.CodeMethodInvokeExpression" /> class.</summary>
    </member>
    <member name="M:System.CodeDom.CodeMethodInvokeExpression.#ctor(System.CodeDom.CodeExpression,System.String,System.CodeDom.CodeExpression[])">
      <summary>Initializes a new instance of the <see cref="T:System.CodeDom.CodeMethodInvokeExpression" /> class using the specified target object, method name, and parameters.</summary>
      <param name="targetObject">A <see cref="T:System.CodeDom.CodeExpression" /> that indicates the target object with the method to invoke.</param>
      <param name="methodName">The name of the method to invoke.</param>
      <param name="parameters">An array of <see cref="T:System.CodeDom.CodeExpression" /> objects that indicate the parameters to call the method with.</param>
    </member>
    <member name="M:System.CodeDom.CodeMethodInvokeExpression.#ctor(System.CodeDom.CodeMethodReferenceExpression,System.CodeDom.CodeExpression[])">
      <summary>Initializes a new instance of the <see cref="T:System.CodeDom.CodeMethodInvokeExpression" /> class using the specified method and parameters.</summary>
      <param name="method">A <see cref="T:System.CodeDom.CodeMethodReferenceExpression" /> that indicates the method to invoke.</param>
      <param name="parameters">An array of <see cref="T:System.CodeDom.CodeExpression" /> objects that indicate the parameters with which to invoke the method.</param>
    </member>
    <member name="P:System.CodeDom.CodeMethodInvokeExpression.Method">
      <summary>Gets or sets the method to invoke.</summary>
      <returns>A <see cref="T:System.CodeDom.CodeMethodReferenceExpression" /> that indicates the method to invoke.</returns>
    </member>
    <member name="P:System.CodeDom.CodeMethodInvokeExpression.Parameters">
      <summary>Gets the parameters to invoke the method with.</summary>
      <returns>A <see cref="T:System.CodeDom.CodeExpressionCollection" /> that indicates the parameters to invoke the method with.</returns>
    </member>
    <member name="T:System.CodeDom.CodeMethodReferenceExpression">
      <summary>Represents a reference to a method.</summary>
    </member>
    <member name="M:System.CodeDom.CodeMethodReferenceExpression.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.CodeDom.CodeMethodReferenceExpression" /> class.</summary>
    </member>
    <member name="M:System.CodeDom.CodeMethodReferenceExpression.#ctor(System.CodeDom.CodeExpression,System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.CodeDom.CodeMethodReferenceExpression" /> class using the specified target object and method name.</summary>
      <param name="targetObject">A <see cref="T:System.CodeDom.CodeExpression" /> that indicates the object to target.</param>
      <param name="methodName">The name of the method to call.</param>
    </member>
    <member name="M:System.CodeDom.CodeMethodReferenceExpression.#ctor(System.CodeDom.CodeExpression,System.String,System.CodeDom.CodeTypeReference[])">
      <summary>Initializes a new instance of the <see cref="T:System.CodeDom.CodeMethodReferenceExpression" /> class using the specified target object, method name, and generic type arguments.</summary>
      <param name="targetObject">A <see cref="T:System.CodeDom.CodeExpression" /> that indicates the object to target.</param>
      <param name="methodName">The name of the method to call.</param>
      <param name="typeParameters">An array of <see cref="T:System.CodeDom.CodeTypeReference" /> values that specify the <see cref="P:System.CodeDom.CodeMethodReferenceExpression.TypeArguments" /> for this <see cref="T:System.CodeDom.CodeMethodReferenceExpression" />.</param>
    </member>
    <member name="P:System.CodeDom.CodeMethodReferenceExpression.MethodName">
      <summary>Gets or sets the name of the method to reference.</summary>
      <returns>The name of the method to reference.</returns>
    </member>
    <member name="P:System.CodeDom.CodeMethodReferenceExpression.TargetObject">
      <summary>Gets or sets the expression that indicates the method to reference.</summary>
      <returns>A <see cref="T:System.CodeDom.CodeExpression" /> that represents the method to reference.</returns>
    </member>
    <member name="P:System.CodeDom.CodeMethodReferenceExpression.TypeArguments">
      <summary>Gets the type arguments for the current generic method reference expression.</summary>
      <returns>A <see cref="T:System.CodeDom.CodeTypeReferenceCollection" /> containing the type arguments for the current code <see cref="T:System.CodeDom.CodeMethodReferenceExpression" />.</returns>
    </member>
    <member name="T:System.CodeDom.CodeMethodReturnStatement">
      <summary>Represents a return value statement.</summary>
    </member>
    <member name="M:System.CodeDom.CodeMethodReturnStatement.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.CodeDom.CodeMethodReturnStatement" /> class.</summary>
    </member>
    <member name="M:System.CodeDom.CodeMethodReturnStatement.#ctor(System.CodeDom.CodeExpression)">
      <summary>Initializes a new instance of the <see cref="T:System.CodeDom.CodeMethodReturnStatement" /> class using the specified expression.</summary>
      <param name="expression">A <see cref="T:System.CodeDom.CodeExpression" /> that indicates the return value.</param>
    </member>
    <member name="P:System.CodeDom.CodeMethodReturnStatement.Expression">
      <summary>Gets or sets the return value.</summary>
      <returns>A <see cref="T:System.CodeDom.CodeExpression" /> that indicates the value to return for the return statement, or <see langword="null" /> if the statement is part of a subroutine.</returns>
    </member>
    <member name="T:System.CodeDom.CodeNamespace">
      <summary>Represents a namespace declaration.</summary>
    </member>
    <member name="E:System.CodeDom.CodeNamespace.PopulateComments">
      <summary>An event that will be raised the first time the <see cref="P:System.CodeDom.CodeNamespace.Comments" /> collection is accessed.</summary>
    </member>
    <member name="E:System.CodeDom.CodeNamespace.PopulateImports">
      <summary>An event that will be raised the first time the <see cref="P:System.CodeDom.CodeNamespace.Imports" /> collection is accessed.</summary>
    </member>
    <member name="E:System.CodeDom.CodeNamespace.PopulateTypes">
      <summary>An event that will be raised the first time the <see cref="P:System.CodeDom.CodeNamespace.Types" /> collection is accessed.</summary>
    </member>
    <member name="M:System.CodeDom.CodeNamespace.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.CodeDom.CodeNamespace" /> class.</summary>
    </member>
    <member name="M:System.CodeDom.CodeNamespace.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.CodeDom.CodeNamespace" /> class using the specified name.</summary>
      <param name="name">The name of the namespace being declared.</param>
    </member>
    <member name="P:System.CodeDom.CodeNamespace.Comments">
      <summary>Gets the comments for the namespace.</summary>
      <returns>A <see cref="T:System.CodeDom.CodeCommentStatementCollection" /> that indicates the comments for the namespace.</returns>
    </member>
    <member name="P:System.CodeDom.CodeNamespace.Imports">
      <summary>Gets the collection of namespace import directives used by the namespace.</summary>
      <returns>A <see cref="T:System.CodeDom.CodeNamespaceImportCollection" /> that indicates the namespace import directives used by the namespace.</returns>
    </member>
    <member name="P:System.CodeDom.CodeNamespace.Name">
      <summary>Gets or sets the name of the namespace.</summary>
      <returns>The name of the namespace.</returns>
    </member>
    <member name="P:System.CodeDom.CodeNamespace.Types">
      <summary>Gets the collection of types that the namespace contains.</summary>
      <returns>A <see cref="T:System.CodeDom.CodeTypeDeclarationCollection" /> that indicates the types contained in the namespace.</returns>
    </member>
    <member name="T:System.CodeDom.CodeNamespaceCollection">
      <summary>Represents a collection of <see cref="T:System.CodeDom.CodeNamespace" /> objects.</summary>
    </member>
    <member name="M:System.CodeDom.CodeNamespaceCollection.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.CodeDom.CodeNamespaceCollection" /> class.</summary>
    </member>
    <member name="M:System.CodeDom.CodeNamespaceCollection.#ctor(System.CodeDom.CodeNamespace[])">
      <summary>Initializes a new instance of the <see cref="T:System.CodeDom.CodeNamespaceCollection" /> class that contains the specified array of <see cref="T:System.CodeDom.CodeNamespace" /> objects.</summary>
      <param name="value">An array of <see cref="T:System.CodeDom.CodeNamespace" /> objects with which to initialize the collection.</param>
      <exception cref="T:System.ArgumentNullException">One or more objects in the array are <see langword="null" />.</exception>
    </member>
    <member name="M:System.CodeDom.CodeNamespaceCollection.#ctor(System.CodeDom.CodeNamespaceCollection)">
      <summary>Initializes a new instance of the <see cref="T:System.CodeDom.CodeNamespaceCollection" /> class that contains the elements of the specified source collection.</summary>
      <param name="value">The <see cref="T:System.CodeDom.CodeNamespaceCollection" /> with which to initialize the collection.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.CodeDom.CodeNamespaceCollection.Add(System.CodeDom.CodeNamespace)">
      <summary>Adds the specified <see cref="T:System.CodeDom.CodeNamespace" /> object to the collection.</summary>
      <param name="value">The <see cref="T:System.CodeDom.CodeNamespace" /> to add.</param>
      <returns>The index at which the new element was inserted.</returns>
    </member>
    <member name="M:System.CodeDom.CodeNamespaceCollection.AddRange(System.CodeDom.CodeNamespace[])">
      <summary>Copies the elements of the specified <see cref="T:System.CodeDom.CodeNamespace" /> array to the end of the collection.</summary>
      <param name="value">An array of type <see cref="T:System.CodeDom.CodeNamespace" /> that contains the objects to add to the collection.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.CodeDom.CodeNamespaceCollection.AddRange(System.CodeDom.CodeNamespaceCollection)">
      <summary>Adds the contents of the specified <see cref="T:System.CodeDom.CodeNamespaceCollection" /> object to the end of the collection.</summary>
      <param name="value">A <see cref="T:System.CodeDom.CodeNamespaceCollection" /> that contains the objects to add to the collection.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.CodeDom.CodeNamespaceCollection.Contains(System.CodeDom.CodeNamespace)">
      <summary>Gets a value that indicates whether the collection contains the specified <see cref="T:System.CodeDom.CodeNamespace" /> object.</summary>
      <param name="value">The <see cref="T:System.CodeDom.CodeNamespace" /> to search for in the collection.</param>
      <returns>
        <see langword="true" /> if the <see cref="T:System.CodeDom.CodeNamespace" /> is contained in the collection; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.CodeDom.CodeNamespaceCollection.CopyTo(System.CodeDom.CodeNamespace[],System.Int32)">
      <summary>Copies the collection objects to a one-dimensional <see cref="T:System.Array" /> instance, starting at the specified index.</summary>
      <param name="array">The one-dimensional <see cref="T:System.Array" /> that is the destination of the values copied from the collection.</param>
      <param name="index">The index of the array at which to begin inserting.</param>
      <exception cref="T:System.ArgumentException">The destination array is multidimensional.  
  
 -or-  
  
 The number of elements in the <see cref="T:System.CodeDom.CodeNamespaceCollection" /> is greater than the available space between the index of the target array specified by the <paramref name="index" /> parameter and the end of the target array.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="array" /> parameter is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="index" /> parameter is less than the target array's minimum index.</exception>
    </member>
    <member name="M:System.CodeDom.CodeNamespaceCollection.IndexOf(System.CodeDom.CodeNamespace)">
      <summary>Gets the index of the specified <see cref="T:System.CodeDom.CodeNamespace" /> object in the <see cref="T:System.CodeDom.CodeNamespaceCollection" />, if it exists in the collection.</summary>
      <param name="value">The <see cref="T:System.CodeDom.CodeNamespace" /> to locate.</param>
      <returns>The index of the specified <see cref="T:System.CodeDom.CodeNamespace" />, if it is found, in the collection; otherwise, -1.</returns>
    </member>
    <member name="M:System.CodeDom.CodeNamespaceCollection.Insert(System.Int32,System.CodeDom.CodeNamespace)">
      <summary>Inserts the specified <see cref="T:System.CodeDom.CodeNamespace" /> object into the collection at the specified index.</summary>
      <param name="index">The zero-based index where the new item should be inserted.</param>
      <param name="value">The <see cref="T:System.CodeDom.CodeNamespace" /> to insert.</param>
    </member>
    <member name="M:System.CodeDom.CodeNamespaceCollection.Remove(System.CodeDom.CodeNamespace)">
      <summary>Removes the specified <see cref="T:System.CodeDom.CodeNamespace" /> object from the collection.</summary>
      <param name="value">The <see cref="T:System.CodeDom.CodeNamespace" /> to remove from the collection.</param>
      <exception cref="T:System.ArgumentException">The specified object is not found in the collection.</exception>
    </member>
    <member name="P:System.CodeDom.CodeNamespaceCollection.Item(System.Int32)">
      <summary>Gets or sets the <see cref="T:System.CodeDom.CodeNamespaceCollection" /> object at the specified index in the collection.</summary>
      <param name="index">The index of the collection to access.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="index" /> parameter is outside the valid range of indexes for the collection.</exception>
      <returns>A <see cref="T:System.CodeDom.CodeNamespace" /> at each valid index.</returns>
    </member>
    <member name="T:System.CodeDom.CodeNamespaceImport">
      <summary>Represents a namespace import directive that indicates a namespace to use.</summary>
    </member>
    <member name="M:System.CodeDom.CodeNamespaceImport.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.CodeDom.CodeNamespaceImport" /> class.</summary>
    </member>
    <member name="M:System.CodeDom.CodeNamespaceImport.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.CodeDom.CodeNamespaceImport" /> class using the specified namespace to import.</summary>
      <param name="nameSpace">The name of the namespace to import.</param>
    </member>
    <member name="P:System.CodeDom.CodeNamespaceImport.LinePragma">
      <summary>Gets or sets the line and file the statement occurs on.</summary>
      <returns>A <see cref="T:System.CodeDom.CodeLinePragma" /> that indicates the context of the statement.</returns>
    </member>
    <member name="P:System.CodeDom.CodeNamespaceImport.Namespace">
      <summary>Gets or sets the namespace to import.</summary>
      <returns>The name of the namespace to import.</returns>
    </member>
    <member name="T:System.CodeDom.CodeNamespaceImportCollection">
      <summary>Represents a collection of <see cref="T:System.CodeDom.CodeNamespaceImport" /> objects.</summary>
    </member>
    <member name="M:System.CodeDom.CodeNamespaceImportCollection.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.CodeDom.CodeNamespaceImportCollection" /> class.</summary>
    </member>
    <member name="M:System.CodeDom.CodeNamespaceImportCollection.Add(System.CodeDom.CodeNamespaceImport)">
      <summary>Adds a <see cref="T:System.CodeDom.CodeNamespaceImport" /> object to the collection.</summary>
      <param name="value">The <see cref="T:System.CodeDom.CodeNamespaceImport" /> object to add to the collection.</param>
    </member>
    <member name="M:System.CodeDom.CodeNamespaceImportCollection.AddRange(System.CodeDom.CodeNamespaceImport[])">
      <summary>Adds a set of <see cref="T:System.CodeDom.CodeNamespaceImport" /> objects to the collection.</summary>
      <param name="value">An array of type <see cref="T:System.CodeDom.CodeNamespaceImport" /> that contains the objects to add to the collection.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.CodeDom.CodeNamespaceImportCollection.Clear">
      <summary>Clears the collection of members.</summary>
    </member>
    <member name="M:System.CodeDom.CodeNamespaceImportCollection.GetEnumerator">
      <summary>Gets an enumerator that enumerates the collection members.</summary>
      <returns>An <see cref="T:System.Collections.IEnumerator" /> that indicates the collection members.</returns>
    </member>
    <member name="M:System.CodeDom.CodeNamespaceImportCollection.System#Collections#ICollection#CopyTo(System.Array,System.Int32)">
      <summary>Copies the elements of the <see cref="T:System.Collections.ICollection" /> to an <see cref="T:System.Array" />, starting at a particular <see cref="T:System.Array" /> index.</summary>
      <param name="array">The one-dimensional <see cref="T:System.Array" /> that is the destination of the elements copied from the <see cref="T:System.Collections.ICollection" />. The array must have zero-based indexing.</param>
      <param name="index">The zero-based index in <paramref name="array" /> at which copying begins.</param>
    </member>
    <member name="M:System.CodeDom.CodeNamespaceImportCollection.System#Collections#IEnumerable#GetEnumerator">
      <summary>Returns an enumerator that can iterate through a collection.</summary>
      <returns>An <see cref="T:System.Collections.IEnumerator" /> that can be used to iterate through the collection.</returns>
    </member>
    <member name="M:System.CodeDom.CodeNamespaceImportCollection.System#Collections#IList#Add(System.Object)">
      <summary>Adds an object to the <see cref="T:System.Collections.IList" />.</summary>
      <param name="value">The <see cref="T:System.Object" /> to add to the <see cref="T:System.Collections.IList" />.</param>
      <returns>The position at which the new element was inserted.</returns>
    </member>
    <member name="M:System.CodeDom.CodeNamespaceImportCollection.System#Collections#IList#Clear">
      <summary>Removes all items from the <see cref="T:System.Collections.IList" />.</summary>
    </member>
    <member name="M:System.CodeDom.CodeNamespaceImportCollection.System#Collections#IList#Contains(System.Object)">
      <summary>Determines whether the <see cref="T:System.Collections.IList" /> contains a specific value.</summary>
      <param name="value">The <see cref="T:System.Object" /> to locate in the <see cref="T:System.Collections.IList" />.</param>
      <returns>
        <see langword="true" /> if the value is in the list; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.CodeDom.CodeNamespaceImportCollection.System#Collections#IList#IndexOf(System.Object)">
      <summary>Determines the index of a specific item in the <see cref="T:System.Collections.IList" />.</summary>
      <param name="value">The <see cref="T:System.Object" /> to locate in the <see cref="T:System.Collections.IList" />.</param>
      <returns>The index of <paramref name="value" /> if it is found in the list; otherwise, -1.</returns>
    </member>
    <member name="M:System.CodeDom.CodeNamespaceImportCollection.System#Collections#IList#Insert(System.Int32,System.Object)">
      <summary>Inserts an item in the <see cref="T:System.Collections.IList" /> at the specified position.</summary>
      <param name="index">The zero-based index at which <paramref name="value" /> should be inserted.</param>
      <param name="value">The <see cref="T:System.Object" /> to insert into the <see cref="T:System.Collections.IList" />.</param>
    </member>
    <member name="M:System.CodeDom.CodeNamespaceImportCollection.System#Collections#IList#Remove(System.Object)">
      <summary>Removes the first occurrence of a specific object from the <see cref="T:System.Collections.IList" />.</summary>
      <param name="value">The <see cref="T:System.Object" /> to remove from the <see cref="T:System.Collections.IList" />.</param>
    </member>
    <member name="M:System.CodeDom.CodeNamespaceImportCollection.System#Collections#IList#RemoveAt(System.Int32)">
      <summary>Removes the element at the specified index of the <see cref="T:System.Collections.IList" />.</summary>
      <param name="index">The zero-based index of the element to remove.</param>
    </member>
    <member name="P:System.CodeDom.CodeNamespaceImportCollection.Count">
      <summary>Gets the number of namespaces in the collection.</summary>
      <returns>The number of namespaces in the collection.</returns>
    </member>
    <member name="P:System.CodeDom.CodeNamespaceImportCollection.Item(System.Int32)">
      <summary>Gets or sets the <see cref="T:System.CodeDom.CodeNamespaceImport" /> object at the specified index in the collection.</summary>
      <param name="index">The index of the collection to access.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="index" /> parameter is outside the valid range of indexes for the collection.</exception>
      <returns>A <see cref="T:System.CodeDom.CodeNamespaceImport" /> object at each valid index.</returns>
    </member>
    <member name="P:System.CodeDom.CodeNamespaceImportCollection.System#Collections#ICollection#Count">
      <summary>Gets the number of elements contained in the <see cref="T:System.Collections.ICollection" />.</summary>
      <returns>The number of elements contained in the <see cref="T:System.Collections.ICollection" />.</returns>
    </member>
    <member name="P:System.CodeDom.CodeNamespaceImportCollection.System#Collections#ICollection#IsSynchronized">
      <summary>Gets a value indicating whether access to the <see cref="T:System.Collections.ICollection" /> is synchronized (thread safe).</summary>
      <returns>
        <see langword="true" /> if access to the <see cref="T:System.Collections.ICollection" /> is synchronized (thread safe); otherwise, <see langword="false" />. This property always returns <see langword="false" />.</returns>
    </member>
    <member name="P:System.CodeDom.CodeNamespaceImportCollection.System#Collections#ICollection#SyncRoot">
      <summary>Gets an object that can be used to synchronize access to the <see cref="T:System.Collections.ICollection" />.</summary>
      <returns>An object that can be used to synchronize access to the <see cref="T:System.Collections.ICollection" />.  This property always returns <see langword="null" />.</returns>
    </member>
    <member name="P:System.CodeDom.CodeNamespaceImportCollection.System#Collections#IList#IsFixedSize">
      <summary>Gets a value indicating whether the <see cref="T:System.Collections.IList" /> has a fixed size.</summary>
      <returns>
        <see langword="true" /> if the <see cref="T:System.Collections.IList" /> has a fixed size; otherwise, <see langword="false" />.  This property always returns <see langword="false" />.</returns>
    </member>
    <member name="P:System.CodeDom.CodeNamespaceImportCollection.System#Collections#IList#IsReadOnly">
      <summary>Gets a value indicating whether the <see cref="T:System.Collections.IList" /> is read-only.</summary>
      <returns>
        <see langword="true" /> if the <see cref="T:System.Collections.IList" /> is read-only; otherwise, <see langword="false" />.  This property always returns <see langword="false" />.</returns>
    </member>
    <member name="P:System.CodeDom.CodeNamespaceImportCollection.System#Collections#IList#Item(System.Int32)">
      <summary>Gets or sets the element at the specified index.</summary>
      <param name="index">The zero-based index of the element to get or set.</param>
      <returns>The element at the specified index.</returns>
    </member>
    <member name="T:System.CodeDom.CodeObject">
      <summary>Provides a common base class for most Code Document Object Model (CodeDOM) objects.</summary>
    </member>
    <member name="M:System.CodeDom.CodeObject.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.CodeDom.CodeObject" /> class.</summary>
    </member>
    <member name="P:System.CodeDom.CodeObject.UserData">
      <summary>Gets the user-definable data for the current object.</summary>
      <returns>An <see cref="T:System.Collections.IDictionary" /> containing user data for the current object.</returns>
    </member>
    <member name="T:System.CodeDom.CodeObjectCreateExpression">
      <summary>Represents an expression that creates a new instance of a type.</summary>
    </member>
    <member name="M:System.CodeDom.CodeObjectCreateExpression.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.CodeDom.CodeObjectCreateExpression" /> class.</summary>
    </member>
    <member name="M:System.CodeDom.CodeObjectCreateExpression.#ctor(System.CodeDom.CodeTypeReference,System.CodeDom.CodeExpression[])">
      <summary>Initializes a new instance of the <see cref="T:System.CodeDom.CodeObjectCreateExpression" /> class using the specified type and parameters.</summary>
      <param name="createType">A <see cref="T:System.CodeDom.CodeTypeReference" /> that indicates the data type of the object to create.</param>
      <param name="parameters">An array of <see cref="T:System.CodeDom.CodeExpression" /> objects that indicates the parameters to use to create the object.</param>
    </member>
    <member name="M:System.CodeDom.CodeObjectCreateExpression.#ctor(System.String,System.CodeDom.CodeExpression[])">
      <summary>Initializes a new instance of the <see cref="T:System.CodeDom.CodeObjectCreateExpression" /> class using the specified type and parameters.</summary>
      <param name="createType">The name of the data type of object to create.</param>
      <param name="parameters">An array of <see cref="T:System.CodeDom.CodeExpression" /> objects that indicates the parameters to use to create the object.</param>
    </member>
    <member name="M:System.CodeDom.CodeObjectCreateExpression.#ctor(System.Type,System.CodeDom.CodeExpression[])">
      <summary>Initializes a new instance of the <see cref="T:System.CodeDom.CodeObjectCreateExpression" /> class using the specified type and parameters.</summary>
      <param name="createType">The data type of the object to create.</param>
      <param name="parameters">An array of <see cref="T:System.CodeDom.CodeExpression" /> objects that indicates the parameters to use to create the object.</param>
    </member>
    <member name="P:System.CodeDom.CodeObjectCreateExpression.CreateType">
      <summary>Gets or sets the data type of the object to create.</summary>
      <returns>A <see cref="T:System.CodeDom.CodeTypeReference" /> to the data type of the object to create.</returns>
    </member>
    <member name="P:System.CodeDom.CodeObjectCreateExpression.Parameters">
      <summary>Gets or sets the parameters to use in creating the object.</summary>
      <returns>A <see cref="T:System.CodeDom.CodeExpressionCollection" /> that indicates the parameters to use when creating the object.</returns>
    </member>
    <member name="T:System.CodeDom.CodeParameterDeclarationExpression">
      <summary>Represents a parameter declaration for a method, property, or constructor.</summary>
    </member>
    <member name="M:System.CodeDom.CodeParameterDeclarationExpression.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.CodeDom.CodeParameterDeclarationExpression" /> class.</summary>
    </member>
    <member name="M:System.CodeDom.CodeParameterDeclarationExpression.#ctor(System.CodeDom.CodeTypeReference,System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.CodeDom.CodeParameterDeclarationExpression" /> class using the specified parameter type and name.</summary>
      <param name="type">An object that indicates the type of the parameter to declare.</param>
      <param name="name">The name of the parameter to declare.</param>
    </member>
    <member name="M:System.CodeDom.CodeParameterDeclarationExpression.#ctor(System.String,System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.CodeDom.CodeParameterDeclarationExpression" /> class using the specified parameter type and name.</summary>
      <param name="type">The type of the parameter to declare.</param>
      <param name="name">The name of the parameter to declare.</param>
    </member>
    <member name="M:System.CodeDom.CodeParameterDeclarationExpression.#ctor(System.Type,System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.CodeDom.CodeParameterDeclarationExpression" /> class using the specified parameter type and name.</summary>
      <param name="type">The type of the parameter to declare.</param>
      <param name="name">The name of the parameter to declare.</param>
    </member>
    <member name="P:System.CodeDom.CodeParameterDeclarationExpression.CustomAttributes">
      <summary>Gets or sets the custom attributes for the parameter declaration.</summary>
      <returns>An object that indicates the custom attributes.</returns>
    </member>
    <member name="P:System.CodeDom.CodeParameterDeclarationExpression.Direction">
      <summary>Gets or sets the direction of the field.</summary>
      <returns>An object that indicates the direction of the field.</returns>
    </member>
    <member name="P:System.CodeDom.CodeParameterDeclarationExpression.Name">
      <summary>Gets or sets the name of the parameter.</summary>
      <returns>The name of the parameter.</returns>
    </member>
    <member name="P:System.CodeDom.CodeParameterDeclarationExpression.Type">
      <summary>Gets or sets the type of the parameter.</summary>
      <returns>The type of the parameter.</returns>
    </member>
    <member name="T:System.CodeDom.CodeParameterDeclarationExpressionCollection">
      <summary>Represents a collection of <see cref="T:System.CodeDom.CodeParameterDeclarationExpression" /> objects.</summary>
    </member>
    <member name="M:System.CodeDom.CodeParameterDeclarationExpressionCollection.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.CodeDom.CodeParameterDeclarationExpressionCollection" /> class.</summary>
    </member>
    <member name="M:System.CodeDom.CodeParameterDeclarationExpressionCollection.#ctor(System.CodeDom.CodeParameterDeclarationExpression[])">
      <summary>Initializes a new instance of the <see cref="T:System.CodeDom.CodeParameterDeclarationExpressionCollection" /> class containing the specified array of <see cref="T:System.CodeDom.CodeParameterDeclarationExpression" /> objects.</summary>
      <param name="value">An array of <see cref="T:System.CodeDom.CodeParameterDeclarationExpression" /> objects with which to initialize the collection.</param>
      <exception cref="T:System.ArgumentNullException">one or more objects in the array are <see langword="null" />.</exception>
    </member>
    <member name="M:System.CodeDom.CodeParameterDeclarationExpressionCollection.#ctor(System.CodeDom.CodeParameterDeclarationExpressionCollection)">
      <summary>Initializes a new instance of the <see cref="T:System.CodeDom.CodeParameterDeclarationExpressionCollection" /> class containing the elements of the specified source collection.</summary>
      <param name="value">A <see cref="T:System.CodeDom.CodeParameterDeclarationExpressionCollection" /> with which to initialize the collection.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.CodeDom.CodeParameterDeclarationExpressionCollection.Add(System.CodeDom.CodeParameterDeclarationExpression)">
      <summary>Adds the specified <see cref="T:System.CodeDom.CodeParameterDeclarationExpression" /> to the collection.</summary>
      <param name="value">The <see cref="T:System.CodeDom.CodeParameterDeclarationExpression" /> to add.</param>
      <returns>The index at which the new element was inserted.</returns>
    </member>
    <member name="M:System.CodeDom.CodeParameterDeclarationExpressionCollection.AddRange(System.CodeDom.CodeParameterDeclarationExpression[])">
      <summary>Copies the elements of the specified array to the end of the collection.</summary>
      <param name="value">An array of type <see cref="T:System.CodeDom.CodeParameterDeclarationExpression" /> containing the objects to add to the collection.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.CodeDom.CodeParameterDeclarationExpressionCollection.AddRange(System.CodeDom.CodeParameterDeclarationExpressionCollection)">
      <summary>Adds the contents of another <see cref="T:System.CodeDom.CodeParameterDeclarationExpressionCollection" /> to the end of the collection.</summary>
      <param name="value">A <see cref="T:System.CodeDom.CodeParameterDeclarationExpressionCollection" /> containing the objects to add to the collection.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.CodeDom.CodeParameterDeclarationExpressionCollection.Contains(System.CodeDom.CodeParameterDeclarationExpression)">
      <summary>Gets a value indicating whether the collection contains the specified <see cref="T:System.CodeDom.CodeParameterDeclarationExpression" />.</summary>
      <param name="value">A <see cref="T:System.CodeDom.CodeParameterDeclarationExpression" /> to search for in the collection.</param>
      <returns>
        <see langword="true" /> if the collection contains the specified object; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.CodeDom.CodeParameterDeclarationExpressionCollection.CopyTo(System.CodeDom.CodeParameterDeclarationExpression[],System.Int32)">
      <summary>Copies the collection objects to a one-dimensional <see cref="T:System.Array" /> instance beginning at the specified index.</summary>
      <param name="array">The one-dimensional <see cref="T:System.Array" /> that is the destination of the values copied from the collection.</param>
      <param name="index">The index of the array at which to begin inserting.</param>
      <exception cref="T:System.ArgumentException">The destination array is multidimensional.  
  
 -or-  
  
 The number of elements in the <see cref="T:System.CodeDom.CodeParameterDeclarationExpressionCollection" /> is greater than the available space between the index of the target array specified by the <paramref name="index" /> parameter and the end of the target array.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="array" /> parameter is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="index" /> parameter is less than the target array's minimum index.</exception>
    </member>
    <member name="M:System.CodeDom.CodeParameterDeclarationExpressionCollection.IndexOf(System.CodeDom.CodeParameterDeclarationExpression)">
      <summary>Gets the index in the collection of the specified <see cref="T:System.CodeDom.CodeParameterDeclarationExpression" />, if it exists in the collection.</summary>
      <param name="value">The <see cref="T:System.CodeDom.CodeParameterDeclarationExpression" /> to locate in the collection.</param>
      <returns>The index in the collection of the specified object, if found; otherwise, -1.</returns>
    </member>
    <member name="M:System.CodeDom.CodeParameterDeclarationExpressionCollection.Insert(System.Int32,System.CodeDom.CodeParameterDeclarationExpression)">
      <summary>Inserts the specified <see cref="T:System.CodeDom.CodeParameterDeclarationExpression" /> into the collection at the specified index.</summary>
      <param name="index">The zero-based index where the specified object should be inserted.</param>
      <param name="value">The <see cref="T:System.CodeDom.CodeParameterDeclarationExpression" /> to insert.</param>
    </member>
    <member name="M:System.CodeDom.CodeParameterDeclarationExpressionCollection.Remove(System.CodeDom.CodeParameterDeclarationExpression)">
      <summary>Removes the specified <see cref="T:System.CodeDom.CodeParameterDeclarationExpression" /> from the collection.</summary>
      <param name="value">The <see cref="T:System.CodeDom.CodeParameterDeclarationExpression" /> to remove from the collection.</param>
      <exception cref="T:System.ArgumentException">The specified object is not found in the collection.</exception>
    </member>
    <member name="P:System.CodeDom.CodeParameterDeclarationExpressionCollection.Item(System.Int32)">
      <summary>Gets or sets the <see cref="T:System.CodeDom.CodeParameterDeclarationExpression" /> at the specified index in the collection.</summary>
      <param name="index">The index of the collection to access.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="index" /> parameter is outside the valid range of indexes for the collection.</exception>
      <returns>A <see cref="T:System.CodeDom.CodeParameterDeclarationExpression" /> at each valid index.</returns>
    </member>
    <member name="T:System.CodeDom.CodePrimitiveExpression">
      <summary>Represents a primitive data type value.</summary>
    </member>
    <member name="M:System.CodeDom.CodePrimitiveExpression.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.CodeDom.CodePrimitiveExpression" /> class.</summary>
    </member>
    <member name="M:System.CodeDom.CodePrimitiveExpression.#ctor(System.Object)">
      <summary>Initializes a new instance of the <see cref="T:System.CodeDom.CodePrimitiveExpression" /> class using the specified object.</summary>
      <param name="value">The object to represent.</param>
    </member>
    <member name="P:System.CodeDom.CodePrimitiveExpression.Value">
      <summary>Gets or sets the primitive data type to represent.</summary>
      <returns>The primitive data type instance to represent the value of.</returns>
    </member>
    <member name="T:System.CodeDom.CodePropertyReferenceExpression">
      <summary>Represents a reference to the value of a property.</summary>
    </member>
    <member name="M:System.CodeDom.CodePropertyReferenceExpression.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.CodeDom.CodePropertyReferenceExpression" /> class.</summary>
    </member>
    <member name="M:System.CodeDom.CodePropertyReferenceExpression.#ctor(System.CodeDom.CodeExpression,System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.CodeDom.CodePropertyReferenceExpression" /> class using the specified target object and property name.</summary>
      <param name="targetObject">A <see cref="T:System.CodeDom.CodeExpression" /> that indicates the object that contains the property to reference.</param>
      <param name="propertyName">The name of the property to reference.</param>
    </member>
    <member name="P:System.CodeDom.CodePropertyReferenceExpression.PropertyName">
      <summary>Gets or sets the name of the property to reference.</summary>
      <returns>The name of the property to reference.</returns>
    </member>
    <member name="P:System.CodeDom.CodePropertyReferenceExpression.TargetObject">
      <summary>Gets or sets the object that contains the property to reference.</summary>
      <returns>A <see cref="T:System.CodeDom.CodeExpression" /> that indicates the object that contains the property to reference.</returns>
    </member>
    <member name="T:System.CodeDom.CodePropertySetValueReferenceExpression">
      <summary>Represents the value argument of a property set method call within a property set method.</summary>
    </member>
    <member name="M:System.CodeDom.CodePropertySetValueReferenceExpression.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.CodeDom.CodePropertySetValueReferenceExpression" /> class.</summary>
    </member>
    <member name="T:System.CodeDom.CodeRegionDirective">
      <summary>Specifies the name and mode for a code region.</summary>
    </member>
    <member name="M:System.CodeDom.CodeRegionDirective.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.CodeDom.CodeRegionDirective" /> class with default values.</summary>
    </member>
    <member name="M:System.CodeDom.CodeRegionDirective.#ctor(System.CodeDom.CodeRegionMode,System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.CodeDom.CodeRegionDirective" /> class, specifying its mode and name.</summary>
      <param name="regionMode">One of the <see cref="T:System.CodeDom.CodeRegionMode" /> values.</param>
      <param name="regionText">The name for the region.</param>
    </member>
    <member name="P:System.CodeDom.CodeRegionDirective.RegionMode">
      <summary>Gets or sets the mode for the region directive.</summary>
      <returns>One of the <see cref="T:System.CodeDom.CodeRegionMode" /> values. The default is <see cref="F:System.CodeDom.CodeRegionMode.None" />.</returns>
    </member>
    <member name="P:System.CodeDom.CodeRegionDirective.RegionText">
      <summary>Gets or sets the name of the region.</summary>
      <returns>The name of the region.</returns>
    </member>
    <member name="T:System.CodeDom.CodeRegionMode">
      <summary>Specifies the start or end of a code region.</summary>
    </member>
    <member name="F:System.CodeDom.CodeRegionMode.End">
      <summary>End of the region.</summary>
    </member>
    <member name="F:System.CodeDom.CodeRegionMode.None">
      <summary>Not used.</summary>
    </member>
    <member name="F:System.CodeDom.CodeRegionMode.Start">
      <summary>Start of the region.</summary>
    </member>
    <member name="T:System.CodeDom.CodeRemoveEventStatement">
      <summary>Represents a statement that removes an event handler.</summary>
    </member>
    <member name="M:System.CodeDom.CodeRemoveEventStatement.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.CodeDom.CodeRemoveEventStatement" /> class.</summary>
    </member>
    <member name="M:System.CodeDom.CodeRemoveEventStatement.#ctor(System.CodeDom.CodeEventReferenceExpression,System.CodeDom.CodeExpression)">
      <summary>Initializes a new instance of the <see cref="T:System.CodeDom.CodeRemoveEventStatement" /> class with the specified event and event handler.</summary>
      <param name="eventRef">A <see cref="T:System.CodeDom.CodeEventReferenceExpression" /> that indicates the event to detach the event handler from.</param>
      <param name="listener">A <see cref="T:System.CodeDom.CodeExpression" /> that indicates the event handler to remove.</param>
    </member>
    <member name="M:System.CodeDom.CodeRemoveEventStatement.#ctor(System.CodeDom.CodeExpression,System.String,System.CodeDom.CodeExpression)">
      <summary>Initializes a new instance of the <see cref="T:System.CodeDom.CodeRemoveEventStatement" /> class using the specified target object, event name, and event handler.</summary>
      <param name="targetObject">A <see cref="T:System.CodeDom.CodeExpression" /> that indicates the object that contains the event.</param>
      <param name="eventName">The name of the event.</param>
      <param name="listener">A <see cref="T:System.CodeDom.CodeExpression" /> that indicates the event handler to remove.</param>
    </member>
    <member name="P:System.CodeDom.CodeRemoveEventStatement.Event">
      <summary>Gets or sets the event to remove a listener from.</summary>
      <returns>A <see cref="T:System.CodeDom.CodeEventReferenceExpression" /> that indicates the event to remove a listener from.</returns>
    </member>
    <member name="P:System.CodeDom.CodeRemoveEventStatement.Listener">
      <summary>Gets or sets the event handler to remove.</summary>
      <returns>A <see cref="T:System.CodeDom.CodeExpression" /> that indicates the event handler to remove.</returns>
    </member>
    <member name="T:System.CodeDom.CodeSnippetCompileUnit">
      <summary>Represents a literal code fragment that can be compiled.</summary>
    </member>
    <member name="M:System.CodeDom.CodeSnippetCompileUnit.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.CodeDom.CodeSnippetCompileUnit" /> class.</summary>
    </member>
    <member name="M:System.CodeDom.CodeSnippetCompileUnit.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.CodeDom.CodeSnippetCompileUnit" /> class.</summary>
      <param name="value">The literal code fragment to represent.</param>
    </member>
    <member name="P:System.CodeDom.CodeSnippetCompileUnit.LinePragma">
      <summary>Gets or sets the line and file information about where the code is located in a source code document.</summary>
      <returns>A <see cref="T:System.CodeDom.CodeLinePragma" /> that indicates the position of the code fragment.</returns>
    </member>
    <member name="P:System.CodeDom.CodeSnippetCompileUnit.Value">
      <summary>Gets or sets the literal code fragment to represent.</summary>
      <returns>The literal code fragment.</returns>
    </member>
    <member name="T:System.CodeDom.CodeSnippetExpression">
      <summary>Represents a literal expression.</summary>
    </member>
    <member name="M:System.CodeDom.CodeSnippetExpression.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.CodeDom.CodeSnippetExpression" /> class.</summary>
    </member>
    <member name="M:System.CodeDom.CodeSnippetExpression.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.CodeDom.CodeSnippetExpression" /> class using the specified literal expression.</summary>
      <param name="value">The literal expression to represent.</param>
    </member>
    <member name="P:System.CodeDom.CodeSnippetExpression.Value">
      <summary>Gets or sets the literal string of code.</summary>
      <returns>The literal string.</returns>
    </member>
    <member name="T:System.CodeDom.CodeSnippetStatement">
      <summary>Represents a statement using a literal code fragment.</summary>
    </member>
    <member name="M:System.CodeDom.CodeSnippetStatement.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.CodeDom.CodeSnippetStatement" /> class.</summary>
    </member>
    <member name="M:System.CodeDom.CodeSnippetStatement.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.CodeDom.CodeSnippetStatement" /> class using the specified code fragment.</summary>
      <param name="value">The literal code fragment of the statement to represent.</param>
    </member>
    <member name="P:System.CodeDom.CodeSnippetStatement.Value">
      <summary>Gets or sets the literal code fragment statement.</summary>
      <returns>The literal code fragment statement.</returns>
    </member>
    <member name="T:System.CodeDom.CodeSnippetTypeMember">
      <summary>Represents a member of a type using a literal code fragment.</summary>
    </member>
    <member name="M:System.CodeDom.CodeSnippetTypeMember.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.CodeDom.CodeSnippetTypeMember" /> class.</summary>
    </member>
    <member name="M:System.CodeDom.CodeSnippetTypeMember.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.CodeDom.CodeSnippetTypeMember" /> class using the specified text.</summary>
      <param name="text">The literal code fragment for the type member.</param>
    </member>
    <member name="P:System.CodeDom.CodeSnippetTypeMember.Text">
      <summary>Gets or sets the literal code fragment for the type member.</summary>
      <returns>The literal code fragment for the type member.</returns>
    </member>
    <member name="T:System.CodeDom.CodeStatement">
      <summary>Represents the <see langword="abstract" /> base class from which all code statements derive.</summary>
    </member>
    <member name="M:System.CodeDom.CodeStatement.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.CodeDom.CodeStatement" /> class.</summary>
    </member>
    <member name="P:System.CodeDom.CodeStatement.EndDirectives">
      <summary>Gets a <see cref="T:System.CodeDom.CodeDirectiveCollection" /> object that contains end directives.</summary>
      <returns>A <see cref="T:System.CodeDom.CodeDirectiveCollection" /> object containing end directives.</returns>
    </member>
    <member name="P:System.CodeDom.CodeStatement.LinePragma">
      <summary>Gets or sets the line on which the code statement occurs.</summary>
      <returns>A <see cref="T:System.CodeDom.CodeLinePragma" /> object that indicates the context of the code statement.</returns>
    </member>
    <member name="P:System.CodeDom.CodeStatement.StartDirectives">
      <summary>Gets a <see cref="T:System.CodeDom.CodeDirectiveCollection" /> object that contains start directives.</summary>
      <returns>A <see cref="T:System.CodeDom.CodeDirectiveCollection" /> object containing start directives.</returns>
    </member>
    <member name="T:System.CodeDom.CodeStatementCollection">
      <summary>Represents a collection of <see cref="T:System.CodeDom.CodeStatement" /> objects.</summary>
    </member>
    <member name="M:System.CodeDom.CodeStatementCollection.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.CodeDom.CodeStatementCollection" /> class.</summary>
    </member>
    <member name="M:System.CodeDom.CodeStatementCollection.#ctor(System.CodeDom.CodeStatement[])">
      <summary>Initializes a new instance of the <see cref="T:System.CodeDom.CodeStatementCollection" /> class that contains the specified array of <see cref="T:System.CodeDom.CodeStatement" /> objects.</summary>
      <param name="value">An array of <see cref="T:System.CodeDom.CodeStatement" /> objects with which to initialize the collection.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.CodeDom.CodeStatementCollection.#ctor(System.CodeDom.CodeStatementCollection)">
      <summary>Initializes a new instance of the <see cref="T:System.CodeDom.CodeStatementCollection" /> class that contains the elements of the specified source collection.</summary>
      <param name="value">A <see cref="T:System.CodeDom.CodeStatementCollection" /> object with which to initialize the collection.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.CodeDom.CodeStatementCollection.Add(System.CodeDom.CodeExpression)">
      <summary>Adds the specified <see cref="T:System.CodeDom.CodeExpression" /> object to the collection.</summary>
      <param name="value">The <see cref="T:System.CodeDom.CodeExpression" /> object to add.</param>
      <returns>The index at which the new element was inserted.</returns>
    </member>
    <member name="M:System.CodeDom.CodeStatementCollection.Add(System.CodeDom.CodeStatement)">
      <summary>Adds the specified <see cref="T:System.CodeDom.CodeStatement" /> object to the collection.</summary>
      <param name="value">The <see cref="T:System.CodeDom.CodeStatement" /> object to add.</param>
      <returns>The index at which the new element was inserted.</returns>
    </member>
    <member name="M:System.CodeDom.CodeStatementCollection.AddRange(System.CodeDom.CodeStatement[])">
      <summary>Adds a set of <see cref="T:System.CodeDom.CodeStatement" /> objects to the collection.</summary>
      <param name="value">An array of <see cref="T:System.CodeDom.CodeStatement" /> objects to add to the collection.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.CodeDom.CodeStatementCollection.AddRange(System.CodeDom.CodeStatementCollection)">
      <summary>Adds the contents of another <see cref="T:System.CodeDom.CodeStatementCollection" /> object to the end of the collection.</summary>
      <param name="value">A <see cref="T:System.CodeDom.CodeStatementCollection" /> object that contains the objects to add to the collection.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.CodeDom.CodeStatementCollection.Contains(System.CodeDom.CodeStatement)">
      <summary>Gets a value that indicates whether the collection contains the specified <see cref="T:System.CodeDom.CodeStatement" /> object.</summary>
      <param name="value">The <see cref="T:System.CodeDom.CodeStatement" /> object to search for in the collection.</param>
      <returns>
        <see langword="true" /> if the collection contains the specified object; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.CodeDom.CodeStatementCollection.CopyTo(System.CodeDom.CodeStatement[],System.Int32)">
      <summary>Copies the elements of the <see cref="T:System.CodeDom.CodeStatementCollection" /> object to a one-dimensional <see cref="T:System.Array" /> instance, starting at the specified index.</summary>
      <param name="array">The one-dimensional <see cref="T:System.Array" /> that is the destination of the values copied from the collection.</param>
      <param name="index">The index of the array at which to begin inserting.</param>
      <exception cref="T:System.ArgumentException">The destination array is multidimensional.  
  
 -or-  
  
 The number of elements in the <see cref="T:System.CodeDom.CodeStatementCollection" /> is greater than the available space between the index of the target array specified by the <paramref name="index" /> parameter and the end of the target array.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="array" /> parameter is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="index" /> parameter is less than the target array's minimum index.</exception>
    </member>
    <member name="M:System.CodeDom.CodeStatementCollection.IndexOf(System.CodeDom.CodeStatement)">
      <summary>Gets the index of the specified <see cref="T:System.CodeDom.CodeStatement" /> object in the <see cref="T:System.CodeDom.CodeStatementCollection" />, if it exists in the collection.</summary>
      <param name="value">The <see cref="T:System.CodeDom.CodeStatement" /> to locate in the collection.</param>
      <returns>The index of the specified object, if it is found, in the collection; otherwise, -1.</returns>
    </member>
    <member name="M:System.CodeDom.CodeStatementCollection.Insert(System.Int32,System.CodeDom.CodeStatement)">
      <summary>Inserts the specified <see cref="T:System.CodeDom.CodeStatement" /> object into the collection at the specified index.</summary>
      <param name="index">The zero-based index where the specified object should be inserted.</param>
      <param name="value">The <see cref="T:System.CodeDom.CodeStatement" /> object to insert.</param>
    </member>
    <member name="M:System.CodeDom.CodeStatementCollection.Remove(System.CodeDom.CodeStatement)">
      <summary>Removes the specified <see cref="T:System.CodeDom.CodeStatement" /> object from the collection.</summary>
      <param name="value">The <see cref="T:System.CodeDom.CodeStatement" /> to remove from the collection.</param>
      <exception cref="T:System.ArgumentException">The specified object is not found in the collection.</exception>
    </member>
    <member name="P:System.CodeDom.CodeStatementCollection.Item(System.Int32)">
      <summary>Gets or sets the <see cref="T:System.CodeDom.CodeStatement" /> object at the specified index in the collection.</summary>
      <param name="index">The index of the collection to access.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="index" /> parameter is outside the valid range of indexes for the collection.</exception>
      <returns>A <see cref="T:System.CodeDom.CodeStatement" /> at each valid index.</returns>
    </member>
    <member name="T:System.CodeDom.CodeThisReferenceExpression">
      <summary>Represents a reference to the current local class instance.</summary>
    </member>
    <member name="M:System.CodeDom.CodeThisReferenceExpression.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.CodeDom.CodeThisReferenceExpression" /> class.</summary>
    </member>
    <member name="T:System.CodeDom.CodeThrowExceptionStatement">
      <summary>Represents a statement that throws an exception.</summary>
    </member>
    <member name="M:System.CodeDom.CodeThrowExceptionStatement.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.CodeDom.CodeThrowExceptionStatement" /> class.</summary>
    </member>
    <member name="M:System.CodeDom.CodeThrowExceptionStatement.#ctor(System.CodeDom.CodeExpression)">
      <summary>Initializes a new instance of the <see cref="T:System.CodeDom.CodeThrowExceptionStatement" /> class with the specified exception type instance.</summary>
      <param name="toThrow">A <see cref="T:System.CodeDom.CodeExpression" /> that indicates the exception to throw.</param>
    </member>
    <member name="P:System.CodeDom.CodeThrowExceptionStatement.ToThrow">
      <summary>Gets or sets the exception to throw.</summary>
      <returns>A <see cref="T:System.CodeDom.CodeExpression" /> representing an instance of the exception to throw.</returns>
    </member>
    <member name="T:System.CodeDom.CodeTryCatchFinallyStatement">
      <summary>Represents a <see langword="try" /> block with any number of <see langword="catch" /> clauses and, optionally, a <see langword="finally" /> block.</summary>
    </member>
    <member name="M:System.CodeDom.CodeTryCatchFinallyStatement.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.CodeDom.CodeTryCatchFinallyStatement" /> class.</summary>
    </member>
    <member name="M:System.CodeDom.CodeTryCatchFinallyStatement.#ctor(System.CodeDom.CodeStatement[],System.CodeDom.CodeCatchClause[])">
      <summary>Initializes a new instance of the <see cref="T:System.CodeDom.CodeTryCatchFinallyStatement" /> class using the specified statements for try and catch clauses.</summary>
      <param name="tryStatements">An array of <see cref="T:System.CodeDom.CodeStatement" /> objects that indicate the statements to try.</param>
      <param name="catchClauses">An array of <see cref="T:System.CodeDom.CodeCatchClause" /> objects that indicate the clauses to catch.</param>
    </member>
    <member name="M:System.CodeDom.CodeTryCatchFinallyStatement.#ctor(System.CodeDom.CodeStatement[],System.CodeDom.CodeCatchClause[],System.CodeDom.CodeStatement[])">
      <summary>Initializes a new instance of the <see cref="T:System.CodeDom.CodeTryCatchFinallyStatement" /> class using the specified statements for try, catch clauses, and finally statements.</summary>
      <param name="tryStatements">An array of <see cref="T:System.CodeDom.CodeStatement" /> objects that indicate the statements to try.</param>
      <param name="catchClauses">An array of <see cref="T:System.CodeDom.CodeCatchClause" /> objects that indicate the clauses to catch.</param>
      <param name="finallyStatements">An array of <see cref="T:System.CodeDom.CodeStatement" /> objects that indicate the finally statements to use.</param>
    </member>
    <member name="P:System.CodeDom.CodeTryCatchFinallyStatement.CatchClauses">
      <summary>Gets the catch clauses to use.</summary>
      <returns>A <see cref="T:System.CodeDom.CodeCatchClauseCollection" /> that indicates the catch clauses to use.</returns>
    </member>
    <member name="P:System.CodeDom.CodeTryCatchFinallyStatement.FinallyStatements">
      <summary>Gets the finally statements to use.</summary>
      <returns>A <see cref="T:System.CodeDom.CodeStatementCollection" /> that indicates the finally statements.</returns>
    </member>
    <member name="P:System.CodeDom.CodeTryCatchFinallyStatement.TryStatements">
      <summary>Gets the statements to try.</summary>
      <returns>A <see cref="T:System.CodeDom.CodeStatementCollection" /> that indicates the statements to try.</returns>
    </member>
    <member name="T:System.CodeDom.CodeTypeConstructor">
      <summary>Represents a static constructor for a class.</summary>
    </member>
    <member name="M:System.CodeDom.CodeTypeConstructor.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.CodeDom.CodeTypeConstructor" /> class.</summary>
    </member>
    <member name="T:System.CodeDom.CodeTypeDeclaration">
      <summary>Represents a type declaration for a class, structure, interface, or enumeration.</summary>
    </member>
    <member name="E:System.CodeDom.CodeTypeDeclaration.PopulateBaseTypes">
      <summary>Occurs when the <see cref="P:System.CodeDom.CodeTypeDeclaration.BaseTypes" /> collection is accessed for the first time.</summary>
    </member>
    <member name="E:System.CodeDom.CodeTypeDeclaration.PopulateMembers">
      <summary>Occurs when the <see cref="P:System.CodeDom.CodeTypeDeclaration.Members" /> collection is accessed for the first time.</summary>
    </member>
    <member name="M:System.CodeDom.CodeTypeDeclaration.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.CodeDom.CodeTypeDeclaration" /> class.</summary>
    </member>
    <member name="M:System.CodeDom.CodeTypeDeclaration.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.CodeDom.CodeTypeDeclaration" /> class with the specified name.</summary>
      <param name="name">The name for the new type.</param>
    </member>
    <member name="P:System.CodeDom.CodeTypeDeclaration.BaseTypes">
      <summary>Gets the base types of the type.</summary>
      <returns>A <see cref="T:System.CodeDom.CodeTypeReferenceCollection" /> object that indicates the base types of the type.</returns>
    </member>
    <member name="P:System.CodeDom.CodeTypeDeclaration.IsClass">
      <summary>Gets or sets a value indicating whether the type is a class or reference type.</summary>
      <returns>
        <see langword="true" /> if the type is a class or reference type; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.CodeDom.CodeTypeDeclaration.IsEnum">
      <summary>Gets or sets a value indicating whether the type is an enumeration.</summary>
      <returns>
        <see langword="true" /> if the type is an enumeration; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.CodeDom.CodeTypeDeclaration.IsInterface">
      <summary>Gets or sets a value indicating whether the type is an interface.</summary>
      <returns>
        <see langword="true" /> if the type is an interface; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.CodeDom.CodeTypeDeclaration.IsPartial">
      <summary>Gets or sets a value indicating whether the type declaration is complete or partial.</summary>
      <returns>
        <see langword="true" /> if the class or structure declaration is a partial representation of the implementation; <see langword="false" /> if the declaration is a complete implementation of the class or structure. The default is <see langword="false" />.</returns>
    </member>
    <member name="P:System.CodeDom.CodeTypeDeclaration.IsStruct">
      <summary>Gets or sets a value indicating whether the type is a value type (struct).</summary>
      <returns>
        <see langword="true" /> if the type is a value type; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.CodeDom.CodeTypeDeclaration.Members">
      <summary>Gets the collection of class members for the represented type.</summary>
      <returns>A <see cref="T:System.CodeDom.CodeTypeMemberCollection" /> object that indicates the class members.</returns>
    </member>
    <member name="P:System.CodeDom.CodeTypeDeclaration.TypeAttributes">
      <summary>Gets or sets the attributes of the type.</summary>
      <returns>A <see cref="T:System.Reflection.TypeAttributes" /> object that indicates the attributes of the type.</returns>
    </member>
    <member name="P:System.CodeDom.CodeTypeDeclaration.TypeParameters">
      <summary>Gets the type parameters for the type declaration.</summary>
      <returns>A <see cref="T:System.CodeDom.CodeTypeParameterCollection" /> that contains the type parameters for the type declaration.</returns>
    </member>
    <member name="T:System.CodeDom.CodeTypeDeclarationCollection">
      <summary>Represents a collection of <see cref="T:System.CodeDom.CodeTypeDeclaration" /> objects.</summary>
    </member>
    <member name="M:System.CodeDom.CodeTypeDeclarationCollection.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.CodeDom.CodeTypeDeclarationCollection" /> class.</summary>
    </member>
    <member name="M:System.CodeDom.CodeTypeDeclarationCollection.#ctor(System.CodeDom.CodeTypeDeclaration[])">
      <summary>Initializes a new instance of the <see cref="T:System.CodeDom.CodeTypeDeclarationCollection" /> class that contains the specified array of <see cref="T:System.CodeDom.CodeTypeDeclaration" /> objects.</summary>
      <param name="value">An array of <see cref="T:System.CodeDom.CodeTypeDeclaration" /> objects with which to initialize the collection.</param>
    </member>
    <member name="M:System.CodeDom.CodeTypeDeclarationCollection.#ctor(System.CodeDom.CodeTypeDeclarationCollection)">
      <summary>Initializes a new instance of the <see cref="T:System.CodeDom.CodeTypeDeclarationCollection" /> class that contains the elements of the specified source collection.</summary>
      <param name="value">A <see cref="T:System.CodeDom.CodeTypeDeclarationCollection" /> object with which to initialize the collection.</param>
    </member>
    <member name="M:System.CodeDom.CodeTypeDeclarationCollection.Add(System.CodeDom.CodeTypeDeclaration)">
      <summary>Adds the specified <see cref="T:System.CodeDom.CodeTypeDeclaration" /> object to the collection.</summary>
      <param name="value">The <see cref="T:System.CodeDom.CodeTypeDeclaration" /> object to add.</param>
      <returns>The index at which the new element was inserted.</returns>
    </member>
    <member name="M:System.CodeDom.CodeTypeDeclarationCollection.AddRange(System.CodeDom.CodeTypeDeclaration[])">
      <summary>Copies the elements of the specified array to the end of the collection.</summary>
      <param name="value">An array of type <see cref="T:System.CodeDom.CodeTypeDeclaration" /> that contains the objects to add to the collection.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.CodeDom.CodeTypeDeclarationCollection.AddRange(System.CodeDom.CodeTypeDeclarationCollection)">
      <summary>Adds the contents of another <see cref="T:System.CodeDom.CodeTypeDeclarationCollection" /> object to the end of the collection.</summary>
      <param name="value">A <see cref="T:System.CodeDom.CodeTypeDeclarationCollection" /> object that contains the objects to add to the collection.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.CodeDom.CodeTypeDeclarationCollection.Contains(System.CodeDom.CodeTypeDeclaration)">
      <summary>Gets a value that indicates whether the collection contains the specified <see cref="T:System.CodeDom.CodeTypeDeclaration" /> object.</summary>
      <param name="value">The <see cref="T:System.CodeDom.CodeTypeDeclaration" /> object to search for in the collection.</param>
      <returns>
        <see langword="true" /> if the collection contains the specified object; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.CodeDom.CodeTypeDeclarationCollection.CopyTo(System.CodeDom.CodeTypeDeclaration[],System.Int32)">
      <summary>Copies the elements in the <see cref="T:System.CodeDom.CodeTypeDeclarationCollection" /> object to a one-dimensional <see cref="T:System.Array" /> instance, starting at the specified index.</summary>
      <param name="array">The one-dimensional <see cref="T:System.Array" /> that is the destination of the values copied from the collection.</param>
      <param name="index">The index of the array at which to begin inserting.</param>
      <exception cref="T:System.ArgumentException">The destination array is multidimensional.  
  
 -or-  
  
 The number of elements in the <see cref="T:System.CodeDom.CodeTypeDeclarationCollection" /> is greater than the available space between the index of the target array specified by the <paramref name="index" /> parameter and the end of the target array.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="array" /> parameter is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="index" /> parameter is less than the target array's minimum index.</exception>
    </member>
    <member name="M:System.CodeDom.CodeTypeDeclarationCollection.IndexOf(System.CodeDom.CodeTypeDeclaration)">
      <summary>Gets the index of the specified <see cref="T:System.CodeDom.CodeTypeDeclaration" /> object in the <see cref="T:System.CodeDom.CodeTypeDeclarationCollection" />, if it exists in the collection.</summary>
      <param name="value">The <see cref="T:System.CodeDom.CodeTypeDeclaration" /> to locate in the collection.</param>
      <returns>The index of the specified object, if it is found, in the collection; otherwise, -1.</returns>
    </member>
    <member name="M:System.CodeDom.CodeTypeDeclarationCollection.Insert(System.Int32,System.CodeDom.CodeTypeDeclaration)">
      <summary>Inserts the specified <see cref="T:System.CodeDom.CodeTypeDeclaration" /> object into the collection at the specified index.</summary>
      <param name="index">The zero-based index where the specified object should be inserted.</param>
      <param name="value">The <see cref="T:System.CodeDom.CodeTypeDeclaration" /> object to insert.</param>
    </member>
    <member name="M:System.CodeDom.CodeTypeDeclarationCollection.Remove(System.CodeDom.CodeTypeDeclaration)">
      <summary>Removes the specified <see cref="T:System.CodeDom.CodeTypeDeclaration" /> object from the collection.</summary>
      <param name="value">The <see cref="T:System.CodeDom.CodeTypeDeclaration" /> to remove from the collection.</param>
      <exception cref="T:System.ArgumentException">The specified object is not found in the collection.</exception>
    </member>
    <member name="P:System.CodeDom.CodeTypeDeclarationCollection.Item(System.Int32)">
      <summary>Gets or sets the <see cref="T:System.CodeDom.CodeTypeDeclaration" /> object at the specified index in the collection.</summary>
      <param name="index">The index of the collection to access.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="index" /> parameter is outside the valid range of indexes for the collection.</exception>
      <returns>A <see cref="T:System.CodeDom.CodeTypeDeclaration" /> at each valid index.</returns>
    </member>
    <member name="T:System.CodeDom.CodeTypeDelegate">
      <summary>Represents a delegate declaration.</summary>
    </member>
    <member name="M:System.CodeDom.CodeTypeDelegate.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.CodeDom.CodeTypeDelegate" /> class.</summary>
    </member>
    <member name="M:System.CodeDom.CodeTypeDelegate.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.CodeDom.CodeTypeDelegate" /> class.</summary>
      <param name="name">The name of the delegate.</param>
    </member>
    <member name="P:System.CodeDom.CodeTypeDelegate.Parameters">
      <summary>Gets the parameters of the delegate.</summary>
      <returns>A <see cref="T:System.CodeDom.CodeParameterDeclarationExpressionCollection" /> that indicates the parameters of the delegate.</returns>
    </member>
    <member name="P:System.CodeDom.CodeTypeDelegate.ReturnType">
      <summary>Gets or sets the return type of the delegate.</summary>
      <returns>A <see cref="T:System.CodeDom.CodeTypeReference" /> that indicates the return type of the delegate.</returns>
    </member>
    <member name="T:System.CodeDom.CodeTypeMember">
      <summary>Provides a base class for a member of a type. Type members include fields, methods, properties, constructors and nested types.</summary>
    </member>
    <member name="M:System.CodeDom.CodeTypeMember.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.CodeDom.CodeTypeMember" /> class.</summary>
    </member>
    <member name="P:System.CodeDom.CodeTypeMember.Attributes">
      <summary>Gets or sets the attributes of the member.</summary>
      <returns>A bitwise combination of the <see cref="T:System.CodeDom.MemberAttributes" /> values used to indicate the attributes of the member. The default value is <see cref="F:System.CodeDom.MemberAttributes.Private" /> | <see cref="F:System.CodeDom.MemberAttributes.Final" />.</returns>
    </member>
    <member name="P:System.CodeDom.CodeTypeMember.Comments">
      <summary>Gets the collection of comments for the type member.</summary>
      <returns>A <see cref="T:System.CodeDom.CodeCommentStatementCollection" /> that indicates the comments for the member.</returns>
    </member>
    <member name="P:System.CodeDom.CodeTypeMember.CustomAttributes">
      <summary>Gets or sets the custom attributes of the member.</summary>
      <returns>A <see cref="T:System.CodeDom.CodeAttributeDeclarationCollection" /> that indicates the custom attributes of the member.</returns>
    </member>
    <member name="P:System.CodeDom.CodeTypeMember.EndDirectives">
      <summary>Gets the end directives for the member.</summary>
      <returns>A <see cref="T:System.CodeDom.CodeDirectiveCollection" /> object containing end directives.</returns>
    </member>
    <member name="P:System.CodeDom.CodeTypeMember.LinePragma">
      <summary>Gets or sets the line on which the type member statement occurs.</summary>
      <returns>A <see cref="T:System.CodeDom.CodeLinePragma" /> object that indicates the location of the type member declaration.</returns>
    </member>
    <member name="P:System.CodeDom.CodeTypeMember.Name">
      <summary>Gets or sets the name of the member.</summary>
      <returns>The name of the member.</returns>
    </member>
    <member name="P:System.CodeDom.CodeTypeMember.StartDirectives">
      <summary>Gets the start directives for the member.</summary>
      <returns>A <see cref="T:System.CodeDom.CodeDirectiveCollection" /> object containing start directives.</returns>
    </member>
    <member name="T:System.CodeDom.CodeTypeMemberCollection">
      <summary>Represents a collection of <see cref="T:System.CodeDom.CodeTypeMember" /> objects.</summary>
    </member>
    <member name="M:System.CodeDom.CodeTypeMemberCollection.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.CodeDom.CodeTypeMemberCollection" /> class.</summary>
    </member>
    <member name="M:System.CodeDom.CodeTypeMemberCollection.#ctor(System.CodeDom.CodeTypeMember[])">
      <summary>Initializes a new instance of the <see cref="T:System.CodeDom.CodeTypeMemberCollection" /> class containing the specified array of <see cref="T:System.CodeDom.CodeTypeMember" /> objects.</summary>
      <param name="value">An array of <see cref="T:System.CodeDom.CodeTypeMember" /> objects with which to initialize the collection.</param>
    </member>
    <member name="M:System.CodeDom.CodeTypeMemberCollection.#ctor(System.CodeDom.CodeTypeMemberCollection)">
      <summary>Initializes a new instance of the <see cref="T:System.CodeDom.CodeTypeMemberCollection" /> class containing the elements of the specified source collection.</summary>
      <param name="value">A <see cref="T:System.CodeDom.CodeTypeMemberCollection" /> with which to initialize the collection.</param>
    </member>
    <member name="M:System.CodeDom.CodeTypeMemberCollection.Add(System.CodeDom.CodeTypeMember)">
      <summary>Adds a <see cref="T:System.CodeDom.CodeTypeMember" /> with the specified value to the collection.</summary>
      <param name="value">The <see cref="T:System.CodeDom.CodeTypeMember" /> to add.</param>
      <returns>The index at which the new element was inserted.</returns>
    </member>
    <member name="M:System.CodeDom.CodeTypeMemberCollection.AddRange(System.CodeDom.CodeTypeMember[])">
      <summary>Copies the elements of the specified <see cref="T:System.CodeDom.CodeTypeMember" /> array to the end of the collection.</summary>
      <param name="value">An array of type <see cref="T:System.CodeDom.CodeTypeMember" /> containing the objects to add to the collection.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.CodeDom.CodeTypeMemberCollection.AddRange(System.CodeDom.CodeTypeMemberCollection)">
      <summary>Adds the contents of another <see cref="T:System.CodeDom.CodeTypeMemberCollection" /> to the end of the collection.</summary>
      <param name="value">A <see cref="T:System.CodeDom.CodeTypeMemberCollection" /> containing the objects to add to the collection.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.CodeDom.CodeTypeMemberCollection.Contains(System.CodeDom.CodeTypeMember)">
      <summary>Gets a value indicating whether the collection contains the specified <see cref="T:System.CodeDom.CodeTypeMember" />.</summary>
      <param name="value">The <see cref="T:System.CodeDom.CodeTypeMember" /> to search for in the collection.</param>
      <returns>
        <see langword="true" /> if the collection contains the specified object; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.CodeDom.CodeTypeMemberCollection.CopyTo(System.CodeDom.CodeTypeMember[],System.Int32)">
      <summary>Copies the collection objects to a one-dimensional <see cref="T:System.Array" /> instance, beginning at the specified index.</summary>
      <param name="array">The one-dimensional <see cref="T:System.Array" /> that is the destination of the values copied from the collection.</param>
      <param name="index">The index of the array at which to begin inserting.</param>
      <exception cref="T:System.ArgumentException">The destination array is multidimensional.  
  
 -or-  
  
 The number of elements in the <see cref="T:System.CodeDom.CodeTypeMemberCollection" /> is greater than the available space between the index of the target array specified by the <paramref name="index" /> parameter and the end of the target array.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="array" /> parameter is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="index" /> parameter is less than the target array's minimum index.</exception>
    </member>
    <member name="M:System.CodeDom.CodeTypeMemberCollection.IndexOf(System.CodeDom.CodeTypeMember)">
      <summary>Gets the index in the collection of the specified <see cref="T:System.CodeDom.CodeTypeMember" />, if it exists in the collection.</summary>
      <param name="value">The <see cref="T:System.CodeDom.CodeTypeMember" /> to locate in the collection.</param>
      <returns>The index in the collection of the specified object, if found; otherwise, -1.</returns>
    </member>
    <member name="M:System.CodeDom.CodeTypeMemberCollection.Insert(System.Int32,System.CodeDom.CodeTypeMember)">
      <summary>Inserts the specified <see cref="T:System.CodeDom.CodeTypeMember" /> into the collection at the specified index.</summary>
      <param name="index">The zero-based index where the specified object should be inserted.</param>
      <param name="value">The <see cref="T:System.CodeDom.CodeTypeMember" /> to insert.</param>
    </member>
    <member name="M:System.CodeDom.CodeTypeMemberCollection.Remove(System.CodeDom.CodeTypeMember)">
      <summary>Removes a specific <see cref="T:System.CodeDom.CodeTypeMember" /> from the collection.</summary>
      <param name="value">The <see cref="T:System.CodeDom.CodeTypeMember" /> to remove from the collection.</param>
      <exception cref="T:System.ArgumentException">The specified object is not found in the collection.</exception>
    </member>
    <member name="P:System.CodeDom.CodeTypeMemberCollection.Item(System.Int32)">
      <summary>Gets or sets the <see cref="T:System.CodeDom.CodeTypeMember" /> at the specified index in the collection.</summary>
      <param name="index">The index of the collection to access.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="index" /> parameter is outside the valid range of indexes for the collection.</exception>
      <returns>A <see cref="T:System.CodeDom.CodeTypeMember" /> at each valid index.</returns>
    </member>
    <member name="T:System.CodeDom.CodeTypeOfExpression">
      <summary>Represents a <see langword="typeof" /> expression, an expression that returns a <see cref="T:System.Type" /> for a specified type name.</summary>
    </member>
    <member name="M:System.CodeDom.CodeTypeOfExpression.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.CodeDom.CodeTypeOfExpression" /> class.</summary>
    </member>
    <member name="M:System.CodeDom.CodeTypeOfExpression.#ctor(System.CodeDom.CodeTypeReference)">
      <summary>Initializes a new instance of the <see cref="T:System.CodeDom.CodeTypeOfExpression" /> class.</summary>
      <param name="type">A <see cref="T:System.CodeDom.CodeTypeReference" /> that indicates the data type for the <see langword="typeof" /> expression.</param>
    </member>
    <member name="M:System.CodeDom.CodeTypeOfExpression.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.CodeDom.CodeTypeOfExpression" /> class using the specified type.</summary>
      <param name="type">The name of the data type for the <see langword="typeof" /> expression.</param>
    </member>
    <member name="M:System.CodeDom.CodeTypeOfExpression.#ctor(System.Type)">
      <summary>Initializes a new instance of the <see cref="T:System.CodeDom.CodeTypeOfExpression" /> class using the specified type.</summary>
      <param name="type">The data type of the data type of the <see langword="typeof" /> expression.</param>
    </member>
    <member name="P:System.CodeDom.CodeTypeOfExpression.Type">
      <summary>Gets or sets the data type referenced by the <see langword="typeof" /> expression.</summary>
      <returns>A <see cref="T:System.CodeDom.CodeTypeReference" /> that indicates the data type referenced by the <see langword="typeof" /> expression. This property will never return <see langword="null" />, and defaults to the <see cref="T:System.Void" /> type.</returns>
    </member>
    <member name="T:System.CodeDom.CodeTypeParameter">
      <summary>Represents a type parameter of a generic type or method.</summary>
    </member>
    <member name="M:System.CodeDom.CodeTypeParameter.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.CodeDom.CodeTypeParameter" /> class.</summary>
    </member>
    <member name="M:System.CodeDom.CodeTypeParameter.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.CodeDom.CodeTypeParameter" /> class with the specified type parameter name.</summary>
      <param name="name">The name of the type parameter.</param>
    </member>
    <member name="P:System.CodeDom.CodeTypeParameter.Constraints">
      <summary>Gets the constraints for the type parameter.</summary>
      <returns>A <see cref="T:System.CodeDom.CodeTypeReferenceCollection" /> object that contains the constraints for the type parameter.</returns>
    </member>
    <member name="P:System.CodeDom.CodeTypeParameter.CustomAttributes">
      <summary>Gets the custom attributes of the type parameter.</summary>
      <returns>A <see cref="T:System.CodeDom.CodeAttributeDeclarationCollection" /> that indicates the custom attributes of the type parameter. The default is <see langword="null" />.</returns>
    </member>
    <member name="P:System.CodeDom.CodeTypeParameter.HasConstructorConstraint">
      <summary>Gets or sets a value indicating whether the type parameter has a constructor constraint.</summary>
      <returns>
        <see langword="true" /> if the type parameter has a constructor constraint; otherwise, <see langword="false" />. The default is <see langword="false" />.</returns>
    </member>
    <member name="P:System.CodeDom.CodeTypeParameter.Name">
      <summary>Gets or sets the name of the type parameter.</summary>
      <returns>The name of the type parameter. The default is an empty string ("").</returns>
    </member>
    <member name="T:System.CodeDom.CodeTypeParameterCollection">
      <summary>Represents a collection of <see cref="T:System.CodeDom.CodeTypeParameter" /> objects.</summary>
    </member>
    <member name="M:System.CodeDom.CodeTypeParameterCollection.#ctor">
      <summary>Initializes a new, empty instance of the <see cref="T:System.CodeDom.CodeTypeParameterCollection" /> class.</summary>
    </member>
    <member name="M:System.CodeDom.CodeTypeParameterCollection.#ctor(System.CodeDom.CodeTypeParameter[])">
      <summary>Initializes a new instance of the <see cref="T:System.CodeDom.CodeTypeParameterCollection" /> class containing the specified array of <see cref="T:System.CodeDom.CodeTypeParameter" /> objects.</summary>
      <param name="value">An array of <see cref="T:System.CodeDom.CodeTypeParameter" /> objects with which to initialize the collection.</param>
    </member>
    <member name="M:System.CodeDom.CodeTypeParameterCollection.#ctor(System.CodeDom.CodeTypeParameterCollection)">
      <summary>Initializes a new instance of the <see cref="T:System.CodeDom.CodeTypeParameterCollection" /> class containing the elements of the specified source collection.</summary>
      <param name="value">A <see cref="T:System.CodeDom.CodeTypeParameterCollection" /> with which to initialize the collection.</param>
    </member>
    <member name="M:System.CodeDom.CodeTypeParameterCollection.Add(System.CodeDom.CodeTypeParameter)">
      <summary>Adds the specified <see cref="T:System.CodeDom.CodeTypeParameter" /> object to the collection.</summary>
      <param name="value">The <see cref="T:System.CodeDom.CodeTypeParameter" /> to add.</param>
      <returns>The zero-based index at which the new element was inserted.</returns>
    </member>
    <member name="M:System.CodeDom.CodeTypeParameterCollection.Add(System.String)">
      <summary>Adds the specified <see cref="T:System.CodeDom.CodeTypeParameter" /> object to the collection using the specified data type name.</summary>
      <param name="value">The name of a data type for which to add the <see cref="T:System.CodeDom.CodeTypeParameter" /> object to the collection.</param>
    </member>
    <member name="M:System.CodeDom.CodeTypeParameterCollection.AddRange(System.CodeDom.CodeTypeParameter[])">
      <summary>Copies the elements of the specified <see cref="T:System.CodeDom.CodeTypeParameter" /> array to the end of the collection.</summary>
      <param name="value">An array of type <see cref="T:System.CodeDom.CodeTypeParameter" /> containing the objects to add to the collection.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.CodeDom.CodeTypeParameterCollection.AddRange(System.CodeDom.CodeTypeParameterCollection)">
      <summary>Copies the elements of the specified <see cref="T:System.CodeDom.CodeTypeParameterCollection" /> to the end of the collection.</summary>
      <param name="value">A <see cref="T:System.CodeDom.CodeTypeParameterCollection" /> containing the <see cref="T:System.CodeDom.CodeTypeParameter" /> objects to add to the collection.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.CodeDom.CodeTypeParameterCollection.Contains(System.CodeDom.CodeTypeParameter)">
      <summary>Determines whether the collection contains the specified <see cref="T:System.CodeDom.CodeTypeParameter" /> object.</summary>
      <param name="value">The <see cref="T:System.CodeDom.CodeTypeParameter" /> object to search for in the collection.</param>
      <returns>
        <see langword="true" /> if the <see cref="T:System.CodeDom.CodeTypeParameter" /> object is contained in the collection; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.CodeDom.CodeTypeParameterCollection.CopyTo(System.CodeDom.CodeTypeParameter[],System.Int32)">
      <summary>Copies the items in the collection to the specified one-dimensional <see cref="T:System.Array" /> at the specified index.</summary>
      <param name="array">The one-dimensional <see cref="T:System.Array" /> that is the destination of the values copied from the collection.</param>
      <param name="index">The index of the array at which to begin inserting.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="array" /> is multidimensional.  
  
 -or-  
  
 The number of elements in the <see cref="T:System.CodeDom.CodeTypeParameterCollection" /> is greater than the available space between the index of the target array specified by <paramref name="index" /> and the end of the target array.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> is less than the target array's lowest index.</exception>
    </member>
    <member name="M:System.CodeDom.CodeTypeParameterCollection.IndexOf(System.CodeDom.CodeTypeParameter)">
      <summary>Gets the index in the collection of the specified <see cref="T:System.CodeDom.CodeTypeParameter" /> object, if it exists in the collection.</summary>
      <param name="value">The <see cref="T:System.CodeDom.CodeTypeParameter" /> object to locate in the collection.</param>
      <returns>The zero-based index of the specified <see cref="T:System.CodeDom.CodeTypeParameter" /> object in the collection if found; otherwise, -1.</returns>
    </member>
    <member name="M:System.CodeDom.CodeTypeParameterCollection.Insert(System.Int32,System.CodeDom.CodeTypeParameter)">
      <summary>Inserts the specified <see cref="T:System.CodeDom.CodeTypeParameter" /> object into the collection at the specified index.</summary>
      <param name="index">The zero-based index at which to insert the item.</param>
      <param name="value">The <see cref="T:System.CodeDom.CodeTypeParameter" /> object to insert.</param>
    </member>
    <member name="M:System.CodeDom.CodeTypeParameterCollection.Remove(System.CodeDom.CodeTypeParameter)">
      <summary>Removes the specified <see cref="T:System.CodeDom.CodeTypeParameter" /> object from the collection.</summary>
      <param name="value">The <see cref="T:System.CodeDom.CodeTypeParameter" /> object to remove from the collection.</param>
      <exception cref="T:System.ArgumentException">The specified object is not found in the collection.</exception>
    </member>
    <member name="P:System.CodeDom.CodeTypeParameterCollection.Item(System.Int32)">
      <summary>Gets or sets the <see cref="T:System.CodeDom.CodeTypeParameter" /> object at the specified index in the collection.</summary>
      <param name="index">The zero-based index of the collection object to access.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> is outside the valid range of indexes for the collection.</exception>
      <returns>The <see cref="T:System.CodeDom.CodeTypeParameter" /> object at the specified index.</returns>
    </member>
    <member name="T:System.CodeDom.CodeTypeReference">
      <summary>Represents a reference to a type.</summary>
    </member>
    <member name="M:System.CodeDom.CodeTypeReference.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.CodeDom.CodeTypeReference" /> class.</summary>
    </member>
    <member name="M:System.CodeDom.CodeTypeReference.#ctor(System.CodeDom.CodeTypeParameter)">
      <summary>Initializes a new instance of the <see cref="T:System.CodeDom.CodeTypeReference" /> class using the specified code type parameter.</summary>
      <param name="typeParameter">A <see cref="T:System.CodeDom.CodeTypeParameter" /> that represents the type of the type parameter.</param>
    </member>
    <member name="M:System.CodeDom.CodeTypeReference.#ctor(System.CodeDom.CodeTypeReference,System.Int32)">
      <summary>Initializes a new instance of the <see cref="T:System.CodeDom.CodeTypeReference" /> class using the specified array type and rank.</summary>
      <param name="arrayType">A <see cref="T:System.CodeDom.CodeTypeReference" /> that indicates the type of the array.</param>
      <param name="rank">The number of dimensions in the array.</param>
    </member>
    <member name="M:System.CodeDom.CodeTypeReference.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.CodeDom.CodeTypeReference" /> class using the specified type name.</summary>
      <param name="typeName">The name of the type to reference.</param>
    </member>
    <member name="M:System.CodeDom.CodeTypeReference.#ctor(System.String,System.CodeDom.CodeTypeReference[])">
      <summary>Initializes a new instance of the <see cref="T:System.CodeDom.CodeTypeReference" /> class using the specified type name and type arguments.</summary>
      <param name="typeName">The name of the type to reference.</param>
      <param name="typeArguments">An array of <see cref="T:System.CodeDom.CodeTypeReference" /> values.</param>
    </member>
    <member name="M:System.CodeDom.CodeTypeReference.#ctor(System.String,System.CodeDom.CodeTypeReferenceOptions)">
      <summary>Initializes a new instance of the <see cref="T:System.CodeDom.CodeTypeReference" /> class using the specified type name and code type reference option.</summary>
      <param name="typeName">The name of the type to reference.</param>
      <param name="codeTypeReferenceOption">The code type reference option, one of the <see cref="T:System.CodeDom.CodeTypeReferenceOptions" /> values.</param>
    </member>
    <member name="M:System.CodeDom.CodeTypeReference.#ctor(System.String,System.Int32)">
      <summary>Initializes a new instance of the <see cref="T:System.CodeDom.CodeTypeReference" /> class using the specified array type name and rank.</summary>
      <param name="baseType">The name of the type of the elements of the array.</param>
      <param name="rank">The number of dimensions of the array.</param>
    </member>
    <member name="M:System.CodeDom.CodeTypeReference.#ctor(System.Type)">
      <summary>Initializes a new instance of the <see cref="T:System.CodeDom.CodeTypeReference" /> class using the specified type.</summary>
      <param name="type">The <see cref="T:System.Type" /> to reference.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="type" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.CodeDom.CodeTypeReference.#ctor(System.Type,System.CodeDom.CodeTypeReferenceOptions)">
      <summary>Initializes a new instance of the <see cref="T:System.CodeDom.CodeTypeReference" /> class using the specified type and code type reference.</summary>
      <param name="type">The <see cref="T:System.Type" /> to reference.</param>
      <param name="codeTypeReferenceOption">The code type reference option, one of the <see cref="T:System.CodeDom.CodeTypeReferenceOptions" /> values.</param>
    </member>
    <member name="P:System.CodeDom.CodeTypeReference.ArrayElementType">
      <summary>Gets or sets the type of the elements in the array.</summary>
      <returns>A <see cref="T:System.CodeDom.CodeTypeReference" /> that indicates the type of the array elements.</returns>
    </member>
    <member name="P:System.CodeDom.CodeTypeReference.ArrayRank">
      <summary>Gets or sets the array rank of the array.</summary>
      <returns>The number of dimensions of the array.</returns>
    </member>
    <member name="P:System.CodeDom.CodeTypeReference.BaseType">
      <summary>Gets or sets the name of the type being referenced.</summary>
      <returns>The name of the type being referenced.</returns>
    </member>
    <member name="P:System.CodeDom.CodeTypeReference.Options">
      <summary>Gets or sets the code type reference option.</summary>
      <returns>A bitwise combination of the <see cref="T:System.CodeDom.CodeTypeReferenceOptions" /> values.</returns>
    </member>
    <member name="P:System.CodeDom.CodeTypeReference.TypeArguments">
      <summary>Gets the type arguments for the current generic type reference.</summary>
      <returns>A <see cref="T:System.CodeDom.CodeTypeReferenceCollection" /> containing the type arguments for the current <see cref="T:System.CodeDom.CodeTypeReference" /> object.</returns>
    </member>
    <member name="T:System.CodeDom.CodeTypeReferenceCollection">
      <summary>Represents a collection of <see cref="T:System.CodeDom.CodeTypeReference" /> objects.</summary>
    </member>
    <member name="M:System.CodeDom.CodeTypeReferenceCollection.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.CodeDom.CodeTypeReferenceCollection" /> class.</summary>
    </member>
    <member name="M:System.CodeDom.CodeTypeReferenceCollection.#ctor(System.CodeDom.CodeTypeReference[])">
      <summary>Initializes a new instance of the <see cref="T:System.CodeDom.CodeTypeReferenceCollection" /> class containing the specified array of <see cref="T:System.CodeDom.CodeTypeReference" /> objects.</summary>
      <param name="value">An array of <see cref="T:System.CodeDom.CodeTypeReference" /> objects with which to initialize the collection.</param>
    </member>
    <member name="M:System.CodeDom.CodeTypeReferenceCollection.#ctor(System.CodeDom.CodeTypeReferenceCollection)">
      <summary>Initializes a new instance of the <see cref="T:System.CodeDom.CodeTypeReferenceCollection" /> class containing the elements of the specified source collection.</summary>
      <param name="value">A <see cref="T:System.CodeDom.CodeTypeReferenceCollection" /> with which to initialize the collection.</param>
    </member>
    <member name="M:System.CodeDom.CodeTypeReferenceCollection.Add(System.CodeDom.CodeTypeReference)">
      <summary>Adds the specified <see cref="T:System.CodeDom.CodeTypeReference" /> to the collection.</summary>
      <param name="value">The <see cref="T:System.CodeDom.CodeTypeReference" /> to add.</param>
      <returns>The index at which the new element was inserted.</returns>
    </member>
    <member name="M:System.CodeDom.CodeTypeReferenceCollection.Add(System.String)">
      <summary>Adds a <see cref="T:System.CodeDom.CodeTypeReference" /> to the collection using the specified data type name.</summary>
      <param name="value">The name of a data type for which to add a <see cref="T:System.CodeDom.CodeTypeReference" /> to the collection.</param>
    </member>
    <member name="M:System.CodeDom.CodeTypeReferenceCollection.Add(System.Type)">
      <summary>Adds a <see cref="T:System.CodeDom.CodeTypeReference" /> to the collection using the specified data type.</summary>
      <param name="value">The data type for which to add a <see cref="T:System.CodeDom.CodeTypeReference" /> to the collection.</param>
    </member>
    <member name="M:System.CodeDom.CodeTypeReferenceCollection.AddRange(System.CodeDom.CodeTypeReference[])">
      <summary>Copies the elements of the specified <see cref="T:System.CodeDom.CodeTypeReference" /> array to the end of the collection.</summary>
      <param name="value">An array of type <see cref="T:System.CodeDom.CodeTypeReference" /> containing the objects to add to the collection.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.CodeDom.CodeTypeReferenceCollection.AddRange(System.CodeDom.CodeTypeReferenceCollection)">
      <summary>Adds the contents of the specified <see cref="T:System.CodeDom.CodeTypeReferenceCollection" /> to the end of the collection.</summary>
      <param name="value">A <see cref="T:System.CodeDom.CodeTypeReferenceCollection" /> containing the objects to add to the collection.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.CodeDom.CodeTypeReferenceCollection.Contains(System.CodeDom.CodeTypeReference)">
      <summary>Gets a value indicating whether the collection contains the specified <see cref="T:System.CodeDom.CodeTypeReference" />.</summary>
      <param name="value">The <see cref="T:System.CodeDom.CodeTypeReference" /> to search for in the collection.</param>
      <returns>
        <see langword="true" /> if the <see cref="T:System.CodeDom.CodeTypeReference" /> is contained in the collection; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.CodeDom.CodeTypeReferenceCollection.CopyTo(System.CodeDom.CodeTypeReference[],System.Int32)">
      <summary>Copies the items in the collection to the specified one-dimensional <see cref="T:System.Array" /> at the specified index.</summary>
      <param name="array">The one-dimensional <see cref="T:System.Array" /> that is the destination of the values copied from the collection.</param>
      <param name="index">The index of the array at which to begin inserting.</param>
      <exception cref="T:System.ArgumentException">The <paramref name="array" /> parameter is multidimensional.  
  
 -or-  
  
 The number of elements in the <see cref="T:System.CodeDom.CodeTypeReferenceCollection" /> is greater than the available space between the index of the target array specified by the <paramref name="index" /> parameter and the end of the target array.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="array" /> parameter is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="index" /> parameter is less than the target array's minimum index.</exception>
    </member>
    <member name="M:System.CodeDom.CodeTypeReferenceCollection.IndexOf(System.CodeDom.CodeTypeReference)">
      <summary>Gets the index in the collection of the specified <see cref="T:System.CodeDom.CodeTypeReference" />, if it exists in the collection.</summary>
      <param name="value">The <see cref="T:System.CodeDom.CodeTypeReference" /> to locate in the collection.</param>
      <returns>The index of the specified <see cref="T:System.CodeDom.CodeTypeReference" /> in the collection if found; otherwise, -1.</returns>
    </member>
    <member name="M:System.CodeDom.CodeTypeReferenceCollection.Insert(System.Int32,System.CodeDom.CodeTypeReference)">
      <summary>Inserts a <see cref="T:System.CodeDom.CodeTypeReference" /> into the collection at the specified index.</summary>
      <param name="index">The zero-based index where the item should be inserted.</param>
      <param name="value">The <see cref="T:System.CodeDom.CodeTypeReference" /> to insert.</param>
    </member>
    <member name="M:System.CodeDom.CodeTypeReferenceCollection.Remove(System.CodeDom.CodeTypeReference)">
      <summary>Removes the specified <see cref="T:System.CodeDom.CodeTypeReference" /> from the collection.</summary>
      <param name="value">The <see cref="T:System.CodeDom.CodeTypeReference" /> to remove from the collection.</param>
      <exception cref="T:System.ArgumentException">The specified object is not found in the collection.</exception>
    </member>
    <member name="P:System.CodeDom.CodeTypeReferenceCollection.Item(System.Int32)">
      <summary>Gets or sets the <see cref="T:System.CodeDom.CodeTypeReference" /> at the specified index in the collection.</summary>
      <param name="index">The index of the collection to access.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="index" /> parameter is outside the valid range of indexes for the collection.</exception>
      <returns>A <see cref="T:System.CodeDom.CodeTypeReference" /> at each valid index.</returns>
    </member>
    <member name="T:System.CodeDom.CodeTypeReferenceExpression">
      <summary>Represents a reference to a data type.</summary>
    </member>
    <member name="M:System.CodeDom.CodeTypeReferenceExpression.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.CodeDom.CodeTypeReferenceExpression" /> class.</summary>
    </member>
    <member name="M:System.CodeDom.CodeTypeReferenceExpression.#ctor(System.CodeDom.CodeTypeReference)">
      <summary>Initializes a new instance of the <see cref="T:System.CodeDom.CodeTypeReferenceExpression" /> class using the specified type.</summary>
      <param name="type">A <see cref="T:System.CodeDom.CodeTypeReference" /> that indicates the data type to reference.</param>
    </member>
    <member name="M:System.CodeDom.CodeTypeReferenceExpression.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.CodeDom.CodeTypeReferenceExpression" /> class using the specified data type name.</summary>
      <param name="type">The name of the data type to reference.</param>
    </member>
    <member name="M:System.CodeDom.CodeTypeReferenceExpression.#ctor(System.Type)">
      <summary>Initializes a new instance of the <see cref="T:System.CodeDom.CodeTypeReferenceExpression" /> class using the specified data type.</summary>
      <param name="type">An instance of the data type to reference.</param>
    </member>
    <member name="P:System.CodeDom.CodeTypeReferenceExpression.Type">
      <summary>Gets or sets the data type to reference.</summary>
      <returns>A <see cref="T:System.CodeDom.CodeTypeReference" /> that indicates the data type to reference.</returns>
    </member>
    <member name="T:System.CodeDom.CodeTypeReferenceOptions">
      <summary>Specifies how the code type reference is to be resolved.</summary>
    </member>
    <member name="F:System.CodeDom.CodeTypeReferenceOptions.GenericTypeParameter">
      <summary>Resolve the type from the type parameter.</summary>
    </member>
    <member name="F:System.CodeDom.CodeTypeReferenceOptions.GlobalReference">
      <summary>Resolve the type from the root namespace.</summary>
    </member>
    <member name="T:System.CodeDom.CodeVariableDeclarationStatement">
      <summary>Represents a variable declaration.</summary>
    </member>
    <member name="M:System.CodeDom.CodeVariableDeclarationStatement.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.CodeDom.CodeVariableDeclarationStatement" /> class.</summary>
    </member>
    <member name="M:System.CodeDom.CodeVariableDeclarationStatement.#ctor(System.CodeDom.CodeTypeReference,System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.CodeDom.CodeVariableDeclarationStatement" /> class using the specified type and name.</summary>
      <param name="type">A <see cref="T:System.CodeDom.CodeTypeReference" /> that indicates the data type of the variable.</param>
      <param name="name">The name of the variable.</param>
    </member>
    <member name="M:System.CodeDom.CodeVariableDeclarationStatement.#ctor(System.CodeDom.CodeTypeReference,System.String,System.CodeDom.CodeExpression)">
      <summary>Initializes a new instance of the <see cref="T:System.CodeDom.CodeVariableDeclarationStatement" /> class using the specified data type, variable name, and initialization expression.</summary>
      <param name="type">A <see cref="T:System.CodeDom.CodeTypeReference" /> that indicates the type of the variable.</param>
      <param name="name">The name of the variable.</param>
      <param name="initExpression">A <see cref="T:System.CodeDom.CodeExpression" /> that indicates the initialization expression for the variable.</param>
    </member>
    <member name="M:System.CodeDom.CodeVariableDeclarationStatement.#ctor(System.String,System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.CodeDom.CodeVariableDeclarationStatement" /> class using the specified data type name and variable name.</summary>
      <param name="type">The name of the data type of the variable.</param>
      <param name="name">The name of the variable.</param>
    </member>
    <member name="M:System.CodeDom.CodeVariableDeclarationStatement.#ctor(System.String,System.String,System.CodeDom.CodeExpression)">
      <summary>Initializes a new instance of the <see cref="T:System.CodeDom.CodeVariableDeclarationStatement" /> class using the specified data type, variable name, and initialization expression.</summary>
      <param name="type">The name of the data type of the variable.</param>
      <param name="name">The name of the variable.</param>
      <param name="initExpression">A <see cref="T:System.CodeDom.CodeExpression" /> that indicates the initialization expression for the variable.</param>
    </member>
    <member name="M:System.CodeDom.CodeVariableDeclarationStatement.#ctor(System.Type,System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.CodeDom.CodeVariableDeclarationStatement" /> class using the specified data type and variable name.</summary>
      <param name="type">The data type for the variable.</param>
      <param name="name">The name of the variable.</param>
    </member>
    <member name="M:System.CodeDom.CodeVariableDeclarationStatement.#ctor(System.Type,System.String,System.CodeDom.CodeExpression)">
      <summary>Initializes a new instance of the <see cref="T:System.CodeDom.CodeVariableDeclarationStatement" /> class using the specified data type, variable name, and initialization expression.</summary>
      <param name="type">The data type of the variable.</param>
      <param name="name">The name of the variable.</param>
      <param name="initExpression">A <see cref="T:System.CodeDom.CodeExpression" /> that indicates the initialization expression for the variable.</param>
    </member>
    <member name="P:System.CodeDom.CodeVariableDeclarationStatement.InitExpression">
      <summary>Gets or sets the initialization expression for the variable.</summary>
      <returns>A <see cref="T:System.CodeDom.CodeExpression" /> that indicates the initialization expression for the variable.</returns>
    </member>
    <member name="P:System.CodeDom.CodeVariableDeclarationStatement.Name">
      <summary>Gets or sets the name of the variable.</summary>
      <returns>The name of the variable.</returns>
    </member>
    <member name="P:System.CodeDom.CodeVariableDeclarationStatement.Type">
      <summary>Gets or sets the data type of the variable.</summary>
      <returns>A <see cref="T:System.CodeDom.CodeTypeReference" /> that indicates the data type of the variable.</returns>
    </member>
    <member name="T:System.CodeDom.CodeVariableReferenceExpression">
      <summary>Represents a reference to a local variable.</summary>
    </member>
    <member name="M:System.CodeDom.CodeVariableReferenceExpression.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.CodeDom.CodeVariableReferenceExpression" /> class.</summary>
    </member>
    <member name="M:System.CodeDom.CodeVariableReferenceExpression.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.CodeDom.CodeVariableReferenceExpression" /> class using the specified local variable name.</summary>
      <param name="variableName">The name of the local variable to reference.</param>
    </member>
    <member name="P:System.CodeDom.CodeVariableReferenceExpression.VariableName">
      <summary>Gets or sets the name of the local variable to reference.</summary>
      <returns>The name of the local variable to reference.</returns>
    </member>
    <member name="T:System.CodeDom.Compiler.CodeCompiler">
      <summary>Provides an example implementation of the <see cref="T:System.CodeDom.Compiler.ICodeCompiler" /> interface.</summary>
    </member>
    <member name="M:System.CodeDom.Compiler.CodeCompiler.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.CodeDom.Compiler.CodeCompiler" /> class.</summary>
    </member>
    <member name="M:System.CodeDom.Compiler.CodeCompiler.CmdArgsFromParameters(System.CodeDom.Compiler.CompilerParameters)">
      <summary>Gets the command arguments to be passed to the compiler from the specified <see cref="T:System.CodeDom.Compiler.CompilerParameters" />.</summary>
      <param name="options">A <see cref="T:System.CodeDom.Compiler.CompilerParameters" /> that indicates the compiler options.</param>
      <returns>The command arguments.</returns>
    </member>
    <member name="M:System.CodeDom.Compiler.CodeCompiler.FromDom(System.CodeDom.Compiler.CompilerParameters,System.CodeDom.CodeCompileUnit)">
      <summary>Compiles the specified compile unit using the specified options, and returns the results from the compilation.</summary>
      <param name="options">A <see cref="T:System.CodeDom.Compiler.CompilerParameters" /> object that indicates the compiler options.</param>
      <param name="e">A <see cref="T:System.CodeDom.CodeCompileUnit" /> object that indicates the source to compile.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="options" /> is <see langword="null" />.</exception>
      <returns>The results of compilation.</returns>
    </member>
    <member name="M:System.CodeDom.Compiler.CodeCompiler.FromDomBatch(System.CodeDom.Compiler.CompilerParameters,System.CodeDom.CodeCompileUnit[])">
      <summary>Compiles the specified compile units using the specified options, and returns the results from the compilation.</summary>
      <param name="options">A <see cref="T:System.CodeDom.Compiler.CompilerParameters" /> object that indicates the compiler options.</param>
      <param name="ea">An array of <see cref="T:System.CodeDom.CodeCompileUnit" /> objects that indicates the source to compile.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="options" /> is <see langword="null" />.  
  
 -or-  
  
 <paramref name="ea" /> is <see langword="null" />.</exception>
      <returns>The results of compilation.</returns>
    </member>
    <member name="M:System.CodeDom.Compiler.CodeCompiler.FromFile(System.CodeDom.Compiler.CompilerParameters,System.String)">
      <summary>Compiles the specified file using the specified options, and returns the results from the compilation.</summary>
      <param name="options">A <see cref="T:System.CodeDom.Compiler.CompilerParameters" /> object that indicates the compiler options.</param>
      <param name="fileName">The file name to compile.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="options" /> is <see langword="null" />.  
  
 -or-  
  
 <paramref name="fileName" /> is <see langword="null" />.</exception>
      <returns>The results of compilation.</returns>
    </member>
    <member name="M:System.CodeDom.Compiler.CodeCompiler.FromFileBatch(System.CodeDom.Compiler.CompilerParameters,System.String[])">
      <summary>Compiles the specified files using the specified options, and returns the results from the compilation.</summary>
      <param name="options">A <see cref="T:System.CodeDom.Compiler.CompilerParameters" /> object that indicates the compiler options.</param>
      <param name="fileNames">An array of strings that indicates the file names of the files to compile.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="options" /> is <see langword="null" />.  
  
 -or-  
  
 <paramref name="fileNames" /> is <see langword="null" />.</exception>
      <returns>The results of compilation.</returns>
    </member>
    <member name="M:System.CodeDom.Compiler.CodeCompiler.FromSource(System.CodeDom.Compiler.CompilerParameters,System.String)">
      <summary>Compiles the specified source code string using the specified options, and returns the results from the compilation.</summary>
      <param name="options">A <see cref="T:System.CodeDom.Compiler.CompilerParameters" /> object that indicates the compiler options.</param>
      <param name="source">The source code string to compile.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="options" /> is <see langword="null" />.</exception>
      <returns>The results of compilation.</returns>
    </member>
    <member name="M:System.CodeDom.Compiler.CodeCompiler.FromSourceBatch(System.CodeDom.Compiler.CompilerParameters,System.String[])">
      <summary>Compiles the specified source code strings using the specified options, and returns the results from the compilation.</summary>
      <param name="options">A <see cref="T:System.CodeDom.Compiler.CompilerParameters" /> object that indicates the compiler options.</param>
      <param name="sources">An array of strings containing the source code to compile.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="options" /> is <see langword="null" />.  
  
 -or-  
  
 <paramref name="sources" /> is <see langword="null" />.</exception>
      <returns>The results of compilation.</returns>
    </member>
    <member name="M:System.CodeDom.Compiler.CodeCompiler.GetResponseFileCmdArgs(System.CodeDom.Compiler.CompilerParameters,System.String)">
      <summary>Gets the command arguments to use when invoking the compiler to generate a response file.</summary>
      <param name="options">A <see cref="T:System.CodeDom.Compiler.CompilerParameters" /> object that indicates the compiler options.</param>
      <param name="cmdArgs">A command arguments string.</param>
      <returns>The command arguments to use to generate a response file, or <see langword="null" /> if there are no response file arguments.</returns>
    </member>
    <member name="M:System.CodeDom.Compiler.CodeCompiler.JoinStringArray(System.String[],System.String)">
      <summary>Joins the specified string arrays.</summary>
      <param name="sa">The array of strings to join.</param>
      <param name="separator">The separator to use.</param>
      <returns>The concatenated string.</returns>
    </member>
    <member name="M:System.CodeDom.Compiler.CodeCompiler.ProcessCompilerOutputLine(System.CodeDom.Compiler.CompilerResults,System.String)">
      <summary>Processes the specified line from the specified <see cref="T:System.CodeDom.Compiler.CompilerResults" />.</summary>
      <param name="results">A <see cref="T:System.CodeDom.Compiler.CompilerResults" /> that indicates the results of compilation.</param>
      <param name="line">The line to process.</param>
    </member>
    <member name="M:System.CodeDom.Compiler.CodeCompiler.System#CodeDom#Compiler#ICodeCompiler#CompileAssemblyFromDom(System.CodeDom.Compiler.CompilerParameters,System.CodeDom.CodeCompileUnit)">
      <summary>For a description of this member, see <see cref="M:System.CodeDom.Compiler.ICodeCompiler.CompileAssemblyFromDom(System.CodeDom.Compiler.CompilerParameters,System.CodeDom.CodeCompileUnit)" />.</summary>
      <param name="options">A <see cref="T:System.CodeDom.Compiler.CompilerParameters" /> object that indicates the compiler options.</param>
      <param name="e">A <see cref="T:System.CodeDom.CodeCompileUnit" /> that indicates the source to compile.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="options" /> is <see langword="null" />.</exception>
      <returns>The results of compilation.</returns>
    </member>
    <member name="M:System.CodeDom.Compiler.CodeCompiler.System#CodeDom#Compiler#ICodeCompiler#CompileAssemblyFromDomBatch(System.CodeDom.Compiler.CompilerParameters,System.CodeDom.CodeCompileUnit[])">
      <summary>For a description of this member, see <see cref="M:System.CodeDom.Compiler.ICodeCompiler.CompileAssemblyFromDomBatch(System.CodeDom.Compiler.CompilerParameters,System.CodeDom.CodeCompileUnit[])" />.</summary>
      <param name="options">A <see cref="T:System.CodeDom.Compiler.CompilerParameters" /> object that indicates the compiler options.</param>
      <param name="ea">An array of <see cref="T:System.CodeDom.CodeCompileUnit" /> objects that indicates the source to compile.</param>
      <returns>The results of compilation.</returns>
    </member>
    <member name="M:System.CodeDom.Compiler.CodeCompiler.System#CodeDom#Compiler#ICodeCompiler#CompileAssemblyFromFile(System.CodeDom.Compiler.CompilerParameters,System.String)">
      <summary>For a description of this member, see <see cref="M:System.CodeDom.Compiler.ICodeCompiler.CompileAssemblyFromFile(System.CodeDom.Compiler.CompilerParameters,System.String)" />.</summary>
      <param name="options">A <see cref="T:System.CodeDom.Compiler.CompilerParameters" /> object that indicates the compiler options.</param>
      <param name="fileName">The file name to compile.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="options" /> is <see langword="null" />.</exception>
      <returns>The results of compilation.</returns>
    </member>
    <member name="M:System.CodeDom.Compiler.CodeCompiler.System#CodeDom#Compiler#ICodeCompiler#CompileAssemblyFromFileBatch(System.CodeDom.Compiler.CompilerParameters,System.String[])">
      <summary>For a description of this member, see <see cref="M:System.CodeDom.Compiler.ICodeCompiler.CompileAssemblyFromFileBatch(System.CodeDom.Compiler.CompilerParameters,System.String[])" />.</summary>
      <param name="options">A <see cref="T:System.CodeDom.Compiler.CompilerParameters" /> object that indicates the compiler options.</param>
      <param name="fileNames">An array of strings that indicates the file names to compile.</param>
      <returns>The results of compilation.</returns>
    </member>
    <member name="M:System.CodeDom.Compiler.CodeCompiler.System#CodeDom#Compiler#ICodeCompiler#CompileAssemblyFromSource(System.CodeDom.Compiler.CompilerParameters,System.String)">
      <summary>For a description of this member, see <see cref="M:System.CodeDom.Compiler.ICodeCompiler.CompileAssemblyFromSource(System.CodeDom.Compiler.CompilerParameters,System.String)" />.</summary>
      <param name="options">A <see cref="T:System.CodeDom.Compiler.CompilerParameters" /> object that indicates the compiler options.</param>
      <param name="source">A string that indicates the source code to compile.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="options" /> is <see langword="null" />.</exception>
      <returns>The results of compilation.</returns>
    </member>
    <member name="M:System.CodeDom.Compiler.CodeCompiler.System#CodeDom#Compiler#ICodeCompiler#CompileAssemblyFromSourceBatch(System.CodeDom.Compiler.CompilerParameters,System.String[])">
      <summary>For a description of this member, see <see cref="M:System.CodeDom.Compiler.ICodeCompiler.CompileAssemblyFromSourceBatch(System.CodeDom.Compiler.CompilerParameters,System.String[])" />.</summary>
      <param name="options">A <see cref="T:System.CodeDom.Compiler.CompilerParameters" /> object that indicates the compiler options.</param>
      <param name="sources">An array of strings that indicates the source code to compile.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="options" /> is <see langword="null" />.</exception>
      <returns>The results of compilation.</returns>
    </member>
    <member name="P:System.CodeDom.Compiler.CodeCompiler.CompilerName">
      <summary>Gets the name of the compiler executable.</summary>
      <returns>The name of the compiler executable.</returns>
    </member>
    <member name="P:System.CodeDom.Compiler.CodeCompiler.FileExtension">
      <summary>Gets the file name extension to use for source files.</summary>
      <returns>The file name extension to use for source files.</returns>
    </member>
    <member name="T:System.CodeDom.Compiler.CodeDomProvider">
      <summary>Provides a base class for <see cref="T:System.CodeDom.Compiler.CodeDomProvider" /> implementations. This class is abstract.</summary>
    </member>
    <member name="M:System.CodeDom.Compiler.CodeDomProvider.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.CodeDom.Compiler.CodeDomProvider" /> class.</summary>
    </member>
    <member name="M:System.CodeDom.Compiler.CodeDomProvider.CompileAssemblyFromDom(System.CodeDom.Compiler.CompilerParameters,System.CodeDom.CodeCompileUnit[])">
      <summary>Compiles an assembly based on the <see cref="N:System.CodeDom" /> trees contained in the specified array of <see cref="T:System.CodeDom.CodeCompileUnit" /> objects, using the specified compiler settings.</summary>
      <param name="options">A <see cref="T:System.CodeDom.Compiler.CompilerParameters" /> object that indicates the settings for the compilation.</param>
      <param name="compilationUnits">An array of type <see cref="T:System.CodeDom.CodeCompileUnit" /> that indicates the code to compile.</param>
      <exception cref="T:System.NotImplementedException">Neither this method nor the <see cref="M:System.CodeDom.Compiler.CodeDomProvider.CreateCompiler" /> method is overridden in a derived class.</exception>
      <exception cref="T:System.PlatformNotSupportedException">.NET Core and .NET 5+ only: In all cases.</exception>
      <returns>A <see cref="T:System.CodeDom.Compiler.CompilerResults" /> object that indicates the results of the compilation.</returns>
    </member>
    <member name="M:System.CodeDom.Compiler.CodeDomProvider.CompileAssemblyFromFile(System.CodeDom.Compiler.CompilerParameters,System.String[])">
      <summary>Compiles an assembly from the source code contained in the specified files, using the specified compiler settings.</summary>
      <param name="options">A <see cref="T:System.CodeDom.Compiler.CompilerParameters" /> object that indicates the settings for the compilation.</param>
      <param name="fileNames">An array of the names of the files to compile.</param>
      <exception cref="T:System.NotImplementedException">Neither this method nor the <see cref="M:System.CodeDom.Compiler.CodeDomProvider.CreateCompiler" /> method is overridden in a derived class.</exception>
      <exception cref="T:System.PlatformNotSupportedException">.NET Core and .NET 5+ only: In all cases.</exception>
      <returns>A <see cref="T:System.CodeDom.Compiler.CompilerResults" /> object that indicates the results of compilation.</returns>
    </member>
    <member name="M:System.CodeDom.Compiler.CodeDomProvider.CompileAssemblyFromSource(System.CodeDom.Compiler.CompilerParameters,System.String[])">
      <summary>Compiles an assembly from the specified array of strings containing source code, using the specified compiler settings.</summary>
      <param name="options">A <see cref="T:System.CodeDom.Compiler.CompilerParameters" /> object that indicates the compiler settings for this compilation.</param>
      <param name="sources">An array of source code strings to compile.</param>
      <exception cref="T:System.NotImplementedException">Neither this method nor the <see cref="M:System.CodeDom.Compiler.CodeDomProvider.CreateCompiler" /> method is overridden in a derived class.</exception>
      <exception cref="T:System.PlatformNotSupportedException">.NET Core and .NET 5+ only: In all cases.</exception>
      <returns>A <see cref="T:System.CodeDom.Compiler.CompilerResults" /> object that indicates the results of compilation.</returns>
    </member>
    <member name="M:System.CodeDom.Compiler.CodeDomProvider.CreateCompiler">
      <summary>When overridden in a derived class, creates a new code compiler.</summary>
      <returns>An <see cref="T:System.CodeDom.Compiler.ICodeCompiler" /> that can be used for compilation of <see cref="N:System.CodeDom" /> based source code representations.</returns>
    </member>
    <member name="M:System.CodeDom.Compiler.CodeDomProvider.CreateEscapedIdentifier(System.String)">
      <summary>Creates an escaped identifier for the specified value.</summary>
      <param name="value">The string for which to create an escaped identifier.</param>
      <exception cref="T:System.NotImplementedException">Neither this method nor the <see cref="M:System.CodeDom.Compiler.CodeDomProvider.CreateGenerator" /> method is overridden in a derived class.</exception>
      <returns>The escaped identifier for the value.</returns>
    </member>
    <member name="M:System.CodeDom.Compiler.CodeDomProvider.CreateGenerator">
      <summary>When overridden in a derived class, creates a new code generator.</summary>
      <returns>An <see cref="T:System.CodeDom.Compiler.ICodeGenerator" /> that can be used to generate <see cref="N:System.CodeDom" /> based source code representations.</returns>
    </member>
    <member name="M:System.CodeDom.Compiler.CodeDomProvider.CreateGenerator(System.IO.TextWriter)">
      <summary>When overridden in a derived class, creates a new code generator using the specified <see cref="T:System.IO.TextWriter" /> for output.</summary>
      <param name="output">A <see cref="T:System.IO.TextWriter" /> to use to output.</param>
      <returns>An <see cref="T:System.CodeDom.Compiler.ICodeGenerator" /> that can be used to generate <see cref="N:System.CodeDom" /> based source code representations.</returns>
    </member>
    <member name="M:System.CodeDom.Compiler.CodeDomProvider.CreateGenerator(System.String)">
      <summary>When overridden in a derived class, creates a new code generator using the specified file name for output.</summary>
      <param name="fileName">The file name to output to.</param>
      <returns>An <see cref="T:System.CodeDom.Compiler.ICodeGenerator" /> that can be used to generate <see cref="N:System.CodeDom" /> based source code representations.</returns>
    </member>
    <member name="M:System.CodeDom.Compiler.CodeDomProvider.CreateParser">
      <summary>When overridden in a derived class, creates a new code parser.</summary>
      <returns>An <see cref="T:System.CodeDom.Compiler.ICodeParser" /> that can be used to parse source code. The base implementation always returns <see langword="null" />.</returns>
    </member>
    <member name="M:System.CodeDom.Compiler.CodeDomProvider.CreateProvider(System.String)">
      <summary>Gets a <see cref="T:System.CodeDom.Compiler.CodeDomProvider" /> instance for the specified language.</summary>
      <param name="language">The language name.</param>
      <exception cref="T:System.Configuration.ConfigurationErrorsException">The <paramref name="language" /> does not have a configured provider on this computer.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="language" /> is <see langword="null" />.</exception>
      <exception cref="T:System.Security.SecurityException">The caller does not have the required permission.</exception>
      <returns>A CodeDOM provider that is implemented for the specified language name.</returns>
    </member>
    <member name="M:System.CodeDom.Compiler.CodeDomProvider.CreateProvider(System.String,System.Collections.Generic.IDictionary{System.String,System.String})">
      <summary>Gets a <see cref="T:System.CodeDom.Compiler.CodeDomProvider" /> instance for the specified language and provider options.</summary>
      <param name="language">The language name.</param>
      <param name="providerOptions">A collection of provider options from the configuration file.</param>
      <returns>A CodeDOM provider that is implemented for the specified language name and options.</returns>
    </member>
    <member name="M:System.CodeDom.Compiler.CodeDomProvider.CreateValidIdentifier(System.String)">
      <summary>Creates a valid identifier for the specified value.</summary>
      <param name="value">The string for which to generate a valid identifier.</param>
      <exception cref="T:System.NotImplementedException">Neither this method nor the <see cref="M:System.CodeDom.Compiler.CodeDomProvider.CreateGenerator" /> method is overridden in a derived class.</exception>
      <returns>A valid identifier for the specified value.</returns>
    </member>
    <member name="M:System.CodeDom.Compiler.CodeDomProvider.GenerateCodeFromCompileUnit(System.CodeDom.CodeCompileUnit,System.IO.TextWriter,System.CodeDom.Compiler.CodeGeneratorOptions)">
      <summary>Generates code for the specified Code Document Object Model (CodeDOM) compilation unit and sends it to the specified text writer, using the specified options.</summary>
      <param name="compileUnit">A <see cref="T:System.CodeDom.CodeCompileUnit" /> for which to generate code.</param>
      <param name="writer">The <see cref="T:System.IO.TextWriter" /> to which the output code is sent.</param>
      <param name="options">A <see cref="T:System.CodeDom.Compiler.CodeGeneratorOptions" /> that indicates the options to use for generating code.</param>
      <exception cref="T:System.NotImplementedException">Neither this method nor the <see cref="M:System.CodeDom.Compiler.CodeDomProvider.CreateGenerator" /> method is overridden in a derived class.</exception>
    </member>
    <member name="M:System.CodeDom.Compiler.CodeDomProvider.GenerateCodeFromExpression(System.CodeDom.CodeExpression,System.IO.TextWriter,System.CodeDom.Compiler.CodeGeneratorOptions)">
      <summary>Generates code for the specified Code Document Object Model (CodeDOM) expression and sends it to the specified text writer, using the specified options.</summary>
      <param name="expression">A <see cref="T:System.CodeDom.CodeExpression" /> object that indicates the expression for which to generate code.</param>
      <param name="writer">The <see cref="T:System.IO.TextWriter" /> to which output code is sent.</param>
      <param name="options">A <see cref="T:System.CodeDom.Compiler.CodeGeneratorOptions" /> that indicates the options to use for generating code.</param>
      <exception cref="T:System.NotImplementedException">Neither this method nor the <see cref="M:System.CodeDom.Compiler.CodeDomProvider.CreateGenerator" /> method is overridden in a derived class.</exception>
    </member>
    <member name="M:System.CodeDom.Compiler.CodeDomProvider.GenerateCodeFromMember(System.CodeDom.CodeTypeMember,System.IO.TextWriter,System.CodeDom.Compiler.CodeGeneratorOptions)">
      <summary>Generates code for the specified Code Document Object Model (CodeDOM) member declaration and sends it to the specified text writer, using the specified options.</summary>
      <param name="member">A <see cref="T:System.CodeDom.CodeTypeMember" /> object that indicates the member for which to generate code.</param>
      <param name="writer">The <see cref="T:System.IO.TextWriter" /> to which output code is sent.</param>
      <param name="options">A <see cref="T:System.CodeDom.Compiler.CodeGeneratorOptions" /> that indicates the options to use for generating code.</param>
      <exception cref="T:System.NotImplementedException">This method is not overridden in a derived class.</exception>
    </member>
    <member name="M:System.CodeDom.Compiler.CodeDomProvider.GenerateCodeFromNamespace(System.CodeDom.CodeNamespace,System.IO.TextWriter,System.CodeDom.Compiler.CodeGeneratorOptions)">
      <summary>Generates code for the specified Code Document Object Model (CodeDOM) namespace and sends it to the specified text writer, using the specified options.</summary>
      <param name="codeNamespace">A <see cref="T:System.CodeDom.CodeNamespace" /> object that indicates the namespace for which to generate code.</param>
      <param name="writer">The <see cref="T:System.IO.TextWriter" /> to which output code is sent.</param>
      <param name="options">A <see cref="T:System.CodeDom.Compiler.CodeGeneratorOptions" /> that indicates the options to use for generating code.</param>
      <exception cref="T:System.NotImplementedException">Neither this method nor the <see cref="M:System.CodeDom.Compiler.CodeDomProvider.CreateGenerator" /> method is overridden in a derived class.</exception>
    </member>
    <member name="M:System.CodeDom.Compiler.CodeDomProvider.GenerateCodeFromStatement(System.CodeDom.CodeStatement,System.IO.TextWriter,System.CodeDom.Compiler.CodeGeneratorOptions)">
      <summary>Generates code for the specified Code Document Object Model (CodeDOM) statement and sends it to the specified text writer, using the specified options.</summary>
      <param name="statement">A <see cref="T:System.CodeDom.CodeStatement" /> containing the CodeDOM elements for which to generate code.</param>
      <param name="writer">The <see cref="T:System.IO.TextWriter" /> to which output code is sent.</param>
      <param name="options">A <see cref="T:System.CodeDom.Compiler.CodeGeneratorOptions" /> that indicates the options to use for generating code.</param>
      <exception cref="T:System.NotImplementedException">Neither this method nor the <see cref="M:System.CodeDom.Compiler.CodeDomProvider.CreateGenerator" /> method is overridden in a derived class.</exception>
    </member>
    <member name="M:System.CodeDom.Compiler.CodeDomProvider.GenerateCodeFromType(System.CodeDom.CodeTypeDeclaration,System.IO.TextWriter,System.CodeDom.Compiler.CodeGeneratorOptions)">
      <summary>Generates code for the specified Code Document Object Model (CodeDOM) type declaration and sends it to the specified text writer, using the specified options.</summary>
      <param name="codeType">A <see cref="T:System.CodeDom.CodeTypeDeclaration" /> object that indicates the type for which to generate code.</param>
      <param name="writer">The <see cref="T:System.IO.TextWriter" /> to which output code is sent.</param>
      <param name="options">A <see cref="T:System.CodeDom.Compiler.CodeGeneratorOptions" /> that indicates the options to use for generating code.</param>
      <exception cref="T:System.NotImplementedException">Neither this method nor the <see cref="M:System.CodeDom.Compiler.CodeDomProvider.CreateGenerator" /> method is overridden in a derived class.</exception>
    </member>
    <member name="M:System.CodeDom.Compiler.CodeDomProvider.GetAllCompilerInfo">
      <summary>Returns the language provider and compiler configuration settings for this computer.</summary>
      <exception cref="T:System.Security.SecurityException">The caller does not have the required permission.</exception>
      <returns>An array of type <see cref="T:System.CodeDom.Compiler.CompilerInfo" /> representing the settings of all configured <see cref="T:System.CodeDom.Compiler.CodeDomProvider" /> implementations.</returns>
    </member>
    <member name="M:System.CodeDom.Compiler.CodeDomProvider.GetCompilerInfo(System.String)">
      <summary>Returns the language provider and compiler configuration settings for the specified language.</summary>
      <param name="language">A language name.</param>
      <exception cref="T:System.Configuration.ConfigurationException">The <paramref name="language" /> does not have a configured provider on this computer.</exception>
      <exception cref="T:System.Configuration.ConfigurationErrorsException">The <paramref name="language" /> is <see langword="null" />.</exception>
      <exception cref="T:System.Security.SecurityException">The caller does not have the required permission.</exception>
      <returns>A <see cref="T:System.CodeDom.Compiler.CompilerInfo" /> object populated with settings of the configured <see cref="T:System.CodeDom.Compiler.CodeDomProvider" /> implementation.</returns>
    </member>
    <member name="M:System.CodeDom.Compiler.CodeDomProvider.GetConverter(System.Type)">
      <summary>Gets a <see cref="T:System.ComponentModel.TypeConverter" /> for the specified data type.</summary>
      <param name="type">The type of object to retrieve a type converter for.</param>
      <returns>A <see cref="T:System.ComponentModel.TypeConverter" /> for the specified type, or <see langword="null" /> if a <see cref="T:System.ComponentModel.TypeConverter" /> for the specified type cannot be found.</returns>
    </member>
    <member name="M:System.CodeDom.Compiler.CodeDomProvider.GetLanguageFromExtension(System.String)">
      <summary>Returns a language name associated with the specified file name extension, as configured in the <see cref="T:System.CodeDom.Compiler.CodeDomProvider" /> compiler configuration section.</summary>
      <param name="extension">A file name extension.</param>
      <exception cref="T:System.Configuration.ConfigurationException">The <paramref name="extension" /> does not have a configured language provider on this computer.</exception>
      <exception cref="T:System.Configuration.ConfigurationErrorsException">The <paramref name="extension" /> is <see langword="null" />.</exception>
      <exception cref="T:System.Security.SecurityException">The caller does not have the required permission.</exception>
      <returns>A language name associated with the file name extension, as configured in the <see cref="T:System.CodeDom.Compiler.CodeDomProvider" /> compiler configuration settings.</returns>
    </member>
    <member name="M:System.CodeDom.Compiler.CodeDomProvider.GetTypeOutput(System.CodeDom.CodeTypeReference)">
      <summary>Gets the type indicated by the specified <see cref="T:System.CodeDom.CodeTypeReference" />.</summary>
      <param name="type">A <see cref="T:System.CodeDom.CodeTypeReference" /> that indicates the type to return.</param>
      <exception cref="T:System.NotImplementedException">Neither this method nor the <see cref="M:System.CodeDom.Compiler.CodeDomProvider.CreateGenerator" /> method is overridden in a derived class.</exception>
      <returns>A text representation of the specified type, formatted for the language in which code is generated by this code generator. In Visual Basic, for example, passing in a <see cref="T:System.CodeDom.CodeTypeReference" /> for the <see cref="T:System.Int32" /> type will return "Integer".</returns>
    </member>
    <member name="M:System.CodeDom.Compiler.CodeDomProvider.IsDefinedExtension(System.String)">
      <summary>Tests whether a file name extension has an associated <see cref="T:System.CodeDom.Compiler.CodeDomProvider" /> implementation configured on the computer.</summary>
      <param name="extension">A file name extension.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="extension" /> is <see langword="null" />.</exception>
      <exception cref="T:System.Security.SecurityException">The caller does not have the required permission.</exception>
      <returns>
        <see langword="true" /> if a <see cref="T:System.CodeDom.Compiler.CodeDomProvider" /> implementation is configured for the specified file name extension; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.CodeDom.Compiler.CodeDomProvider.IsDefinedLanguage(System.String)">
      <summary>Tests whether a language has a <see cref="T:System.CodeDom.Compiler.CodeDomProvider" /> implementation configured on the computer.</summary>
      <param name="language">The language name.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="language" /> is <see langword="null" />.</exception>
      <exception cref="T:System.Security.SecurityException">The caller does not have the required permission.</exception>
      <returns>
        <see langword="true" /> if a <see cref="T:System.CodeDom.Compiler.CodeDomProvider" /> implementation is configured for the specified language; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.CodeDom.Compiler.CodeDomProvider.IsValidIdentifier(System.String)">
      <summary>Returns a value that indicates whether the specified value is a valid identifier for the current language.</summary>
      <param name="value">The value to verify as a valid identifier.</param>
      <exception cref="T:System.NotImplementedException">Neither this method nor the <see cref="M:System.CodeDom.Compiler.CodeDomProvider.CreateGenerator" /> method is overridden in a derived class.</exception>
      <returns>
        <see langword="true" /> if the <paramref name="value" /> parameter is a valid identifier; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.CodeDom.Compiler.CodeDomProvider.Parse(System.IO.TextReader)">
      <summary>Compiles the code read from the specified text stream into a <see cref="T:System.CodeDom.CodeCompileUnit" />.</summary>
      <param name="codeStream">A <see cref="T:System.IO.TextReader" /> object that is used to read the code to be parsed.</param>
      <exception cref="T:System.NotImplementedException">Neither this method nor the <see cref="M:System.CodeDom.Compiler.CodeDomProvider.CreateGenerator" /> method is overridden in a derived class.</exception>
      <returns>A <see cref="T:System.CodeDom.CodeCompileUnit" /> that contains a representation of the parsed code.</returns>
    </member>
    <member name="M:System.CodeDom.Compiler.CodeDomProvider.Supports(System.CodeDom.Compiler.GeneratorSupport)">
      <summary>Returns a value indicating whether the specified code generation support is provided.</summary>
      <param name="generatorSupport">A <see cref="T:System.CodeDom.Compiler.GeneratorSupport" /> object that indicates the type of code generation support to verify.</param>
      <exception cref="T:System.NotImplementedException">Neither this method nor the <see cref="M:System.CodeDom.Compiler.CodeDomProvider.CreateGenerator" /> method is overridden in a derived class.</exception>
      <returns>
        <see langword="true" /> if the specified code generation support is provided; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.CodeDom.Compiler.CodeDomProvider.FileExtension">
      <summary>Gets the default file name extension to use for source code files in the current language.</summary>
      <returns>A file name extension corresponding to the extension of the source files of the current language. The base implementation always returns <see cref="F:System.String.Empty" />.</returns>
    </member>
    <member name="P:System.CodeDom.Compiler.CodeDomProvider.LanguageOptions">
      <summary>Gets a language features identifier.</summary>
      <returns>A <see cref="T:System.CodeDom.Compiler.LanguageOptions" /> that indicates special features of the language.</returns>
    </member>
    <member name="T:System.CodeDom.Compiler.CodeGenerator">
      <summary>Provides an example implementation of the <see cref="T:System.CodeDom.Compiler.ICodeGenerator" /> interface. This class is abstract.</summary>
    </member>
    <member name="M:System.CodeDom.Compiler.CodeGenerator.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.CodeDom.Compiler.CodeGenerator" /> class.</summary>
    </member>
    <member name="M:System.CodeDom.Compiler.CodeGenerator.ContinueOnNewLine(System.String)">
      <summary>Generates a line-continuation character and outputs the specified string on a new line.</summary>
      <param name="st">The string to write on the new line.</param>
    </member>
    <member name="M:System.CodeDom.Compiler.CodeGenerator.CreateEscapedIdentifier(System.String)">
      <summary>Creates an escaped identifier for the specified value.</summary>
      <param name="value">The string to create an escaped identifier for.</param>
      <returns>The escaped identifier for the value.</returns>
    </member>
    <member name="M:System.CodeDom.Compiler.CodeGenerator.CreateValidIdentifier(System.String)">
      <summary>Creates a valid identifier for the specified value.</summary>
      <param name="value">A string to create a valid identifier for.</param>
      <returns>A valid identifier for the value.</returns>
    </member>
    <member name="M:System.CodeDom.Compiler.CodeGenerator.GenerateArgumentReferenceExpression(System.CodeDom.CodeArgumentReferenceExpression)">
      <summary>Generates code for the specified argument reference expression.</summary>
      <param name="e">A <see cref="T:System.CodeDom.CodeArgumentReferenceExpression" /> that indicates the expression to generate code for.</param>
    </member>
    <member name="M:System.CodeDom.Compiler.CodeGenerator.GenerateArrayCreateExpression(System.CodeDom.CodeArrayCreateExpression)">
      <summary>Generates code for the specified array creation expression.</summary>
      <param name="e">A <see cref="T:System.CodeDom.CodeArrayCreateExpression" /> that indicates the expression to generate code for.</param>
    </member>
    <member name="M:System.CodeDom.Compiler.CodeGenerator.GenerateArrayIndexerExpression(System.CodeDom.CodeArrayIndexerExpression)">
      <summary>Generates code for the specified array indexer expression.</summary>
      <param name="e">A <see cref="T:System.CodeDom.CodeArrayIndexerExpression" /> that indicates the expression to generate code for.</param>
    </member>
    <member name="M:System.CodeDom.Compiler.CodeGenerator.GenerateAssignStatement(System.CodeDom.CodeAssignStatement)">
      <summary>Generates code for the specified assignment statement.</summary>
      <param name="e">A <see cref="T:System.CodeDom.CodeAssignStatement" /> that indicates the statement to generate code for.</param>
    </member>
    <member name="M:System.CodeDom.Compiler.CodeGenerator.GenerateAttachEventStatement(System.CodeDom.CodeAttachEventStatement)">
      <summary>Generates code for the specified attach event statement.</summary>
      <param name="e">A <see cref="T:System.CodeDom.CodeAttachEventStatement" /> that indicates the statement to generate code for.</param>
    </member>
    <member name="M:System.CodeDom.Compiler.CodeGenerator.GenerateAttributeDeclarationsEnd(System.CodeDom.CodeAttributeDeclarationCollection)">
      <summary>Generates code for the specified attribute block end.</summary>
      <param name="attributes">A <see cref="T:System.CodeDom.CodeAttributeDeclarationCollection" /> that indicates the end of the attribute block to generate code for.</param>
    </member>
    <member name="M:System.CodeDom.Compiler.CodeGenerator.GenerateAttributeDeclarationsStart(System.CodeDom.CodeAttributeDeclarationCollection)">
      <summary>Generates code for the specified attribute block start.</summary>
      <param name="attributes">A <see cref="T:System.CodeDom.CodeAttributeDeclarationCollection" /> that indicates the start of the attribute block to generate code for.</param>
    </member>
    <member name="M:System.CodeDom.Compiler.CodeGenerator.GenerateBaseReferenceExpression(System.CodeDom.CodeBaseReferenceExpression)">
      <summary>Generates code for the specified base reference expression.</summary>
      <param name="e">A <see cref="T:System.CodeDom.CodeBaseReferenceExpression" /> that indicates the expression to generate code for.</param>
    </member>
    <member name="M:System.CodeDom.Compiler.CodeGenerator.GenerateBinaryOperatorExpression(System.CodeDom.CodeBinaryOperatorExpression)">
      <summary>Generates code for the specified binary operator expression.</summary>
      <param name="e">A <see cref="T:System.CodeDom.CodeBinaryOperatorExpression" /> that indicates the expression to generate code for.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="e" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.CodeDom.Compiler.CodeGenerator.GenerateCastExpression(System.CodeDom.CodeCastExpression)">
      <summary>Generates code for the specified cast expression.</summary>
      <param name="e">A <see cref="T:System.CodeDom.CodeCastExpression" /> that indicates the expression to generate code for.</param>
    </member>
    <member name="M:System.CodeDom.Compiler.CodeGenerator.GenerateCodeFromMember(System.CodeDom.CodeTypeMember,System.IO.TextWriter,System.CodeDom.Compiler.CodeGeneratorOptions)">
      <summary>Generates code for the specified class member using the specified text writer and code generator options.</summary>
      <param name="member">The class member to generate code for.</param>
      <param name="writer">The text writer to output code to.</param>
      <param name="options">The options to use when generating the code.</param>
      <exception cref="T:System.InvalidOperationException">The <see cref="P:System.CodeDom.Compiler.CodeGenerator.Output" /> property is not <see langword="null" />.</exception>
    </member>
    <member name="M:System.CodeDom.Compiler.CodeGenerator.GenerateComment(System.CodeDom.CodeComment)">
      <summary>Generates code for the specified comment.</summary>
      <param name="e">A <see cref="T:System.CodeDom.CodeComment" /> to generate code for.</param>
    </member>
    <member name="M:System.CodeDom.Compiler.CodeGenerator.GenerateCommentStatement(System.CodeDom.CodeCommentStatement)">
      <summary>Generates code for the specified comment statement.</summary>
      <param name="e">The statement to generate code for.</param>
      <exception cref="T:System.ArgumentException">The <see cref="P:System.CodeDom.CodeCommentStatement.Comment" /> property of <paramref name="e" /> is not set.</exception>
    </member>
    <member name="M:System.CodeDom.Compiler.CodeGenerator.GenerateCommentStatements(System.CodeDom.CodeCommentStatementCollection)">
      <summary>Generates code for the specified comment statements.</summary>
      <param name="e">The expression to generate code for.</param>
    </member>
    <member name="M:System.CodeDom.Compiler.CodeGenerator.GenerateCompileUnit(System.CodeDom.CodeCompileUnit)">
      <summary>Generates code for the specified compile unit.</summary>
      <param name="e">The compile unit to generate code for.</param>
    </member>
    <member name="M:System.CodeDom.Compiler.CodeGenerator.GenerateCompileUnitEnd(System.CodeDom.CodeCompileUnit)">
      <summary>Generates code for the end of a compile unit.</summary>
      <param name="e">The compile unit to generate code for.</param>
    </member>
    <member name="M:System.CodeDom.Compiler.CodeGenerator.GenerateCompileUnitStart(System.CodeDom.CodeCompileUnit)">
      <summary>Generates code for the start of a compile unit.</summary>
      <param name="e">The compile unit to generate code for.</param>
    </member>
    <member name="M:System.CodeDom.Compiler.CodeGenerator.GenerateConditionStatement(System.CodeDom.CodeConditionStatement)">
      <summary>Generates code for the specified conditional statement.</summary>
      <param name="e">The statement to generate code for.</param>
    </member>
    <member name="M:System.CodeDom.Compiler.CodeGenerator.GenerateConstructor(System.CodeDom.CodeConstructor,System.CodeDom.CodeTypeDeclaration)">
      <summary>Generates code for the specified constructor.</summary>
      <param name="e">The constructor to generate code for.</param>
      <param name="c">The type of the object that this constructor constructs.</param>
    </member>
    <member name="M:System.CodeDom.Compiler.CodeGenerator.GenerateDecimalValue(System.Decimal)">
      <summary>Generates code for the specified decimal value.</summary>
      <param name="d">The decimal value to generate code for.</param>
    </member>
    <member name="M:System.CodeDom.Compiler.CodeGenerator.GenerateDefaultValueExpression(System.CodeDom.CodeDefaultValueExpression)">
      <summary>Generates code for the specified reference to a default value.</summary>
      <param name="e">The reference to generate code for.</param>
    </member>
    <member name="M:System.CodeDom.Compiler.CodeGenerator.GenerateDelegateCreateExpression(System.CodeDom.CodeDelegateCreateExpression)">
      <summary>Generates code for the specified delegate creation expression.</summary>
      <param name="e">The expression to generate code for.</param>
    </member>
    <member name="M:System.CodeDom.Compiler.CodeGenerator.GenerateDelegateInvokeExpression(System.CodeDom.CodeDelegateInvokeExpression)">
      <summary>Generates code for the specified delegate invoke expression.</summary>
      <param name="e">The expression to generate code for.</param>
    </member>
    <member name="M:System.CodeDom.Compiler.CodeGenerator.GenerateDirectionExpression(System.CodeDom.CodeDirectionExpression)">
      <summary>Generates code for the specified direction expression.</summary>
      <param name="e">The expression to generate code for.</param>
    </member>
    <member name="M:System.CodeDom.Compiler.CodeGenerator.GenerateDirectives(System.CodeDom.CodeDirectiveCollection)">
      <summary>Generates code for the specified code directives.</summary>
      <param name="directives">The code directives to generate code for.</param>
    </member>
    <member name="M:System.CodeDom.Compiler.CodeGenerator.GenerateDoubleValue(System.Double)">
      <summary>Generates code for a double-precision floating point number.</summary>
      <param name="d">The value to generate code for.</param>
    </member>
    <member name="M:System.CodeDom.Compiler.CodeGenerator.GenerateEntryPointMethod(System.CodeDom.CodeEntryPointMethod,System.CodeDom.CodeTypeDeclaration)">
      <summary>Generates code for the specified entry point method.</summary>
      <param name="e">The entry point for the code.</param>
      <param name="c">The code that declares the type.</param>
    </member>
    <member name="M:System.CodeDom.Compiler.CodeGenerator.GenerateEvent(System.CodeDom.CodeMemberEvent,System.CodeDom.CodeTypeDeclaration)">
      <summary>Generates code for the specified event.</summary>
      <param name="e">The member event to generate code for.</param>
      <param name="c">The type of the object that this event occurs on.</param>
    </member>
    <member name="M:System.CodeDom.Compiler.CodeGenerator.GenerateEventReferenceExpression(System.CodeDom.CodeEventReferenceExpression)">
      <summary>Generates code for the specified event reference expression.</summary>
      <param name="e">The expression to generate code for.</param>
    </member>
    <member name="M:System.CodeDom.Compiler.CodeGenerator.GenerateExpression(System.CodeDom.CodeExpression)">
      <summary>Generates code for the specified code expression.</summary>
      <param name="e">The code expression to generate code for.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="e" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="e" /> is not a valid <see cref="T:System.CodeDom.CodeStatement" />.</exception>
    </member>
    <member name="M:System.CodeDom.Compiler.CodeGenerator.GenerateExpressionStatement(System.CodeDom.CodeExpressionStatement)">
      <summary>Generates code for the specified expression statement.</summary>
      <param name="e">The statement to generate code for.</param>
    </member>
    <member name="M:System.CodeDom.Compiler.CodeGenerator.GenerateField(System.CodeDom.CodeMemberField)">
      <summary>Generates code for the specified member field.</summary>
      <param name="e">The field to generate code for.</param>
    </member>
    <member name="M:System.CodeDom.Compiler.CodeGenerator.GenerateFieldReferenceExpression(System.CodeDom.CodeFieldReferenceExpression)">
      <summary>Generates code for the specified field reference expression.</summary>
      <param name="e">The expression to generate code for.</param>
    </member>
    <member name="M:System.CodeDom.Compiler.CodeGenerator.GenerateGotoStatement(System.CodeDom.CodeGotoStatement)">
      <summary>Generates code for the specified <see langword="goto" /> statement.</summary>
      <param name="e">The expression to generate code for.</param>
    </member>
    <member name="M:System.CodeDom.Compiler.CodeGenerator.GenerateIndexerExpression(System.CodeDom.CodeIndexerExpression)">
      <summary>Generates code for the specified indexer expression.</summary>
      <param name="e">The expression to generate code for.</param>
    </member>
    <member name="M:System.CodeDom.Compiler.CodeGenerator.GenerateIterationStatement(System.CodeDom.CodeIterationStatement)">
      <summary>Generates code for the specified iteration statement.</summary>
      <param name="e">The statement to generate code for.</param>
    </member>
    <member name="M:System.CodeDom.Compiler.CodeGenerator.GenerateLabeledStatement(System.CodeDom.CodeLabeledStatement)">
      <summary>Generates code for the specified labeled statement.</summary>
      <param name="e">The statement to generate code for.</param>
    </member>
    <member name="M:System.CodeDom.Compiler.CodeGenerator.GenerateLinePragmaEnd(System.CodeDom.CodeLinePragma)">
      <summary>Generates code for the specified line pragma end.</summary>
      <param name="e">The end of the line pragma to generate code for.</param>
    </member>
    <member name="M:System.CodeDom.Compiler.CodeGenerator.GenerateLinePragmaStart(System.CodeDom.CodeLinePragma)">
      <summary>Generates code for the specified line pragma start.</summary>
      <param name="e">The start of the line pragma to generate code for.</param>
    </member>
    <member name="M:System.CodeDom.Compiler.CodeGenerator.GenerateMethod(System.CodeDom.CodeMemberMethod,System.CodeDom.CodeTypeDeclaration)">
      <summary>Generates code for the specified method.</summary>
      <param name="e">The member method to generate code for.</param>
      <param name="c">The type of the object that this method occurs on.</param>
    </member>
    <member name="M:System.CodeDom.Compiler.CodeGenerator.GenerateMethodInvokeExpression(System.CodeDom.CodeMethodInvokeExpression)">
      <summary>Generates code for the specified method invoke expression.</summary>
      <param name="e">The expression to generate code for.</param>
    </member>
    <member name="M:System.CodeDom.Compiler.CodeGenerator.GenerateMethodReferenceExpression(System.CodeDom.CodeMethodReferenceExpression)">
      <summary>Generates code for the specified method reference expression.</summary>
      <param name="e">The expression to generate code for.</param>
    </member>
    <member name="M:System.CodeDom.Compiler.CodeGenerator.GenerateMethodReturnStatement(System.CodeDom.CodeMethodReturnStatement)">
      <summary>Generates code for the specified method return statement.</summary>
      <param name="e">The statement to generate code for.</param>
    </member>
    <member name="M:System.CodeDom.Compiler.CodeGenerator.GenerateNamespace(System.CodeDom.CodeNamespace)">
      <summary>Generates code for the specified namespace.</summary>
      <param name="e">The namespace to generate code for.</param>
    </member>
    <member name="M:System.CodeDom.Compiler.CodeGenerator.GenerateNamespaceEnd(System.CodeDom.CodeNamespace)">
      <summary>Generates code for the end of a namespace.</summary>
      <param name="e">The namespace to generate code for.</param>
    </member>
    <member name="M:System.CodeDom.Compiler.CodeGenerator.GenerateNamespaceImport(System.CodeDom.CodeNamespaceImport)">
      <summary>Generates code for the specified namespace import.</summary>
      <param name="e">The namespace import to generate code for.</param>
    </member>
    <member name="M:System.CodeDom.Compiler.CodeGenerator.GenerateNamespaceImports(System.CodeDom.CodeNamespace)">
      <summary>Generates code for the specified namespace import.</summary>
      <param name="e">The namespace import to generate code for.</param>
    </member>
    <member name="M:System.CodeDom.Compiler.CodeGenerator.GenerateNamespaces(System.CodeDom.CodeCompileUnit)">
      <summary>Generates code for the namespaces in the specified compile unit.</summary>
      <param name="e">The compile unit to generate namespaces for.</param>
    </member>
    <member name="M:System.CodeDom.Compiler.CodeGenerator.GenerateNamespaceStart(System.CodeDom.CodeNamespace)">
      <summary>Generates code for the start of a namespace.</summary>
      <param name="e">The namespace to generate code for.</param>
    </member>
    <member name="M:System.CodeDom.Compiler.CodeGenerator.GenerateObjectCreateExpression(System.CodeDom.CodeObjectCreateExpression)">
      <summary>Generates code for the specified object creation expression.</summary>
      <param name="e">The expression to generate code for.</param>
    </member>
    <member name="M:System.CodeDom.Compiler.CodeGenerator.GenerateParameterDeclarationExpression(System.CodeDom.CodeParameterDeclarationExpression)">
      <summary>Generates code for the specified parameter declaration expression.</summary>
      <param name="e">The expression to generate code for.</param>
    </member>
    <member name="M:System.CodeDom.Compiler.CodeGenerator.GeneratePrimitiveExpression(System.CodeDom.CodePrimitiveExpression)">
      <summary>Generates code for the specified primitive expression.</summary>
      <param name="e">The expression to generate code for.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="e" /> uses an invalid data type. Only the following data types are valid:  
  
- string  
  
- char  
  
- byte  
  
- Int16  
  
- Int32  
  
- Int64  

- Half
  
- Single  
  
- Double  
  
- Decimal</exception>
    </member>
    <member name="M:System.CodeDom.Compiler.CodeGenerator.GenerateProperty(System.CodeDom.CodeMemberProperty,System.CodeDom.CodeTypeDeclaration)">
      <summary>Generates code for the specified property.</summary>
      <param name="e">The property to generate code for.</param>
      <param name="c">The type of the object that this property occurs on.</param>
    </member>
    <member name="M:System.CodeDom.Compiler.CodeGenerator.GeneratePropertyReferenceExpression(System.CodeDom.CodePropertyReferenceExpression)">
      <summary>Generates code for the specified property reference expression.</summary>
      <param name="e">The expression to generate code for.</param>
    </member>
    <member name="M:System.CodeDom.Compiler.CodeGenerator.GeneratePropertySetValueReferenceExpression(System.CodeDom.CodePropertySetValueReferenceExpression)">
      <summary>Generates code for the specified property set value reference expression.</summary>
      <param name="e">The expression to generate code for.</param>
    </member>
    <member name="M:System.CodeDom.Compiler.CodeGenerator.GenerateRemoveEventStatement(System.CodeDom.CodeRemoveEventStatement)">
      <summary>Generates code for the specified remove event statement.</summary>
      <param name="e">The statement to generate code for.</param>
    </member>
    <member name="M:System.CodeDom.Compiler.CodeGenerator.GenerateSingleFloatValue(System.Single)">
      <summary>Generates code for a single-precision floating point number.</summary>
      <param name="s">The value to generate code for.</param>
    </member>
    <member name="M:System.CodeDom.Compiler.CodeGenerator.GenerateSnippetCompileUnit(System.CodeDom.CodeSnippetCompileUnit)">
      <summary>Outputs the code of the specified literal code fragment compile unit.</summary>
      <param name="e">The literal code fragment compile unit to generate code for.</param>
    </member>
    <member name="M:System.CodeDom.Compiler.CodeGenerator.GenerateSnippetExpression(System.CodeDom.CodeSnippetExpression)">
      <summary>Outputs the code of the specified literal code fragment expression.</summary>
      <param name="e">The expression to generate code for.</param>
    </member>
    <member name="M:System.CodeDom.Compiler.CodeGenerator.GenerateSnippetMember(System.CodeDom.CodeSnippetTypeMember)">
      <summary>Outputs the code of the specified literal code fragment class member.</summary>
      <param name="e">The member to generate code for.</param>
    </member>
    <member name="M:System.CodeDom.Compiler.CodeGenerator.GenerateSnippetStatement(System.CodeDom.CodeSnippetStatement)">
      <summary>Outputs the code of the specified literal code fragment statement.</summary>
      <param name="e">The statement to generate code for.</param>
    </member>
    <member name="M:System.CodeDom.Compiler.CodeGenerator.GenerateStatement(System.CodeDom.CodeStatement)">
      <summary>Generates code for the specified statement.</summary>
      <param name="e">The statement to generate code for.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="e" /> is not a valid <see cref="T:System.CodeDom.CodeStatement" />.</exception>
    </member>
    <member name="M:System.CodeDom.Compiler.CodeGenerator.GenerateStatements(System.CodeDom.CodeStatementCollection)">
      <summary>Generates code for the specified statement collection.</summary>
      <param name="stmts" />
    </member>
    <member name="M:System.CodeDom.Compiler.CodeGenerator.GenerateThisReferenceExpression(System.CodeDom.CodeThisReferenceExpression)">
      <summary>Generates code for the specified this reference expression.</summary>
      <param name="e">The expression to generate code for.</param>
    </member>
    <member name="M:System.CodeDom.Compiler.CodeGenerator.GenerateThrowExceptionStatement(System.CodeDom.CodeThrowExceptionStatement)">
      <summary>Generates code for the specified throw exception statement.</summary>
      <param name="e">The statement to generate code for.</param>
    </member>
    <member name="M:System.CodeDom.Compiler.CodeGenerator.GenerateTryCatchFinallyStatement(System.CodeDom.CodeTryCatchFinallyStatement)">
      <summary>Generates code for the specified <c>try-catch-finally</c> statement.</summary>
      <param name="e">The statement to generate code for.</param>
    </member>
    <member name="M:System.CodeDom.Compiler.CodeGenerator.GenerateTypeConstructor(System.CodeDom.CodeTypeConstructor)">
      <summary>Generates code for the specified class constructor.</summary>
      <param name="e">The class constructor to generate code for.</param>
    </member>
    <member name="M:System.CodeDom.Compiler.CodeGenerator.GenerateTypeEnd(System.CodeDom.CodeTypeDeclaration)">
      <summary>Generates code for the specified end of the class.</summary>
      <param name="e">The end of the class to generate code for.</param>
    </member>
    <member name="M:System.CodeDom.Compiler.CodeGenerator.GenerateTypeOfExpression(System.CodeDom.CodeTypeOfExpression)">
      <summary>Generates code for the specified type of expression.</summary>
      <param name="e">The expression to generate code for.</param>
    </member>
    <member name="M:System.CodeDom.Compiler.CodeGenerator.GenerateTypeReferenceExpression(System.CodeDom.CodeTypeReferenceExpression)">
      <summary>Generates code for the specified type reference expression.</summary>
      <param name="e">The expression to generate code for.</param>
    </member>
    <member name="M:System.CodeDom.Compiler.CodeGenerator.GenerateTypes(System.CodeDom.CodeNamespace)">
      <summary>Generates code for the specified namespace and the classes it contains.</summary>
      <param name="e">The namespace to generate classes for.</param>
    </member>
    <member name="M:System.CodeDom.Compiler.CodeGenerator.GenerateTypeStart(System.CodeDom.CodeTypeDeclaration)">
      <summary>Generates code for the specified start of the class.</summary>
      <param name="e">The start of the class to generate code for.</param>
    </member>
    <member name="M:System.CodeDom.Compiler.CodeGenerator.GenerateVariableDeclarationStatement(System.CodeDom.CodeVariableDeclarationStatement)">
      <summary>Generates code for the specified variable declaration statement.</summary>
      <param name="e">The statement to generate code for.</param>
    </member>
    <member name="M:System.CodeDom.Compiler.CodeGenerator.GenerateVariableReferenceExpression(System.CodeDom.CodeVariableReferenceExpression)">
      <summary>Generates code for the specified variable reference expression.</summary>
      <param name="e">The expression to generate code for.</param>
    </member>
    <member name="M:System.CodeDom.Compiler.CodeGenerator.GetTypeOutput(System.CodeDom.CodeTypeReference)">
      <summary>Gets the name of the specified data type.</summary>
      <param name="value">The type whose name will be returned.</param>
      <returns>The name of the data type reference.</returns>
    </member>
    <member name="M:System.CodeDom.Compiler.CodeGenerator.IsValidIdentifier(System.String)">
      <summary>Gets a value indicating whether the specified value is a valid identifier.</summary>
      <param name="value">The value to test for conflicts with valid identifiers.</param>
      <returns>
        <see langword="true" /> if the value is a valid identifier; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.CodeDom.Compiler.CodeGenerator.IsValidLanguageIndependentIdentifier(System.String)">
      <summary>Gets a value indicating whether the specified string is a valid identifier.</summary>
      <param name="value">The string to test for validity.</param>
      <returns>
        <see langword="true" /> if the specified string is a valid identifier; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.CodeDom.Compiler.CodeGenerator.OutputAttributeArgument(System.CodeDom.CodeAttributeArgument)">
      <summary>Outputs an argument in an attribute block.</summary>
      <param name="arg">The attribute argument to generate code for.</param>
    </member>
    <member name="M:System.CodeDom.Compiler.CodeGenerator.OutputAttributeDeclarations(System.CodeDom.CodeAttributeDeclarationCollection)">
      <summary>Generates code for the specified attribute declaration collection.</summary>
      <param name="attributes">The attributes to generate code for.</param>
    </member>
    <member name="M:System.CodeDom.Compiler.CodeGenerator.OutputDirection(System.CodeDom.FieldDirection)">
      <summary>Generates code for the specified <see cref="T:System.CodeDom.FieldDirection" />.</summary>
      <param name="dir">One of the enumeration values that indicates the attribute of the field.</param>
    </member>
    <member name="M:System.CodeDom.Compiler.CodeGenerator.OutputExpressionList(System.CodeDom.CodeExpressionCollection)">
      <summary>Generates code for the specified expression list.</summary>
      <param name="expressions">The expressions to generate code for.</param>
    </member>
    <member name="M:System.CodeDom.Compiler.CodeGenerator.OutputExpressionList(System.CodeDom.CodeExpressionCollection,System.Boolean)">
      <summary>Generates code for the specified expression list.</summary>
      <param name="expressions">The expressions to generate code for.</param>
      <param name="newlineBetweenItems">
        <see langword="true" /> to insert a new line after each item; otherwise, <see langword="false" />.</param>
    </member>
    <member name="M:System.CodeDom.Compiler.CodeGenerator.OutputFieldScopeModifier(System.CodeDom.MemberAttributes)">
      <summary>Outputs a field scope modifier that corresponds to the specified attributes.</summary>
      <param name="attributes">One of the enumeration values that specifies the attributes.</param>
    </member>
    <member name="M:System.CodeDom.Compiler.CodeGenerator.OutputIdentifier(System.String)">
      <summary>Outputs the specified identifier.</summary>
      <param name="ident">The identifier to output.</param>
    </member>
    <member name="M:System.CodeDom.Compiler.CodeGenerator.OutputMemberAccessModifier(System.CodeDom.MemberAttributes)">
      <summary>Generates code for the specified member access modifier.</summary>
      <param name="attributes">One of the enumeration values that indicates the member access modifier to generate code for.</param>
    </member>
    <member name="M:System.CodeDom.Compiler.CodeGenerator.OutputMemberScopeModifier(System.CodeDom.MemberAttributes)">
      <summary>Generates code for the specified member scope modifier.</summary>
      <param name="attributes">One of the enumeration values that indicates the member scope modifier to generate code for.</param>
    </member>
    <member name="M:System.CodeDom.Compiler.CodeGenerator.OutputOperator(System.CodeDom.CodeBinaryOperatorType)">
      <summary>Generates code for the specified operator.</summary>
      <param name="op">The operator to generate code for.</param>
    </member>
    <member name="M:System.CodeDom.Compiler.CodeGenerator.OutputParameters(System.CodeDom.CodeParameterDeclarationExpressionCollection)">
      <summary>Generates code for the specified parameters.</summary>
      <param name="parameters">The parameter declaration expressions to generate code for.</param>
    </member>
    <member name="M:System.CodeDom.Compiler.CodeGenerator.OutputType(System.CodeDom.CodeTypeReference)">
      <summary>Generates code for the specified type.</summary>
      <param name="typeRef">The type to generate code for.</param>
    </member>
    <member name="M:System.CodeDom.Compiler.CodeGenerator.OutputTypeAttributes(System.Reflection.TypeAttributes,System.Boolean,System.Boolean)">
      <summary>Generates code for the specified type attributes.</summary>
      <param name="attributes">One of the enumeration values that indicates the type attributes to generate code for.</param>
      <param name="isStruct">
        <see langword="true" /> if the type is a struct; otherwise, <see langword="false" />.</param>
      <param name="isEnum">
        <see langword="true" /> if the type is an enum; otherwise, <see langword="false" />.</param>
    </member>
    <member name="M:System.CodeDom.Compiler.CodeGenerator.OutputTypeNamePair(System.CodeDom.CodeTypeReference,System.String)">
      <summary>Generates code for the specified object type and name pair.</summary>
      <param name="typeRef">The type.</param>
      <param name="name">The name for the object.</param>
    </member>
    <member name="M:System.CodeDom.Compiler.CodeGenerator.QuoteSnippetString(System.String)">
      <summary>Converts the specified string by formatting it with escape codes.</summary>
      <param name="value">The string to convert.</param>
      <returns>The converted string.</returns>
    </member>
    <member name="M:System.CodeDom.Compiler.CodeGenerator.Supports(System.CodeDom.Compiler.GeneratorSupport)">
      <summary>Gets a value indicating whether the specified code generation support is provided.</summary>
      <param name="support">The type of code generation support to test for.</param>
      <returns>
        <see langword="true" /> if the specified code generation support is provided; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.CodeDom.Compiler.CodeGenerator.System#CodeDom#Compiler#ICodeGenerator#CreateEscapedIdentifier(System.String)">
      <summary>Creates an escaped identifier for the specified value.</summary>
      <param name="value">The string to create an escaped identifier for.</param>
      <returns>The escaped identifier for the value.</returns>
    </member>
    <member name="M:System.CodeDom.Compiler.CodeGenerator.System#CodeDom#Compiler#ICodeGenerator#CreateValidIdentifier(System.String)">
      <summary>Creates a valid identifier for the specified value.</summary>
      <param name="value">The string to generate a valid identifier for.</param>
      <returns>A valid identifier for the specified value.</returns>
    </member>
    <member name="M:System.CodeDom.Compiler.CodeGenerator.System#CodeDom#Compiler#ICodeGenerator#GenerateCodeFromCompileUnit(System.CodeDom.CodeCompileUnit,System.IO.TextWriter,System.CodeDom.Compiler.CodeGeneratorOptions)">
      <summary>Generates code for the specified Code Document Object Model (CodeDOM) compilation unit and outputs it to the specified text writer using the specified options.</summary>
      <param name="e">The CodeDOM compilation unit to generate code for.</param>
      <param name="w">The text writer to output code to.</param>
      <param name="o">The options to use for generating code.</param>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="w" /> is not available. <paramref name="w" /> may have been closed before the method call was made.</exception>
    </member>
    <member name="M:System.CodeDom.Compiler.CodeGenerator.System#CodeDom#Compiler#ICodeGenerator#GenerateCodeFromExpression(System.CodeDom.CodeExpression,System.IO.TextWriter,System.CodeDom.Compiler.CodeGeneratorOptions)">
      <summary>Generates code for the specified Code Document Object Model (CodeDOM) expression and outputs it to the specified text writer.</summary>
      <param name="e">The expression to generate code for.</param>
      <param name="w">The text writer to output code to.</param>
      <param name="o">The options to use for generating code.</param>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="w" /> is not available. <paramref name="w" /> may have been closed before the method call was made.</exception>
    </member>
    <member name="M:System.CodeDom.Compiler.CodeGenerator.System#CodeDom#Compiler#ICodeGenerator#GenerateCodeFromNamespace(System.CodeDom.CodeNamespace,System.IO.TextWriter,System.CodeDom.Compiler.CodeGeneratorOptions)">
      <summary>Generates code for the specified Code Document Object Model (CodeDOM) namespace and outputs it to the specified text writer using the specified options.</summary>
      <param name="e">The namespace to generate code for.</param>
      <param name="w">The text writer to output code to.</param>
      <param name="o">The options to use for generating code.</param>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="w" /> is not available. <paramref name="w" /> may have been closed before the method call was made.</exception>
    </member>
    <member name="M:System.CodeDom.Compiler.CodeGenerator.System#CodeDom#Compiler#ICodeGenerator#GenerateCodeFromStatement(System.CodeDom.CodeStatement,System.IO.TextWriter,System.CodeDom.Compiler.CodeGeneratorOptions)">
      <summary>Generates code for the specified Code Document Object Model (CodeDOM) statement and outputs it to the specified text writer using the specified options.</summary>
      <param name="e">The statement that contains the CodeDOM elements to translate.</param>
      <param name="w">The text writer to output code to.</param>
      <param name="o">The options to use for generating code.</param>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="w" /> is not available. <paramref name="w" /> may have been closed before the method call was made.</exception>
    </member>
    <member name="M:System.CodeDom.Compiler.CodeGenerator.System#CodeDom#Compiler#ICodeGenerator#GenerateCodeFromType(System.CodeDom.CodeTypeDeclaration,System.IO.TextWriter,System.CodeDom.Compiler.CodeGeneratorOptions)">
      <summary>Generates code for the specified Code Document Object Model (CodeDOM) type declaration and outputs it to the specified text writer using the specified options.</summary>
      <param name="e">The type to generate code for.</param>
      <param name="w">The text writer to output code to.</param>
      <param name="o">The options to use for generating code.</param>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="w" /> is not available. <paramref name="w" /> may have been closed before the method call was made.</exception>
    </member>
    <member name="M:System.CodeDom.Compiler.CodeGenerator.System#CodeDom#Compiler#ICodeGenerator#GetTypeOutput(System.CodeDom.CodeTypeReference)">
      <summary>Gets the type indicated by the specified <see cref="T:System.CodeDom.CodeTypeReference" />.</summary>
      <param name="type">The type to return.</param>
      <returns>The name of the data type reference.</returns>
    </member>
    <member name="M:System.CodeDom.Compiler.CodeGenerator.System#CodeDom#Compiler#ICodeGenerator#IsValidIdentifier(System.String)">
      <summary>Gets a value that indicates whether the specified value is a valid identifier for the current language.</summary>
      <param name="value">The value to test.</param>
      <returns>
        <see langword="true" /> if the <paramref name="value" /> parameter is a valid identifier; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.CodeDom.Compiler.CodeGenerator.System#CodeDom#Compiler#ICodeGenerator#Supports(System.CodeDom.Compiler.GeneratorSupport)">
      <summary>Gets a value indicating whether the generator provides support for the language features represented by the specified <see cref="T:System.CodeDom.Compiler.GeneratorSupport" /> object.</summary>
      <param name="support">The capabilities to test the generator for.</param>
      <returns>
        <see langword="true" /> if the specified capabilities are supported; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.CodeDom.Compiler.CodeGenerator.System#CodeDom#Compiler#ICodeGenerator#ValidateIdentifier(System.String)">
      <summary>Throws an exception if the specified value is not a valid identifier.</summary>
      <param name="value">The identifier to validate.</param>
    </member>
    <member name="M:System.CodeDom.Compiler.CodeGenerator.ValidateIdentifier(System.String)">
      <summary>Throws an exception if the specified string is not a valid identifier.</summary>
      <param name="value">The identifier to test for validity as an identifier.</param>
      <exception cref="T:System.ArgumentException">The specified identifier is invalid or conflicts with reserved or language keywords.</exception>
    </member>
    <member name="M:System.CodeDom.Compiler.CodeGenerator.ValidateIdentifiers(System.CodeDom.CodeObject)">
      <summary>Attempts to validate each identifier field contained in the specified <see cref="T:System.CodeDom.CodeObject" /> or <see cref="N:System.CodeDom" /> tree.</summary>
      <param name="e">An object to test for invalid identifiers.</param>
      <exception cref="T:System.ArgumentException">The specified <see cref="T:System.CodeDom.CodeObject" /> contains an invalid identifier.</exception>
    </member>
    <member name="P:System.CodeDom.Compiler.CodeGenerator.CurrentClass">
      <summary>Gets the code type declaration for the current class.</summary>
      <returns>The code type declaration for the current class.</returns>
    </member>
    <member name="P:System.CodeDom.Compiler.CodeGenerator.CurrentMember">
      <summary>Gets the current member of the class.</summary>
      <returns>The current member of the class.</returns>
    </member>
    <member name="P:System.CodeDom.Compiler.CodeGenerator.CurrentMemberName">
      <summary>Gets the current member name.</summary>
      <returns>The name of the current member.</returns>
    </member>
    <member name="P:System.CodeDom.Compiler.CodeGenerator.CurrentTypeName">
      <summary>Gets the current class name.</summary>
      <returns>The current class name.</returns>
    </member>
    <member name="P:System.CodeDom.Compiler.CodeGenerator.Indent">
      <summary>Gets or sets the amount of spaces to indent each indentation level.</summary>
      <returns>The number of spaces to indent for each indentation level.</returns>
    </member>
    <member name="P:System.CodeDom.Compiler.CodeGenerator.IsCurrentClass">
      <summary>Gets a value indicating whether the current object being generated is a class.</summary>
      <returns>
        <see langword="true" /> if the current object is a class; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.CodeDom.Compiler.CodeGenerator.IsCurrentDelegate">
      <summary>Gets a value indicating whether the current object being generated is a delegate.</summary>
      <returns>
        <see langword="true" /> if the current object is a delegate; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.CodeDom.Compiler.CodeGenerator.IsCurrentEnum">
      <summary>Gets a value indicating whether the current object being generated is an enumeration.</summary>
      <returns>
        <see langword="true" /> if the current object is an enumeration; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.CodeDom.Compiler.CodeGenerator.IsCurrentInterface">
      <summary>Gets a value indicating whether the current object being generated is an interface.</summary>
      <returns>
        <see langword="true" /> if the current object is an interface; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.CodeDom.Compiler.CodeGenerator.IsCurrentStruct">
      <summary>Gets a value indicating whether the current object being generated is a value type or struct.</summary>
      <returns>
        <see langword="true" /> if the current object is a value type or struct; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.CodeDom.Compiler.CodeGenerator.NullToken">
      <summary>Gets the token that represents <see langword="null" />.</summary>
      <returns>The token that represents <see langword="null" />.</returns>
    </member>
    <member name="P:System.CodeDom.Compiler.CodeGenerator.Options">
      <summary>Gets the options to be used by the code generator.</summary>
      <returns>An object that indicates the options for the code generator to use.</returns>
    </member>
    <member name="P:System.CodeDom.Compiler.CodeGenerator.Output">
      <summary>Gets the text writer to use for output.</summary>
      <returns>The text writer to use for output.</returns>
    </member>
    <member name="T:System.CodeDom.Compiler.CodeGeneratorOptions">
      <summary>Represents a set of options used by a code generator.</summary>
    </member>
    <member name="M:System.CodeDom.Compiler.CodeGeneratorOptions.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.CodeDom.Compiler.CodeGeneratorOptions" /> class.</summary>
    </member>
    <member name="P:System.CodeDom.Compiler.CodeGeneratorOptions.BlankLinesBetweenMembers">
      <summary>Gets or sets a value indicating whether to insert blank lines between members.</summary>
      <returns>
        <see langword="true" /> if blank lines should be inserted; otherwise, <see langword="false" />. By default, the value of this property is <see langword="true" />.</returns>
    </member>
    <member name="P:System.CodeDom.Compiler.CodeGeneratorOptions.BracingStyle">
      <summary>Gets or sets the style to use for bracing.</summary>
      <returns>A string containing the bracing style to use.</returns>
    </member>
    <member name="P:System.CodeDom.Compiler.CodeGeneratorOptions.ElseOnClosing">
      <summary>Gets or sets a value indicating whether to append an <see langword="else" />, <see langword="catch" />, or <see langword="finally" /> block, including brackets, at the closing line of each previous <see langword="if" /> or <see langword="try" /> block.</summary>
      <returns>
        <see langword="true" /> if an else should be appended; otherwise, <see langword="false" />. The default value of this property is <see langword="false" />.</returns>
    </member>
    <member name="P:System.CodeDom.Compiler.CodeGeneratorOptions.IndentString">
      <summary>Gets or sets the string to use for indentations.</summary>
      <returns>A string containing the characters to use for indentations.</returns>
    </member>
    <member name="P:System.CodeDom.Compiler.CodeGeneratorOptions.Item(System.String)">
      <summary>Gets or sets the object at the specified index.</summary>
      <param name="index">The name associated with the object to retrieve.</param>
      <returns>The object associated with the specified name. If no object associated with the specified name exists in the collection, <see langword="null" />.</returns>
    </member>
    <member name="P:System.CodeDom.Compiler.CodeGeneratorOptions.VerbatimOrder">
      <summary>Gets or sets a value indicating whether to generate members in the order in which they occur in member collections.</summary>
      <returns>
        <see langword="true" /> to generate the members in the order in which they occur in the member collection; otherwise, <see langword="false" />. The default value of this property is <see langword="false" />.</returns>
    </member>
    <member name="T:System.CodeDom.Compiler.CodeParser">
      <summary>Provides an empty implementation of the <see cref="T:System.CodeDom.Compiler.ICodeParser" /> interface.</summary>
    </member>
    <member name="M:System.CodeDom.Compiler.CodeParser.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.CodeDom.Compiler.CodeParser" /> class.</summary>
    </member>
    <member name="M:System.CodeDom.Compiler.CodeParser.Parse(System.IO.TextReader)">
      <summary>Compiles the specified text stream into a <see cref="T:System.CodeDom.CodeCompileUnit" />.</summary>
      <param name="codeStream">A <see cref="T:System.IO.TextReader" /> that is used to read the code to be parsed.</param>
      <returns>A <see cref="T:System.CodeDom.CodeCompileUnit" /> containing the code model produced from parsing the code.</returns>
    </member>
    <member name="T:System.CodeDom.Compiler.CompilerError">
      <summary>Represents a compiler error or warning.</summary>
    </member>
    <member name="M:System.CodeDom.Compiler.CompilerError.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.CodeDom.Compiler.CompilerError" /> class.</summary>
    </member>
    <member name="M:System.CodeDom.Compiler.CompilerError.#ctor(System.String,System.Int32,System.Int32,System.String,System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.CodeDom.Compiler.CompilerError" /> class using the specified file name, line, column, error number, and error text.</summary>
      <param name="fileName">The file name of the file that the compiler was compiling when it encountered the error.</param>
      <param name="line">The line of the source of the error.</param>
      <param name="column">The column of the source of the error.</param>
      <param name="errorNumber">The error number of the error.</param>
      <param name="errorText">The error message text.</param>
    </member>
    <member name="M:System.CodeDom.Compiler.CompilerError.ToString">
      <summary>Provides an implementation of Object's <see cref="M:System.Object.ToString" /> method.</summary>
      <returns>A string representation of the compiler error.</returns>
    </member>
    <member name="P:System.CodeDom.Compiler.CompilerError.Column">
      <summary>Gets or sets the column number where the source of the error occurs.</summary>
      <returns>The column number of the source file where the compiler encountered the error.</returns>
    </member>
    <member name="P:System.CodeDom.Compiler.CompilerError.ErrorNumber">
      <summary>Gets or sets the error number.</summary>
      <returns>The error number as a string.</returns>
    </member>
    <member name="P:System.CodeDom.Compiler.CompilerError.ErrorText">
      <summary>Gets or sets the text of the error message.</summary>
      <returns>The text of the error message.</returns>
    </member>
    <member name="P:System.CodeDom.Compiler.CompilerError.FileName">
      <summary>Gets or sets the file name of the source file that contains the code which caused the error.</summary>
      <returns>The file name of the source file that contains the code which caused the error.</returns>
    </member>
    <member name="P:System.CodeDom.Compiler.CompilerError.IsWarning">
      <summary>Gets or sets a value that indicates whether the error is a warning.</summary>
      <returns>
        <see langword="true" /> if the error is a warning; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.CodeDom.Compiler.CompilerError.Line">
      <summary>Gets or sets the line number where the source of the error occurs.</summary>
      <returns>The line number of the source file where the compiler encountered the error.</returns>
    </member>
    <member name="T:System.CodeDom.Compiler.CompilerErrorCollection">
      <summary>Represents a collection of <see cref="T:System.CodeDom.Compiler.CompilerError" /> objects.</summary>
    </member>
    <member name="M:System.CodeDom.Compiler.CompilerErrorCollection.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.CodeDom.Compiler.CompilerErrorCollection" /> class.</summary>
    </member>
    <member name="M:System.CodeDom.Compiler.CompilerErrorCollection.#ctor(System.CodeDom.Compiler.CompilerError[])">
      <summary>Initializes a new instance of <see cref="T:System.CodeDom.Compiler.CompilerErrorCollection" /> that contains the specified array of <see cref="T:System.CodeDom.Compiler.CompilerError" /> objects.</summary>
      <param name="value">An array of <see cref="T:System.CodeDom.Compiler.CompilerError" /> objects to initialize the collection with.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.CodeDom.Compiler.CompilerErrorCollection.#ctor(System.CodeDom.Compiler.CompilerErrorCollection)">
      <summary>Initializes a new instance of the <see cref="T:System.CodeDom.Compiler.CompilerErrorCollection" /> class that contains the contents of the specified <see cref="T:System.CodeDom.Compiler.CompilerErrorCollection" />.</summary>
      <param name="value">A <see cref="T:System.CodeDom.Compiler.CompilerErrorCollection" /> object with which to initialize the collection.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.CodeDom.Compiler.CompilerErrorCollection.Add(System.CodeDom.Compiler.CompilerError)">
      <summary>Adds the specified <see cref="T:System.CodeDom.Compiler.CompilerError" /> object to the error collection.</summary>
      <param name="value">The <see cref="T:System.CodeDom.Compiler.CompilerError" /> object to add.</param>
      <returns>The index at which the new element was inserted.</returns>
    </member>
    <member name="M:System.CodeDom.Compiler.CompilerErrorCollection.AddRange(System.CodeDom.Compiler.CompilerError[])">
      <summary>Copies the elements of an array to the end of the error collection.</summary>
      <param name="value">An array of type <see cref="T:System.CodeDom.Compiler.CompilerError" /> that contains the objects to add to the collection.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.CodeDom.Compiler.CompilerErrorCollection.AddRange(System.CodeDom.Compiler.CompilerErrorCollection)">
      <summary>Adds the contents of the specified compiler error collection to the end of the error collection.</summary>
      <param name="value">A <see cref="T:System.CodeDom.Compiler.CompilerErrorCollection" /> object that contains the objects to add to the collection.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.CodeDom.Compiler.CompilerErrorCollection.Contains(System.CodeDom.Compiler.CompilerError)">
      <summary>Gets a value that indicates whether the collection contains the specified <see cref="T:System.CodeDom.Compiler.CompilerError" /> object.</summary>
      <param name="value">The <see cref="T:System.CodeDom.Compiler.CompilerError" /> to locate.</param>
      <returns>
        <see langword="true" /> if the <see cref="T:System.CodeDom.Compiler.CompilerError" /> is contained in the collection; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.CodeDom.Compiler.CompilerErrorCollection.CopyTo(System.CodeDom.Compiler.CompilerError[],System.Int32)">
      <summary>Copies the collection values to a one-dimensional <see cref="T:System.Array" /> instance at the specified index.</summary>
      <param name="array">The one-dimensional <see cref="T:System.Array" /> that is the destination of the values copied from <see cref="T:System.CodeDom.Compiler.CompilerErrorCollection" />.</param>
      <param name="index">The index in the array at which to start copying.</param>
      <exception cref="T:System.ArgumentException">The array indicated by the <paramref name="array" /> parameter is multidimensional.  
  
 -or-  
  
 The number of elements in the <see cref="T:System.CodeDom.Compiler.CompilerErrorCollection" /> is greater than the available space between the index value of the <paramref name="arrayIndex" /> parameter in the array indicated by the <paramref name="array" /> parameter and the end of the array indicated by the <paramref name="array" /> parameter.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="array" /> parameter is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="index" /> parameter is less than the lowbound of the array indicated by the <paramref name="array" /> parameter.</exception>
    </member>
    <member name="M:System.CodeDom.Compiler.CompilerErrorCollection.IndexOf(System.CodeDom.Compiler.CompilerError)">
      <summary>Gets the index of the specified <see cref="T:System.CodeDom.Compiler.CompilerError" /> object in the collection, if it exists in the collection.</summary>
      <param name="value">The <see cref="T:System.CodeDom.Compiler.CompilerError" /> to locate.</param>
      <returns>The index of the specified <see cref="T:System.CodeDom.Compiler.CompilerError" /> in the <see cref="T:System.CodeDom.Compiler.CompilerErrorCollection" />, if found; otherwise, -1.</returns>
    </member>
    <member name="M:System.CodeDom.Compiler.CompilerErrorCollection.Insert(System.Int32,System.CodeDom.Compiler.CompilerError)">
      <summary>Inserts the specified <see cref="T:System.CodeDom.Compiler.CompilerError" /> into the collection at the specified index.</summary>
      <param name="index">The zero-based index where the compiler error should be inserted.</param>
      <param name="value">The <see cref="T:System.CodeDom.Compiler.CompilerError" /> to insert.</param>
    </member>
    <member name="M:System.CodeDom.Compiler.CompilerErrorCollection.Remove(System.CodeDom.Compiler.CompilerError)">
      <summary>Removes a specific <see cref="T:System.CodeDom.Compiler.CompilerError" /> from the collection.</summary>
      <param name="value">The <see cref="T:System.CodeDom.Compiler.CompilerError" /> to remove from the <see cref="T:System.CodeDom.Compiler.CompilerErrorCollection" />.</param>
      <exception cref="T:System.ArgumentException">The specified object is not found in the collection.</exception>
    </member>
    <member name="P:System.CodeDom.Compiler.CompilerErrorCollection.HasErrors">
      <summary>Gets a value that indicates whether the collection contains errors.</summary>
      <returns>
        <see langword="true" /> if the collection contains errors; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.CodeDom.Compiler.CompilerErrorCollection.HasWarnings">
      <summary>Gets a value that indicates whether the collection contains warnings.</summary>
      <returns>
        <see langword="true" /> if the collection contains warnings; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.CodeDom.Compiler.CompilerErrorCollection.Item(System.Int32)">
      <summary>Gets or sets the <see cref="T:System.CodeDom.Compiler.CompilerError" /> at the specified index.</summary>
      <param name="index">The zero-based index of the entry to locate in the collection.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">The index value indicated by the <paramref name="index" /> parameter is outside the valid range of indexes for the collection.</exception>
      <returns>A <see cref="T:System.CodeDom.Compiler.CompilerError" /> at each valid index.</returns>
    </member>
    <member name="T:System.CodeDom.Compiler.CompilerInfo">
      <summary>Represents the configuration settings of a language provider. This class cannot be inherited.</summary>
    </member>
    <member name="M:System.CodeDom.Compiler.CompilerInfo.CreateDefaultCompilerParameters">
      <summary>Gets the configured compiler settings for the language provider implementation.</summary>
      <returns>A read-only <see cref="T:System.CodeDom.Compiler.CompilerParameters" /> instance that contains the compiler options and settings configured for the language provider.</returns>
    </member>
    <member name="M:System.CodeDom.Compiler.CompilerInfo.CreateProvider">
      <summary>Returns a <see cref="T:System.CodeDom.Compiler.CodeDomProvider" /> instance for the current language provider settings.</summary>
      <returns>A CodeDOM provider associated with the language provider configuration.</returns>
    </member>
    <member name="M:System.CodeDom.Compiler.CompilerInfo.CreateProvider(System.Collections.Generic.IDictionary{System.String,System.String})">
      <summary>Returns a <see cref="T:System.CodeDom.Compiler.CodeDomProvider" /> instance for the current language provider settings and specified options.</summary>
      <param name="providerOptions">A collection of provider options from the configuration file.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="providerOptions" /> is <see langword="null" />.</exception>
      <exception cref="T:System.InvalidOperationException">The provider does not support options.</exception>
      <returns>A CodeDOM provider associated with the language provider configuration and specified options.</returns>
    </member>
    <member name="M:System.CodeDom.Compiler.CompilerInfo.Equals(System.Object)">
      <summary>Determines whether the specified object represents the same language provider and compiler settings as the current <see cref="T:System.CodeDom.Compiler.CompilerInfo" />.</summary>
      <param name="o">The object to compare with the current <see cref="T:System.CodeDom.Compiler.CompilerInfo" />.</param>
      <returns>
        <see langword="true" /> if <paramref name="o" /> is a <see cref="T:System.CodeDom.Compiler.CompilerInfo" /> object and its value is the same as this instance; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.CodeDom.Compiler.CompilerInfo.GetExtensions">
      <summary>Returns the file name extensions supported by the language provider.</summary>
      <returns>An array of file name extensions supported by the language provider.</returns>
    </member>
    <member name="M:System.CodeDom.Compiler.CompilerInfo.GetHashCode">
      <summary>Returns the hash code for the current instance.</summary>
      <returns>A 32-bit signed integer hash code for the current <see cref="T:System.CodeDom.Compiler.CompilerInfo" /> instance, suitable for use in hashing algorithms and data structures such as a hash table.</returns>
    </member>
    <member name="M:System.CodeDom.Compiler.CompilerInfo.GetLanguages">
      <summary>Gets the language names supported by the language provider.</summary>
      <returns>An array of language names supported by the language provider.</returns>
    </member>
    <member name="P:System.CodeDom.Compiler.CompilerInfo.CodeDomProviderType">
      <summary>Gets the type of the configured <see cref="T:System.CodeDom.Compiler.CodeDomProvider" /> implementation.</summary>
      <exception cref="T:System.Configuration.ConfigurationException">The language provider is not configured on this computer.</exception>
      <exception cref="T:System.Configuration.ConfigurationErrorsException">Cannot locate the type because it is a <see langword="null" /> or empty string.  
  
 -or-  
  
 Cannot locate the type because the name for the <see cref="T:System.CodeDom.Compiler.CodeDomProvider" /> cannot be found in the configuration file.</exception>
      <returns>A read-only <see cref="T:System.Type" /> instance that represents the configured language provider type.</returns>
    </member>
    <member name="P:System.CodeDom.Compiler.CompilerInfo.IsCodeDomProviderTypeValid">
      <summary>Returns a value indicating whether the language provider implementation is configured on the computer.</summary>
      <returns>
        <see langword="true" /> if the language provider implementation type is configured on the computer; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="T:System.CodeDom.Compiler.CompilerParameters">
      <summary>Represents the parameters used to invoke a compiler.</summary>
    </member>
    <member name="M:System.CodeDom.Compiler.CompilerParameters.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.CodeDom.Compiler.CompilerParameters" /> class.</summary>
    </member>
    <member name="M:System.CodeDom.Compiler.CompilerParameters.#ctor(System.String[])">
      <summary>Initializes a new instance of the <see cref="T:System.CodeDom.Compiler.CompilerParameters" /> class using the specified assembly names.</summary>
      <param name="assemblyNames">The names of the assemblies to reference.</param>
    </member>
    <member name="M:System.CodeDom.Compiler.CompilerParameters.#ctor(System.String[],System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.CodeDom.Compiler.CompilerParameters" /> class using the specified assembly names and output file name.</summary>
      <param name="assemblyNames">The names of the assemblies to reference.</param>
      <param name="outputName">The output file name.</param>
    </member>
    <member name="M:System.CodeDom.Compiler.CompilerParameters.#ctor(System.String[],System.String,System.Boolean)">
      <summary>Initializes a new instance of the <see cref="T:System.CodeDom.Compiler.CompilerParameters" /> class using the specified assembly names, output name, and a value indicating whether to include debug information.</summary>
      <param name="assemblyNames">The names of the assemblies to reference.</param>
      <param name="outputName">The output file name.</param>
      <param name="includeDebugInformation">
        <see langword="true" /> to include debug information; <see langword="false" /> to exclude debug information.</param>
    </member>
    <member name="P:System.CodeDom.Compiler.CompilerParameters.CompilerOptions">
      <summary>Gets or sets optional command-line arguments to use when invoking the compiler.</summary>
      <returns>Any additional command-line arguments for the compiler.</returns>
    </member>
    <member name="P:System.CodeDom.Compiler.CompilerParameters.CoreAssemblyFileName">
      <summary>Gets or sets the name of the core or standard assembly that contains basic types such as <see cref="T:System.Object" />, <see cref="T:System.String" />, or <see cref="T:System.Int32" />.</summary>
      <returns>The name of the core assembly that contains basic types.</returns>
    </member>
    <member name="P:System.CodeDom.Compiler.CompilerParameters.EmbeddedResources">
      <summary>Gets the .NET resource files to include when compiling the assembly output.</summary>
      <returns>A collection that contains the file paths of .NET resources to include in the generated assembly.</returns>
    </member>
    <member name="P:System.CodeDom.Compiler.CompilerParameters.GenerateExecutable">
      <summary>Gets or sets a value indicating whether to generate an executable.</summary>
      <returns>
        <see langword="true" /> if an executable should be generated; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.CodeDom.Compiler.CompilerParameters.GenerateInMemory">
      <summary>Gets or sets a value indicating whether to generate the output in memory.</summary>
      <returns>
        <see langword="true" /> if the compiler should generate the output in memory; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.CodeDom.Compiler.CompilerParameters.IncludeDebugInformation">
      <summary>Gets or sets a value indicating whether to include debug information in the compiled executable.</summary>
      <returns>
        <see langword="true" /> if debug information should be generated; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.CodeDom.Compiler.CompilerParameters.LinkedResources">
      <summary>Gets the .NET resource files that are referenced in the current source.</summary>
      <returns>A collection that contains the file paths of .NET resources that are referenced by the source.</returns>
    </member>
    <member name="P:System.CodeDom.Compiler.CompilerParameters.MainClass">
      <summary>Gets or sets the name of the main class.</summary>
      <returns>The name of the main class.</returns>
    </member>
    <member name="P:System.CodeDom.Compiler.CompilerParameters.OutputAssembly">
      <summary>Gets or sets the name of the output assembly.</summary>
      <returns>The name of the output assembly.</returns>
    </member>
    <member name="P:System.CodeDom.Compiler.CompilerParameters.ReferencedAssemblies">
      <summary>Gets the assemblies referenced by the current project.</summary>
      <returns>A collection that contains the assembly names that are referenced by the source to compile.</returns>
    </member>
    <member name="P:System.CodeDom.Compiler.CompilerParameters.TempFiles">
      <summary>Gets or sets the collection that contains the temporary files.</summary>
      <returns>A collection that contains the temporary files.</returns>
    </member>
    <member name="P:System.CodeDom.Compiler.CompilerParameters.TreatWarningsAsErrors">
      <summary>Gets or sets a value indicating whether to treat warnings as errors.</summary>
      <returns>
        <see langword="true" /> if warnings should be treated as errors; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.CodeDom.Compiler.CompilerParameters.UserToken">
      <summary>Gets or sets the user token to use when creating the compiler process.</summary>
      <returns>The user token to use.</returns>
    </member>
    <member name="P:System.CodeDom.Compiler.CompilerParameters.WarningLevel">
      <summary>Gets or sets the warning level at which the compiler aborts compilation.</summary>
      <returns>The warning level at which the compiler aborts compilation.</returns>
    </member>
    <member name="P:System.CodeDom.Compiler.CompilerParameters.Win32Resource">
      <summary>Gets or sets the file name of a Win32 resource file to link into the compiled assembly.</summary>
      <returns>A Win32 resource file that will be linked into the compiled assembly.</returns>
    </member>
    <member name="T:System.CodeDom.Compiler.CompilerResults">
      <summary>Represents the results of compilation that are returned from a compiler.</summary>
    </member>
    <member name="M:System.CodeDom.Compiler.CompilerResults.#ctor(System.CodeDom.Compiler.TempFileCollection)">
      <summary>Initializes a new instance of the <see cref="T:System.CodeDom.Compiler.CompilerResults" /> class that uses the specified temporary files.</summary>
      <param name="tempFiles">A <see cref="T:System.CodeDom.Compiler.TempFileCollection" /> with which to manage and store references to intermediate files generated during compilation.</param>
    </member>
    <member name="P:System.CodeDom.Compiler.CompilerResults.CompiledAssembly">
      <summary>Gets or sets the compiled assembly.</summary>
      <returns>An <see cref="T:System.Reflection.Assembly" /> that indicates the compiled assembly.</returns>
    </member>
    <member name="P:System.CodeDom.Compiler.CompilerResults.Errors">
      <summary>Gets the collection of compiler errors and warnings.</summary>
      <returns>A <see cref="T:System.CodeDom.Compiler.CompilerErrorCollection" /> that indicates the errors and warnings resulting from compilation, if any.</returns>
    </member>
    <member name="P:System.CodeDom.Compiler.CompilerResults.NativeCompilerReturnValue">
      <summary>Gets or sets the compiler's return value.</summary>
      <returns>The compiler's return value.</returns>
    </member>
    <member name="P:System.CodeDom.Compiler.CompilerResults.Output">
      <summary>Gets the compiler output messages.</summary>
      <returns>A <see cref="T:System.Collections.Specialized.StringCollection" /> that contains the output messages.</returns>
    </member>
    <member name="P:System.CodeDom.Compiler.CompilerResults.PathToAssembly">
      <summary>Gets or sets the path of the compiled assembly.</summary>
      <returns>The path of the assembly, or <see langword="null" /> if the assembly was generated in memory.</returns>
    </member>
    <member name="P:System.CodeDom.Compiler.CompilerResults.TempFiles">
      <summary>Gets or sets the temporary file collection to use.</summary>
      <returns>A <see cref="T:System.CodeDom.Compiler.TempFileCollection" /> with which to manage and store references to intermediate files generated during compilation.</returns>
    </member>
    <member name="T:System.CodeDom.Compiler.Executor">
      <summary>Provides command execution functions for invoking compilers. This class cannot be inherited.</summary>
    </member>
    <member name="M:System.CodeDom.Compiler.Executor.ExecWait(System.String,System.CodeDom.Compiler.TempFileCollection)">
      <summary>Executes the command using the specified temporary files and waits for the call to return.</summary>
      <param name="cmd">The command to execute.</param>
      <param name="tempFiles">A <see cref="T:System.CodeDom.Compiler.TempFileCollection" /> with which to manage and store references to intermediate files generated during compilation.</param>
    </member>
    <member name="M:System.CodeDom.Compiler.Executor.ExecWaitWithCapture(System.IntPtr,System.String,System.CodeDom.Compiler.TempFileCollection,System.String@,System.String@)">
      <summary>Executes the specified command using the specified user token and temporary files, and waits for the call to return, storing output and error information from the compiler in the specified strings.</summary>
      <param name="userToken">The token to start the compiler process with.</param>
      <param name="cmd">The command to execute.</param>
      <param name="tempFiles">A <see cref="T:System.CodeDom.Compiler.TempFileCollection" /> with which to manage and store references to intermediate files generated during compilation.</param>
      <param name="outputName">A reference to a string that will store the compiler's message output.</param>
      <param name="errorName">A reference to a string that will store the name of the error or errors encountered.</param>
      <returns>The return value from the compiler.</returns>
    </member>
    <member name="M:System.CodeDom.Compiler.Executor.ExecWaitWithCapture(System.IntPtr,System.String,System.String,System.CodeDom.Compiler.TempFileCollection,System.String@,System.String@)">
      <summary>Executes the specified command using the specified user token, current directory, and temporary files; then waits for the call to return, storing output and error information from the compiler in the specified strings.</summary>
      <param name="userToken">The token to start the compiler process with.</param>
      <param name="cmd">The command to execute.</param>
      <param name="currentDir">The directory to start the process in.</param>
      <param name="tempFiles">A <see cref="T:System.CodeDom.Compiler.TempFileCollection" /> with which to manage and store references to intermediate files generated during compilation.</param>
      <param name="outputName">A reference to a string that will store the compiler's message output.</param>
      <param name="errorName">A reference to a string that will store the name of the error or errors encountered.</param>
      <returns>The return value from the compiler.</returns>
    </member>
    <member name="M:System.CodeDom.Compiler.Executor.ExecWaitWithCapture(System.String,System.CodeDom.Compiler.TempFileCollection,System.String@,System.String@)">
      <summary>Executes the specified command using the specified temporary files and waits for the call to return, storing output and error information from the compiler in the specified strings.</summary>
      <param name="cmd">The command to execute.</param>
      <param name="tempFiles">A <see cref="T:System.CodeDom.Compiler.TempFileCollection" /> with which to manage and store references to intermediate files generated during compilation.</param>
      <param name="outputName">A reference to a string that will store the compiler's message output.</param>
      <param name="errorName">A reference to a string that will store the name of the error or errors encountered.</param>
      <returns>The return value from the compiler.</returns>
    </member>
    <member name="M:System.CodeDom.Compiler.Executor.ExecWaitWithCapture(System.String,System.String,System.CodeDom.Compiler.TempFileCollection,System.String@,System.String@)">
      <summary>Executes the specified command using the specified current directory and temporary files, and waits for the call to return, storing output and error information from the compiler in the specified strings.</summary>
      <param name="cmd">The command to execute.</param>
      <param name="currentDir">The current directory.</param>
      <param name="tempFiles">A <see cref="T:System.CodeDom.Compiler.TempFileCollection" /> with which to manage and store references to intermediate files generated during compilation.</param>
      <param name="outputName">A reference to a string that will store the compiler's message output.</param>
      <param name="errorName">A reference to a string that will store the name of the error or errors encountered.</param>
      <returns>The return value from the compiler.</returns>
    </member>
    <member name="T:System.CodeDom.Compiler.GeneratorSupport">
      <summary>Defines identifiers used to determine whether a code generator supports certain types of code elements.</summary>
    </member>
    <member name="F:System.CodeDom.Compiler.GeneratorSupport.ArraysOfArrays">
      <summary>Indicates the generator supports arrays of arrays.</summary>
    </member>
    <member name="F:System.CodeDom.Compiler.GeneratorSupport.AssemblyAttributes">
      <summary>Indicates the generator supports assembly attributes.</summary>
    </member>
    <member name="F:System.CodeDom.Compiler.GeneratorSupport.ChainedConstructorArguments">
      <summary>Indicates the generator supports chained constructor arguments.</summary>
    </member>
    <member name="F:System.CodeDom.Compiler.GeneratorSupport.ComplexExpressions">
      <summary>Indicates the generator supports complex expressions.</summary>
    </member>
    <member name="F:System.CodeDom.Compiler.GeneratorSupport.DeclareDelegates">
      <summary>Indicates the generator supports delegate declarations.</summary>
    </member>
    <member name="F:System.CodeDom.Compiler.GeneratorSupport.DeclareEnums">
      <summary>Indicates the generator supports enumeration declarations.</summary>
    </member>
    <member name="F:System.CodeDom.Compiler.GeneratorSupport.DeclareEvents">
      <summary>Indicates the generator supports event declarations.</summary>
    </member>
    <member name="F:System.CodeDom.Compiler.GeneratorSupport.DeclareIndexerProperties">
      <summary>Indicates the generator supports the declaration of indexer properties.</summary>
    </member>
    <member name="F:System.CodeDom.Compiler.GeneratorSupport.DeclareInterfaces">
      <summary>Indicates the generator supports interface declarations.</summary>
    </member>
    <member name="F:System.CodeDom.Compiler.GeneratorSupport.DeclareValueTypes">
      <summary>Indicates the generator supports value type declarations.</summary>
    </member>
    <member name="F:System.CodeDom.Compiler.GeneratorSupport.EntryPointMethod">
      <summary>Indicates the generator supports a program entry point method designation. This is used when building executables.</summary>
    </member>
    <member name="F:System.CodeDom.Compiler.GeneratorSupport.GenericTypeDeclaration">
      <summary>Indicates the generator supports generic type declarations.</summary>
    </member>
    <member name="F:System.CodeDom.Compiler.GeneratorSupport.GenericTypeReference">
      <summary>Indicates the generator supports generic type references.</summary>
    </member>
    <member name="F:System.CodeDom.Compiler.GeneratorSupport.GotoStatements">
      <summary>Indicates the generator supports goto statements.</summary>
    </member>
    <member name="F:System.CodeDom.Compiler.GeneratorSupport.MultidimensionalArrays">
      <summary>Indicates the generator supports referencing multidimensional arrays. Currently, the CodeDom cannot be used to instantiate multidimensional arrays.</summary>
    </member>
    <member name="F:System.CodeDom.Compiler.GeneratorSupport.MultipleInterfaceMembers">
      <summary>Indicates the generator supports the declaration of members that implement multiple interfaces.</summary>
    </member>
    <member name="F:System.CodeDom.Compiler.GeneratorSupport.NestedTypes">
      <summary>Indicates the generator supports the declaration of nested types.</summary>
    </member>
    <member name="F:System.CodeDom.Compiler.GeneratorSupport.ParameterAttributes">
      <summary>Indicates the generator supports parameter attributes.</summary>
    </member>
    <member name="F:System.CodeDom.Compiler.GeneratorSupport.PartialTypes">
      <summary>Indicates the generator supports partial type declarations.</summary>
    </member>
    <member name="F:System.CodeDom.Compiler.GeneratorSupport.PublicStaticMembers">
      <summary>Indicates the generator supports public static members.</summary>
    </member>
    <member name="F:System.CodeDom.Compiler.GeneratorSupport.ReferenceParameters">
      <summary>Indicates the generator supports reference and out parameters.</summary>
    </member>
    <member name="F:System.CodeDom.Compiler.GeneratorSupport.Resources">
      <summary>Indicates the generator supports compilation with .NET resources. These can be default resources compiled directly into an assembly, or resources referenced in a satellite assembly.</summary>
    </member>
    <member name="F:System.CodeDom.Compiler.GeneratorSupport.ReturnTypeAttributes">
      <summary>Indicates the generator supports return type attribute declarations.</summary>
    </member>
    <member name="F:System.CodeDom.Compiler.GeneratorSupport.StaticConstructors">
      <summary>Indicates the generator supports static constructors.</summary>
    </member>
    <member name="F:System.CodeDom.Compiler.GeneratorSupport.TryCatchStatements">
      <summary>Indicates the generator supports <c>try-catch</c> statements.</summary>
    </member>
    <member name="F:System.CodeDom.Compiler.GeneratorSupport.Win32Resources">
      <summary>Indicates the generator supports compilation with Win32 resources.</summary>
    </member>
    <member name="T:System.CodeDom.Compiler.ICodeCompiler">
      <summary>Defines an interface for invoking compilation of source code or a CodeDOM tree using a specific compiler.</summary>
    </member>
    <member name="M:System.CodeDom.Compiler.ICodeCompiler.CompileAssemblyFromDom(System.CodeDom.Compiler.CompilerParameters,System.CodeDom.CodeCompileUnit)">
      <summary>Compiles an assembly from the <see cref="N:System.CodeDom" /> tree contained in the specified <see cref="T:System.CodeDom.CodeCompileUnit" />, using the specified compiler settings.</summary>
      <param name="options">A <see cref="T:System.CodeDom.Compiler.CompilerParameters" /> object that indicates the settings for compilation.</param>
      <param name="compilationUnit">A <see cref="T:System.CodeDom.CodeCompileUnit" /> that indicates the code to compile.</param>
      <returns>A <see cref="T:System.CodeDom.Compiler.CompilerResults" /> object that indicates the results of compilation.</returns>
    </member>
    <member name="M:System.CodeDom.Compiler.ICodeCompiler.CompileAssemblyFromDomBatch(System.CodeDom.Compiler.CompilerParameters,System.CodeDom.CodeCompileUnit[])">
      <summary>Compiles an assembly based on the <see cref="N:System.CodeDom" /> trees contained in the specified array of <see cref="T:System.CodeDom.CodeCompileUnit" /> objects, using the specified compiler settings.</summary>
      <param name="options">A <see cref="T:System.CodeDom.Compiler.CompilerParameters" /> object that indicates the settings for compilation.</param>
      <param name="compilationUnits">An array of type <see cref="T:System.CodeDom.CodeCompileUnit" /> that indicates the code to compile.</param>
      <returns>A <see cref="T:System.CodeDom.Compiler.CompilerResults" /> object that indicates the results of compilation.</returns>
    </member>
    <member name="M:System.CodeDom.Compiler.ICodeCompiler.CompileAssemblyFromFile(System.CodeDom.Compiler.CompilerParameters,System.String)">
      <summary>Compiles an assembly from the source code contained within the specified file, using the specified compiler settings.</summary>
      <param name="options">A <see cref="T:System.CodeDom.Compiler.CompilerParameters" /> object that indicates the settings for compilation.</param>
      <param name="fileName">The file name of the file that contains the source code to compile.</param>
      <returns>A <see cref="T:System.CodeDom.Compiler.CompilerResults" /> object that indicates the results of compilation.</returns>
    </member>
    <member name="M:System.CodeDom.Compiler.ICodeCompiler.CompileAssemblyFromFileBatch(System.CodeDom.Compiler.CompilerParameters,System.String[])">
      <summary>Compiles an assembly from the source code contained within the specified files, using the specified compiler settings.</summary>
      <param name="options">A <see cref="T:System.CodeDom.Compiler.CompilerParameters" /> object that indicates the settings for compilation.</param>
      <param name="fileNames">The file names of the files to compile.</param>
      <returns>A <see cref="T:System.CodeDom.Compiler.CompilerResults" /> object that indicates the results of compilation.</returns>
    </member>
    <member name="M:System.CodeDom.Compiler.ICodeCompiler.CompileAssemblyFromSource(System.CodeDom.Compiler.CompilerParameters,System.String)">
      <summary>Compiles an assembly from the specified string containing source code, using the specified compiler settings.</summary>
      <param name="options">A <see cref="T:System.CodeDom.Compiler.CompilerParameters" /> object that indicates the settings for compilation.</param>
      <param name="source">The source code to compile.</param>
      <returns>A <see cref="T:System.CodeDom.Compiler.CompilerResults" /> object that indicates the results of compilation.</returns>
    </member>
    <member name="M:System.CodeDom.Compiler.ICodeCompiler.CompileAssemblyFromSourceBatch(System.CodeDom.Compiler.CompilerParameters,System.String[])">
      <summary>Compiles an assembly from the specified array of strings containing source code, using the specified compiler settings.</summary>
      <param name="options">A <see cref="T:System.CodeDom.Compiler.CompilerParameters" /> object that indicates the settings for compilation.</param>
      <param name="sources">The source code strings to compile.</param>
      <returns>A <see cref="T:System.CodeDom.Compiler.CompilerResults" /> object that indicates the results of compilation.</returns>
    </member>
    <member name="T:System.CodeDom.Compiler.ICodeGenerator">
      <summary>Defines an interface for generating code.</summary>
    </member>
    <member name="M:System.CodeDom.Compiler.ICodeGenerator.CreateEscapedIdentifier(System.String)">
      <summary>Creates an escaped identifier for the specified value.</summary>
      <param name="value">The string to create an escaped identifier for.</param>
      <returns>The escaped identifier for the value.</returns>
    </member>
    <member name="M:System.CodeDom.Compiler.ICodeGenerator.CreateValidIdentifier(System.String)">
      <summary>Creates a valid identifier for the specified value.</summary>
      <param name="value">The string to generate a valid identifier for.</param>
      <returns>A valid identifier for the specified value.</returns>
    </member>
    <member name="M:System.CodeDom.Compiler.ICodeGenerator.GenerateCodeFromCompileUnit(System.CodeDom.CodeCompileUnit,System.IO.TextWriter,System.CodeDom.Compiler.CodeGeneratorOptions)">
      <summary>Generates code for the specified Code Document Object Model (CodeDOM) compilation unit and outputs it to the specified text writer using the specified options.</summary>
      <param name="e">A <see cref="T:System.CodeDom.CodeCompileUnit" /> to generate code for.</param>
      <param name="w">The <see cref="T:System.IO.TextWriter" /> to output code to.</param>
      <param name="o">A <see cref="T:System.CodeDom.Compiler.CodeGeneratorOptions" /> that indicates the options to use for generating code.</param>
    </member>
    <member name="M:System.CodeDom.Compiler.ICodeGenerator.GenerateCodeFromExpression(System.CodeDom.CodeExpression,System.IO.TextWriter,System.CodeDom.Compiler.CodeGeneratorOptions)">
      <summary>Generates code for the specified Code Document Object Model (CodeDOM) expression and outputs it to the specified text writer.</summary>
      <param name="e">A <see cref="T:System.CodeDom.CodeExpression" /> that indicates the expression to generate code for.</param>
      <param name="w">The <see cref="T:System.IO.TextWriter" /> to output code to.</param>
      <param name="o">A <see cref="T:System.CodeDom.Compiler.CodeGeneratorOptions" /> that indicates the options to use for generating code.</param>
    </member>
    <member name="M:System.CodeDom.Compiler.ICodeGenerator.GenerateCodeFromNamespace(System.CodeDom.CodeNamespace,System.IO.TextWriter,System.CodeDom.Compiler.CodeGeneratorOptions)">
      <summary>Generates code for the specified Code Document Object Model (CodeDOM) namespace and outputs it to the specified text writer using the specified options.</summary>
      <param name="e">A <see cref="T:System.CodeDom.CodeNamespace" /> that indicates the namespace to generate code for.</param>
      <param name="w">The <see cref="T:System.IO.TextWriter" /> to output code to.</param>
      <param name="o">A <see cref="T:System.CodeDom.Compiler.CodeGeneratorOptions" /> that indicates the options to use for generating code.</param>
    </member>
    <member name="M:System.CodeDom.Compiler.ICodeGenerator.GenerateCodeFromStatement(System.CodeDom.CodeStatement,System.IO.TextWriter,System.CodeDom.Compiler.CodeGeneratorOptions)">
      <summary>Generates code for the specified Code Document Object Model (CodeDOM) statement and outputs it to the specified text writer using the specified options.</summary>
      <param name="e">A <see cref="T:System.CodeDom.CodeStatement" /> containing the CodeDOM elements to translate.</param>
      <param name="w">The <see cref="T:System.IO.TextWriter" /> to output code to.</param>
      <param name="o">A <see cref="T:System.CodeDom.Compiler.CodeGeneratorOptions" /> that indicates the options to use for generating code.</param>
    </member>
    <member name="M:System.CodeDom.Compiler.ICodeGenerator.GenerateCodeFromType(System.CodeDom.CodeTypeDeclaration,System.IO.TextWriter,System.CodeDom.Compiler.CodeGeneratorOptions)">
      <summary>Generates code for the specified Code Document Object Model (CodeDOM) type declaration and outputs it to the specified text writer using the specified options.</summary>
      <param name="e">A <see cref="T:System.CodeDom.CodeTypeDeclaration" /> that indicates the type to generate code for.</param>
      <param name="w">The <see cref="T:System.IO.TextWriter" /> to output code to.</param>
      <param name="o">A <see cref="T:System.CodeDom.Compiler.CodeGeneratorOptions" /> that indicates the options to use for generating code.</param>
    </member>
    <member name="M:System.CodeDom.Compiler.ICodeGenerator.GetTypeOutput(System.CodeDom.CodeTypeReference)">
      <summary>Gets the type indicated by the specified <see cref="T:System.CodeDom.CodeTypeReference" />.</summary>
      <param name="type">A <see cref="T:System.CodeDom.CodeTypeReference" /> that indicates the type to return.</param>
      <returns>A text representation of the specified type for the language this code generator is designed to generate code in. For example, in Visual Basic, passing in type System.Int32 will return "Integer".</returns>
    </member>
    <member name="M:System.CodeDom.Compiler.ICodeGenerator.IsValidIdentifier(System.String)">
      <summary>Gets a value that indicates whether the specified value is a valid identifier for the current language.</summary>
      <param name="value">The value to test for being a valid identifier.</param>
      <returns>
        <see langword="true" /> if the <paramref name="value" /> parameter is a valid identifier; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.CodeDom.Compiler.ICodeGenerator.Supports(System.CodeDom.Compiler.GeneratorSupport)">
      <summary>Gets a value indicating whether the generator provides support for the language features represented by the specified <see cref="T:System.CodeDom.Compiler.GeneratorSupport" /> object.</summary>
      <param name="supports">The capabilities to test the generator for.</param>
      <returns>
        <see langword="true" /> if the specified capabilities are supported; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.CodeDom.Compiler.ICodeGenerator.ValidateIdentifier(System.String)">
      <summary>Throws an exception if the specified value is not a valid identifier.</summary>
      <param name="value">The identifier to validate.</param>
      <exception cref="T:System.ArgumentException">The identifier is not valid.</exception>
    </member>
    <member name="T:System.CodeDom.Compiler.ICodeParser">
      <summary>Defines an interface for parsing code into a <see cref="T:System.CodeDom.CodeCompileUnit" />.</summary>
    </member>
    <member name="M:System.CodeDom.Compiler.ICodeParser.Parse(System.IO.TextReader)">
      <summary>When implemented in a derived class, compiles the specified text stream into a <see cref="T:System.CodeDom.CodeCompileUnit" />.</summary>
      <param name="codeStream">A <see cref="T:System.IO.TextReader" /> that can be used to read the code to be compiled.</param>
      <returns>A <see cref="T:System.CodeDom.CodeCompileUnit" /> that contains a representation of the parsed code.</returns>
    </member>
    <member name="T:System.CodeDom.Compiler.LanguageOptions">
      <summary>Defines identifiers that indicate special features of a language.</summary>
    </member>
    <member name="F:System.CodeDom.Compiler.LanguageOptions.CaseInsensitive">
      <summary>The language is case-insensitive.</summary>
    </member>
    <member name="F:System.CodeDom.Compiler.LanguageOptions.None">
      <summary>The language has default characteristics.</summary>
    </member>
    <member name="T:System.CodeDom.Compiler.TempFileCollection">
      <summary>Represents a collection of temporary files.</summary>
    </member>
    <member name="M:System.CodeDom.Compiler.TempFileCollection.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.CodeDom.Compiler.TempFileCollection" /> class with default values.</summary>
    </member>
    <member name="M:System.CodeDom.Compiler.TempFileCollection.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.CodeDom.Compiler.TempFileCollection" /> class using the specified temporary directory that is set to delete the temporary files after their generation and use, by default.</summary>
      <param name="tempDir">A path to the temporary directory to use for storing the temporary files.</param>
    </member>
    <member name="M:System.CodeDom.Compiler.TempFileCollection.#ctor(System.String,System.Boolean)">
      <summary>Initializes a new instance of the <see cref="T:System.CodeDom.Compiler.TempFileCollection" /> class using the specified temporary directory and specified value indicating whether to keep or delete the temporary files after their generation and use, by default.</summary>
      <param name="tempDir">A path to the temporary directory to use for storing the temporary files.</param>
      <param name="keepFiles">
        <see langword="true" /> if the temporary files should be kept after use; <see langword="false" /> if the temporary files should be deleted.</param>
    </member>
    <member name="M:System.CodeDom.Compiler.TempFileCollection.AddExtension(System.String)">
      <summary>Adds a file name with the specified file name extension to the collection.</summary>
      <param name="fileExtension">The file name extension for the auto-generated temporary file name to add to the collection.</param>
      <returns>A file name with the specified extension that was just added to the collection.</returns>
    </member>
    <member name="M:System.CodeDom.Compiler.TempFileCollection.AddExtension(System.String,System.Boolean)">
      <summary>Adds a file name with the specified file name extension to the collection, using the specified value indicating whether the file should be deleted or retained.</summary>
      <param name="fileExtension">The file name extension for the auto-generated temporary file name to add to the collection.</param>
      <param name="keepFile">
        <see langword="true" /> if the file should be kept after use; <see langword="false" /> if the file should be deleted.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="fileExtension" /> is <see langword="null" /> or an empty string.</exception>
      <returns>A file name with the specified extension that was just added to the collection.</returns>
    </member>
    <member name="M:System.CodeDom.Compiler.TempFileCollection.AddFile(System.String,System.Boolean)">
      <summary>Adds the specified file to the collection, using the specified value indicating whether to keep the file after the collection is disposed or when the <see cref="M:System.CodeDom.Compiler.TempFileCollection.Delete" /> method is called.</summary>
      <param name="fileName">The name of the file to add to the collection.</param>
      <param name="keepFile">
        <see langword="true" /> if the file should be kept after use; <see langword="false" /> if the file should be deleted.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="fileName" /> is <see langword="null" /> or an empty string.  
  
 -or-  
  
 <paramref name="fileName" /> is a duplicate.</exception>
    </member>
    <member name="M:System.CodeDom.Compiler.TempFileCollection.CopyTo(System.String[],System.Int32)">
      <summary>Copies the members of the collection to the specified string, beginning at the specified index.</summary>
      <param name="fileNames">The array of strings to copy to.</param>
      <param name="start">The index of the array to begin copying to.</param>
    </member>
    <member name="M:System.CodeDom.Compiler.TempFileCollection.Delete">
      <summary>Deletes the temporary files within this collection that were not marked to be kept.</summary>
    </member>
    <member name="M:System.CodeDom.Compiler.TempFileCollection.Dispose(System.Boolean)">
      <summary>Releases the unmanaged resources used by the <see cref="T:System.CodeDom.Compiler.TempFileCollection" /> and optionally releases the managed resources.</summary>
      <param name="disposing">
        <see langword="true" /> to release both managed and unmanaged resources; <see langword="false" /> to release only unmanaged resources.</param>
    </member>
    <member name="M:System.CodeDom.Compiler.TempFileCollection.Finalize">
      <summary>Attempts to delete the temporary files before this object is reclaimed by garbage collection.</summary>
    </member>
    <member name="M:System.CodeDom.Compiler.TempFileCollection.GetEnumerator">
      <summary>Gets an enumerator that can enumerate the members of the collection.</summary>
      <returns>An <see cref="T:System.Collections.IEnumerator" /> that contains the collection's members.</returns>
    </member>
    <member name="M:System.CodeDom.Compiler.TempFileCollection.System#Collections#ICollection#CopyTo(System.Array,System.Int32)">
      <summary>Copies the elements of the collection to an array, starting at the specified index of the target array.</summary>
      <param name="array">The one-dimensional <see cref="T:System.Array" /> that is the destination of the elements copied from <see cref="T:System.Collections.ICollection" />. The <see cref="T:System.Array" /> must have zero-based indexing.</param>
      <param name="start">The zero-based index in array at which copying begins.</param>
    </member>
    <member name="M:System.CodeDom.Compiler.TempFileCollection.System#Collections#IEnumerable#GetEnumerator">
      <summary>Returns an enumerator that iterates through a collection.</summary>
      <returns>An <see cref="T:System.Collections.IEnumerator" /> that can be used to iterate through the collection.</returns>
    </member>
    <member name="M:System.CodeDom.Compiler.TempFileCollection.System#IDisposable#Dispose">
      <summary>Performs application-defined tasks associated with freeing, releasing, or resetting unmanaged resources.</summary>
    </member>
    <member name="P:System.CodeDom.Compiler.TempFileCollection.BasePath">
      <summary>Gets the full path to the base file name, without a file name extension, on the temporary directory path, that is used to generate temporary file names for the collection.</summary>
      <exception cref="T:System.Security.SecurityException">If the <see cref="P:System.CodeDom.Compiler.TempFileCollection.BasePath" /> property has not been set or is set to <see langword="null" />, and <see cref="F:System.Security.Permissions.FileIOPermissionAccess.AllAccess" /> is not granted for the temporary directory indicated by the <see cref="P:System.CodeDom.Compiler.TempFileCollection.TempDir" /> property.</exception>
      <returns>The full path to the base file name, without a file name extension, on the temporary directory path, that is used to generate temporary file names for the collection.</returns>
    </member>
    <member name="P:System.CodeDom.Compiler.TempFileCollection.Count">
      <summary>Gets the number of files in the collection.</summary>
      <returns>The number of files in the collection.</returns>
    </member>
    <member name="P:System.CodeDom.Compiler.TempFileCollection.KeepFiles">
      <summary>Gets or sets a value indicating whether to keep the files, by default, when the <see cref="M:System.CodeDom.Compiler.TempFileCollection.Delete" /> method is called or the collection is disposed.</summary>
      <returns>
        <see langword="true" /> if the files should be kept; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.CodeDom.Compiler.TempFileCollection.System#Collections#ICollection#Count">
      <summary>Gets the number of elements contained in the collection.</summary>
      <returns>The number of elements contained in the <see cref="T:System.Collections.ICollection" />.</returns>
    </member>
    <member name="P:System.CodeDom.Compiler.TempFileCollection.System#Collections#ICollection#IsSynchronized">
      <summary>Gets a value indicating whether access to the collection is synchronized (thread safe).</summary>
      <returns>
        <see langword="true" /> if access to the <see cref="T:System.Collections.ICollection" /> is synchronized (thread safe); otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.CodeDom.Compiler.TempFileCollection.System#Collections#ICollection#SyncRoot">
      <summary>Gets an object that can be used to synchronize access to the collection.</summary>
      <returns>An object that can be used to synchronize access to the <see cref="T:System.Collections.ICollection" />.</returns>
    </member>
    <member name="P:System.CodeDom.Compiler.TempFileCollection.TempDir">
      <summary>Gets the temporary directory to store the temporary files in.</summary>
      <returns>The temporary directory to store the temporary files in.</returns>
    </member>
    <member name="T:System.CodeDom.FieldDirection">
      <summary>Defines identifiers used to indicate the direction of parameter and argument declarations.</summary>
    </member>
    <member name="F:System.CodeDom.FieldDirection.In">
      <summary>An incoming field.</summary>
    </member>
    <member name="F:System.CodeDom.FieldDirection.Out">
      <summary>An outgoing field.</summary>
    </member>
    <member name="F:System.CodeDom.FieldDirection.Ref">
      <summary>A field by reference.</summary>
    </member>
    <member name="T:System.CodeDom.MemberAttributes">
      <summary>Defines member attribute identifiers for class members.</summary>
    </member>
    <member name="F:System.CodeDom.MemberAttributes.Abstract">
      <summary>An abstract member.</summary>
    </member>
    <member name="F:System.CodeDom.MemberAttributes.AccessMask">
      <summary>An access mask.</summary>
    </member>
    <member name="F:System.CodeDom.MemberAttributes.Assembly">
      <summary>A member that is accessible to any class within the same assembly.</summary>
    </member>
    <member name="F:System.CodeDom.MemberAttributes.Const">
      <summary>A constant member.</summary>
    </member>
    <member name="F:System.CodeDom.MemberAttributes.Family">
      <summary>A member that is accessible within the family of its class and derived classes.</summary>
    </member>
    <member name="F:System.CodeDom.MemberAttributes.FamilyAndAssembly">
      <summary>A member that is accessible within its class, and derived classes in the same assembly.</summary>
    </member>
    <member name="F:System.CodeDom.MemberAttributes.FamilyOrAssembly">
      <summary>A member that is accessible within its class, its derived classes in any assembly, and any class in the same assembly.</summary>
    </member>
    <member name="F:System.CodeDom.MemberAttributes.Final">
      <summary>A member that cannot be overridden in a derived class.</summary>
    </member>
    <member name="F:System.CodeDom.MemberAttributes.New">
      <summary>A new member.</summary>
    </member>
    <member name="F:System.CodeDom.MemberAttributes.Overloaded">
      <summary>An overloaded member. Some languages, such as Visual Basic, require overloaded members to be explicitly indicated.</summary>
    </member>
    <member name="F:System.CodeDom.MemberAttributes.Override">
      <summary>A member that overrides a base class member.</summary>
    </member>
    <member name="F:System.CodeDom.MemberAttributes.Private">
      <summary>A private member.</summary>
    </member>
    <member name="F:System.CodeDom.MemberAttributes.Public">
      <summary>A public member.</summary>
    </member>
    <member name="F:System.CodeDom.MemberAttributes.ScopeMask">
      <summary>A scope mask.</summary>
    </member>
    <member name="F:System.CodeDom.MemberAttributes.Static">
      <summary>A static member. In Visual Basic, this is equivalent to the <see langword="Shared" /> keyword.</summary>
    </member>
    <member name="F:System.CodeDom.MemberAttributes.VTableMask">
      <summary>A VTable mask.</summary>
    </member>
  </members>
</doc>