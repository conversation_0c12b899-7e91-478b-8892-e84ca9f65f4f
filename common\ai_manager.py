"""
AI管理器 - 自律守护者 v7.0 融合版
封装对不同AI服务商（文本、视觉）的API调用
支持OpenAI和Gemini两种服务商
"""

import os
import json
import time
import threading
import base64
from typing import Dict, Any, Optional, Union
from . import config_handler


class AIManager:
    """统一AI管理器，处理文本审核和视觉分析"""
    
    def __init__(self):
        """初始化AI管理器"""
        self.text_config = config_handler.get_ai_config('text_audit')
        self.vision_config = config_handler.get_ai_config('vision_audit')
        
        # 缓存和锁
        self.text_cache = {}  # URL -> (result, timestamp)
        self.vision_cache = {}  # image_hash -> (result, timestamp)
        self.cache_lock = threading.Lock()
        
        # 缓存TTL（秒）
        self.text_cache_ttl = 3600  # 1小时
        self.vision_cache_ttl = 86400  # 24小时
        
        print("AI管理器初始化完成")
    
    def audit_text(self, url: str, title: str) -> str:
        """
        文本内容审核
        
        Args:
            url: 网页URL
            title: 网页标题
            
        Returns:
            str: 'allow', 'deny', 或 'error'
        """
        if not self.text_config.get('enabled', False):
            return 'allow'
        
        # 检查缓存
        cache_key = f"{url}|{title}"
        with self.cache_lock:
            if cache_key in self.text_cache:
                result, timestamp = self.text_cache[cache_key]
                if time.time() - timestamp < self.text_cache_ttl:
                    return result
                else:
                    # 缓存过期，删除
                    del self.text_cache[cache_key]
        
        # 调用AI API
        try:
            result = self._call_text_api(url, title)
            
            # 缓存结果
            with self.cache_lock:
                self.text_cache[cache_key] = (result, time.time())
            
            return result
            
        except Exception as e:
            print(f"文本审核API调用失败: {str(e)}")
            return 'error'
    
    def classify_image(self, image_bytes: bytes) -> str:
        """
        图像分类
        
        Args:
            image_bytes: 图像字节数据
            
        Returns:
            str: 分类结果（如'游戏', '浏览器', '工具'等）或'error'
        """
        if not self.vision_config.get('enabled', False):
            return 'unknown'
        
        # 计算图像哈希作为缓存键
        import hashlib
        image_hash = hashlib.md5(image_bytes).hexdigest()
        
        # 检查缓存
        with self.cache_lock:
            if image_hash in self.vision_cache:
                result, timestamp = self.vision_cache[image_hash]
                if time.time() - timestamp < self.vision_cache_ttl:
                    return result
                else:
                    # 缓存过期，删除
                    del self.vision_cache[image_hash]
        
        # 调用AI API
        try:
            result = self._call_vision_api(image_bytes)
            
            # 缓存结果
            with self.cache_lock:
                self.vision_cache[image_hash] = (result, time.time())
            
            return result
            
        except Exception as e:
            print(f"视觉分析API调用失败: {str(e)}")
            return 'error'
    
    def _call_text_api(self, url: str, title: str) -> str:
        """调用文本审核API"""
        provider = self.text_config.get('provider', 'openai')
        
        if provider == 'openai':
            return self._call_openai_text(url, title)
        elif provider == 'gemini':
            return self._call_gemini_text(url, title)
        else:
            raise ValueError(f"不支持的文本API提供商: {provider}")
    
    def _call_vision_api(self, image_bytes: bytes) -> str:
        """调用视觉分析API"""
        provider = self.vision_config.get('provider', 'openai')
        
        if provider == 'openai':
            return self._call_openai_vision(image_bytes)
        elif provider == 'gemini':
            return self._call_gemini_vision(image_bytes)
        else:
            raise ValueError(f"不支持的视觉API提供商: {provider}")
    
    def _call_openai_text(self, url: str, title: str) -> str:
        """调用OpenAI文本审核API"""
        # TODO: 实现真实的OpenAI API调用
        # 当前为模拟实现，开发者需要替换为真实的API调用
        
        api_key = self.text_config.get('api_key', '')
        if not api_key:
            print("警告: 未设置OpenAI API密钥，使用模拟结果")
            return self._simulate_text_audit(url, title)
        
        try:
            import requests
            
            endpoint = self.text_config.get('api_endpoint', 'https://api.openai.com/v1/chat/completions')
            model = self.text_config.get('model', 'gpt-3.5-turbo')
            prompt_template = self.text_config.get('prompt_template', 
                '作为一名网络内容审核员，请判断以下网页内容是否合规。标题：{title}，URL：{url}。如果内容涉及游戏、色情、暴力或其他不当内容，请回复"deny"，否则回复"allow"。')
            
            prompt = prompt_template.format(title=title, url=url)
            
            headers = {
                'Authorization': f'Bearer {api_key}',
                'Content-Type': 'application/json'
            }
            
            data = {
                'model': model,
                'messages': [{'role': 'user', 'content': prompt}],
                'max_tokens': 10,
                'temperature': 0
            }
            
            response = requests.post(endpoint, headers=headers, json=data, timeout=15)
            response.raise_for_status()
            
            result = response.json()
            decision = result['choices'][0]['message']['content'].strip().lower()
            
            # 标准化返回值
            if 'deny' in decision:
                return 'deny'
            elif 'allow' in decision:
                return 'allow'
            else:
                return 'allow'  # 默认允许
                
        except Exception as e:
            print(f"OpenAI文本API调用失败: {str(e)}")
            return self._simulate_text_audit(url, title)
    
    def _call_gemini_text(self, url: str, title: str) -> str:
        """调用Gemini文本审核API"""
        # TODO: 实现Gemini API调用
        print("Gemini文本API暂未实现，使用模拟结果")
        return self._simulate_text_audit(url, title)
    
    def _call_openai_vision(self, image_bytes: bytes) -> str:
        """调用OpenAI视觉分析API"""
        # TODO: 实现真实的OpenAI Vision API调用
        api_key = self.vision_config.get('api_key', '')
        if not api_key:
            print("警告: 未设置OpenAI API密钥，使用模拟结果")
            return self._simulate_vision_classification()
        
        try:
            import requests
            
            # 将图像转换为base64
            image_base64 = base64.b64encode(image_bytes).decode('utf-8')
            
            model = self.vision_config.get('model', 'gpt-4-vision-preview')
            prompt_template = self.vision_config.get('prompt_template',
                '请将此截图中的应用分类为：游戏、浏览器、工具、聊天软件、办公软件、其他。只回复分类名称。')
            
            headers = {
                'Authorization': f'Bearer {api_key}',
                'Content-Type': 'application/json'
            }
            
            data = {
                'model': model,
                'messages': [
                    {
                        'role': 'user',
                        'content': [
                            {'type': 'text', 'text': prompt_template},
                            {
                                'type': 'image_url',
                                'image_url': {
                                    'url': f'data:image/png;base64,{image_base64}'
                                }
                            }
                        ]
                    }
                ],
                'max_tokens': 20,
                'temperature': 0
            }
            
            response = requests.post('https://api.openai.com/v1/chat/completions', 
                                   headers=headers, json=data, timeout=30)
            response.raise_for_status()
            
            result = response.json()
            classification = result['choices'][0]['message']['content'].strip()
            
            # 标准化分类结果
            classification_map = {
                '游戏': '游戏',
                '浏览器': '浏览器', 
                '工具': '工具',
                '聊天软件': '聊天软件',
                '办公软件': '办公软件'
            }
            
            for key, value in classification_map.items():
                if key in classification:
                    return value
            
            return '其他'
            
        except Exception as e:
            print(f"OpenAI视觉API调用失败: {str(e)}")
            return self._simulate_vision_classification()
    
    def _call_gemini_vision(self, image_bytes: bytes) -> str:
        """调用Gemini视觉分析API"""
        # TODO: 实现Gemini Vision API调用
        print("Gemini视觉API暂未实现，使用模拟结果")
        return self._simulate_vision_classification()
    
    def _simulate_text_audit(self, url: str, title: str) -> str:
        """模拟文本审核（用于开发和测试）"""
        # 简单的关键词检测
        suspicious_keywords = ['game', '游戏', 'porn', '色情', 'violence', '暴力', 'gambling', '赌博']
        
        text_to_check = f"{url} {title}".lower()
        for keyword in suspicious_keywords:
            if keyword in text_to_check:
                return 'deny'
        
        return 'allow'
    
    def _simulate_vision_classification(self) -> str:
        """模拟视觉分类（用于开发和测试）"""
        import random
        categories = ['游戏', '浏览器', '工具', '聊天软件', '办公软件', '其他']
        return random.choice(categories)
    
    def clear_cache(self) -> None:
        """清空所有缓存"""
        with self.cache_lock:
            self.text_cache.clear()
            self.vision_cache.clear()
        print("AI缓存已清空")
    
    def get_cache_stats(self) -> Dict[str, int]:
        """获取缓存统计信息"""
        with self.cache_lock:
            return {
                'text_cache_size': len(self.text_cache),
                'vision_cache_size': len(self.vision_cache)
            }
