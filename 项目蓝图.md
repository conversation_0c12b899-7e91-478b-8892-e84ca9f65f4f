最终项目蓝图：自律守护者 V6.0 (代号：钢铁契约)
一、 项目核心设计哲学

    目标: 创建一个后台守护程序，通过增加终止、修改或卸载程序的操作难度和时间成本，有效辅助用户对抗电脑沉迷。
    简单地说，用户将会创建一个进程黑名单（可能是游戏，娱乐，视频软件），并规定进程禁止时间。程序将在禁止时间内，监控所有黑名单进程并实时终结这些进程。

    核心哲学:

        高墙壁垒 (Operational Fortress): 重点不是代码加密，而是让用户在Windows图形界面和常规命令行工具下，无法找到、修改、终止或删除程序组件。

        服务自愈 (Service Self-Healing): 核心守护逻辑被封装在一个单一的、自恢复的Windows服务中。利用Windows服务管理器的内置故障恢复机制，当服务意外终止时，系统会自动在延迟后重启服务，取代了原有的双进程守护模型。

        身份伪装 (System Camouflage): 程序文件和Windows服务都使用随机的、看起来像系统组件的名称（例如，服务伪装为"Windows Security Enhancement Service"），并伪造旧的文件时间戳，以躲避人工搜索和基于时间的排序。

        安全启动 (Secure Launch): 通过一个独立的启动器（Launcher）来执行主程序，快捷方式指向启动器，从而隐藏主程序的真实安装路径，防止用户通过快捷方式轻易找到并破坏程序文件。

        契约精神 (The Contract): 提供一个预先设定的、严格的“卸载窗口期”。这是唯一官方的、和平的退出路径，尊重用户的最终选择权，但防止冲动性放弃。

二、 项目文件结构 (服务版本)

/Project-IronContract/
├── backend/
│   └── guardian_service.exe # Windows服务核心 (包含所有守护逻辑)
├── frontend/
│   ├── web_control_panel.exe  # Web技术栈的GUI控制台 (最终被安装为control_panel.exe)
│   └── launcher.exe       # 安全启动器
├── common/                  # (此目录在最终打包时不存在，其功能代码被编译进各个exe)
│   ├── config_handler.py
│   ├── cryption.py
│   ├── system_utils.py
│   ├── time_utils.py
│   └── process_utils.py
├── installer/
│   └── setup.exe          # 安装程序
└── build_script.py        # (开发用) 自动化打包脚本
└── uninstall.py         # (开发用) 强制卸载脚本

三、 共享核心逻辑 (common/ 模块的详细实现指南)

这些是构建所有EXE的基础功能模块。

1. cryption.py (加密模块)

    目的: 保护配置文件和启动器路径不被直接查看和修改。

    依赖库: cryptography

    实现步骤:

        双密钥系统:
            ENCRYPTION_KEY: 用于加密主配置文件(config.dat)的密钥。
            LAUNCHER_KEY: 一个完全独立的密钥，专门用于加密启动器路径文件(path.dat)。

        encrypt(data: dict, key: bytes) -> bytes 函数:

            接收一个Python字典和指定的密钥。
            使用json.dumps()将其转换为UTF-8编码的字符串，再转换为字节。
            使用cryptography.hazmat.primitives.ciphers.aead.AESGCM进行加密。AES-GCM模式同时提供了加密和完整性校验。
            返回加密后的字节数据。

        decrypt(encrypted_data: bytes, key: bytes) -> dict 函数:

            接收加密的字节数据和指定的密钥。
            使用相同的密钥和AES-GCM进行解密。
            如果解密失败（数据被篡改或密钥错误），函数应抛出异常。
            将解密后的字节数据用UTF-8解码，再用json.loads()转换回Python字典。
            返回该字典。

2. config_handler.py (配置处理器)

    目的: 作为读写加密配置文件的唯一入口，确保操作的原子性和安全性。黑名单规则现在完全存储在加密配置文件中。

    实现步骤:

        定义配置文件路径: C:\Windows\SysWOW64\config.dat

        存储一体化: 不再使用独立的blacklist.txt。所有配置，包括黑名单规则列表，都存储在config.dat中。

        load_config() -> dict 函数:

            从固定路径读取加密的config.dat文件。
            调用cryption.decrypt()解密数据。
            如果文件不存在或解密失败，返回一个包含默认设置的字典。
            内置迁移逻辑: 如果检测到旧的blacklist.txt文件，会自动读取其内容，将其规则迁移到新的配置字典中，然后删除旧文件。

        save_config(config_data: dict) 函数:

            调用cryption.encrypt()加密整个config_data字典。
            原子性写入: 先将加密数据写入临时文件(config.dat.tmp)，然后通过os.rename()原子性地替换旧文件，防止写入过程中断导致文件损坏。

        完整性保证: 由于整个配置文件都被AES-GCM加密，其完整性由加密算法本身保证，不再需要单独计算和比对文件哈希。

3. time_utils.py (可靠时间源)

    目的: 提供一个不受本地系统时间修改影响的准确时间。

    依赖库: 无 (使用标准库socket)

    实现步骤:

        ReliableClock 类:

        __init__(self) 构造函数:
            在后台线程中启动，循环尝试从NTP服务器列表（如 'pool.ntp.org', 'time.windows.com'）获取网络时间。必须成功一次才能完成初始化。
            获取成功后，记录初始网络时间戳和本地单调时间(time.monotonic())作为基准。

        now(self) -> float 方法:
            通过 `初始网络时间 + (当前单调时间 - 初始单调时间)` 的公式计算出当前可靠时间。

        calibrate(self) 方法:
            重新执行网络时间同步，以校准长期运行可能产生的误差。

4. system_utils.py (系统工具箱)

    目的: 封装所有高权限、平台相关的操作。

    依赖库: os, subprocess, pywin32

    实现步骤:

        create_locked_scheduled_task(task_name, exe_path): [已废弃] 服务版本不再使用计划任务。
        install_service(service_exe_path, service_name): 安装Windows服务。
        start_service(service_name): 启动Windows服务。
        stop_service(service_name): 停止Windows服务。
        delete_service(service_name): 删除Windows服务。
        configure_service_recovery(service_name, restart_delay): 配置服务失败时的自动重启策略。
        forge_file_timestamp(target_file, reference_file): 使用pywin32库，将目标文件的时间戳（创建、修改、访问）伪装成与系统文件（如kernel32.dll）一致。
        lock_directory(dir_path): 使用icacls移除目录的所有继承权限，只授予SYSTEM账户完全控制权，保护安装目录不被访问和修改。

5. process_utils.py (进程工具箱)

    目的: 提供进程枚举、检查和管理功能。

    依赖库: psutil

    实现步骤:

        get_running_processes(): 遍历psutil.process_iter()，返回包含pid, name, exe的进程信息列表。
        is_process_running(process_name): 检查指定名称的进程是否正在运行。
        kill_process_by_name(process_name): 查找并终止所有同名进程。
        start_process(exe_path): 启动一个新进程。

四、后台服务 (backend/ 的详细实现指南)

1. guardian_service.py (Windows服务核心)

    目的: 系统的“大脑”和“攻击之矛”。被重构为一个单一的Windows服务，负责执行所有黑名单规则、处理与前端的通信，并利用系统服务管理器实现自愈。

    实现步骤:

        服务框架 (GuardianService 类):
            继承自 `win32serviceutil.ServiceFramework`，实现Windows服务的标准接口。
            `_svc_name_`: 内部服务名，如 "GuardianProtectionService"。
            `_svc_display_name_`: 在服务管理器中显示的伪装名称，如 "Windows Security Enhancement Service"。
            `SvcDoRun()`: 服务启动时的主逻辑入口。它会创建一个`Guardian`核心类的实例并在一个独立线程中运行。
            `SvcStop()`: 接收服务停止信号，通过设置事件来优雅地终止`Guardian`主循环。

        核心逻辑 (Guardian 类):
            初始化:
                与旧版类似，实例化`ReliableClock`，加载配置，并启动TCP Socket服务器用于IPC。
                接收一个`stop_event`对象，用于响应服务停止请求。
            主循环 (无限循环):
                移除了所有与`watchdog.exe`相关的检查和复活逻辑。
                循环的每个周期都会检查`stop_event`是否被设置，如果被设置则干净地退出循环。
                核心的规则执行逻辑（检查时间、查杀黑名单进程）保持不变。
            指令处理线程 (IPC Command Handler):
                大部分指令（如规则管理、状态查询）保持不变。
                `deactivate_protection` (暂停守护指令) 的逻辑被重构:
                    验证窗口期通过后，不再是禁用计划任务，而是在一个新线程中通过`sc stop`和`sc delete`命令来停止并删除自身服务，实现“自杀”。

五、前端GUI (frontend/ 的详细实现指南)

项目采用现代化的Web技术栈作为GUI，通过安全启动器加载。

1. launcher.exe (安全启动器)

    目的: 作为用户交互的入口，隐藏主程序的真实位置。

    实现步骤:
        启动时，读取自身目录下的path.dat文件。
        使用专用的LAUNCHER_KEY解密path.dat，获取到主控制程序(control_panel.exe)的真实路径。
        启动主控制程序，然后自身退出。桌面快捷方式指向此启动器。

2. web_control_panel.exe (Web控制面板)

    目的: 提供一个现代化、美观、易用的图形界面。

    技术栈: Flask + pywebview。它本质上是一个本地Web服务器，用webview包装成一个独立的桌面应用。

    实现步骤:

        启动与状态检测:
            启动时，启动内置的Flask服务器。
            前端页面(HTML/JS)通过AJAX请求向本地Flask后端的 /api/status 接口查询状态。
            API会尝试连接guardian.exe的TCP端口，或查询计划任务状态，来判断系统是“守护中”还是“休眠中”。

        主界面UI (基于Web):
            界面通过RESTful API与Flask后端通信，Flask后端再通过TCP Socket与guardian.exe通信，充当一个“Web-TCP”桥梁。
            当状态为“守护中”:
                所有规则管理功能（增、删、查）可用。
                “暂停守护”按钮根据 /api/status 返回的窗口期信息自动启用或禁用。
            当状态为“休眠中”:
                规则管理功能被禁用。
                “开启守护”按钮可用，点击后会向 /api/protection/activate 发送请求。

        重启流程 (由Web控制面板的后端API执行):
            收到“开启守护”请求后，以管理员权限执行schtasks命令，重新启用并立即运行守护任务，唤醒整个守护体系。

六、关键工作流程 (串联所有模块)

1. 安装机制 (setup.exe 的核心职责)

    setup.exe是一个持有RSA私钥的安装器，等待用户提供由配对公钥加密的安装凭证。

    A. 核心逻辑:
        请求管理员权限。
        显示UI，等待用户输入Base64格式的安装凭证。
        使用内置的RSA私钥解密凭证，在内存中获得隐藏的安装路径。
        如果解密失败，提示错误并退出。

    B. 静默安装步骤:
        a. 生成随机的系统化文件名作为伪装身份。
        b. 部署文件 (guardian.exe, watchdog.exe, web_control_panel.exe -> control_panel.exe, launcher.exe)。
        c. 伪造所有已部署文件的文件时间戳。
        d. 锁定安装目录权限。
        e. 安装、配置并启动Windows服务。
            使用 `guardian_service.exe install` 命令安装服务。
            通过 `sc failure` 命令配置服务失败时自动重启。
            通过 `sc config` 命令设置服务的伪装显示名称。
            通过 `sc start` 命令立即启动服务。
        f. 初始化并保存第一份加密的config.dat，其中包含新的`service_name`字段。
        g. 创建加密的启动器配置文件path.dat，其中包含control_panel.exe的路径，使用LAUNCHER_KEY加密。
        h. 创建指向launcher.exe的桌面快捷方式。
        i. 执行自删除脚本，抹去自身。

2. 暂停/重启流程 (日常使用) - 新版本：基于服务休眠

    暂停守护 (Pause Flow) - 休眠模式:
        用户等待到预设的“窗口期”。
        Web控制面板的“暂停守护”按钮自动激活。
        用户点击按钮，前端发送deactivate请求，最终由`guardian_service.exe`执行暂停逻辑。
        服务执行休眠操作：停止服务运行并禁用自启动，进入真正的休眠状态。
        系统进入休眠状态，所有限制解除。

    重启守护 (Reactivation Flow) - 唤醒模式:
        用户在Web控制面板点击"开启守护"按钮。
        此版本中没有直接的“重启”按钮。
        控制面板的后端API会以管理员权限执行命令。
        首先，将服务的启动类型恢复为自动启动。
        然后，立即启动该服务。
        系统恢复到完全守护状态，且在电脑重启后也能自动运行。

七、开发与测试工具

此部分描述不包含在最终用户产品中，但对开发和测试至关重要的脚本。

1. build_script.py (自动化打包脚本)

    目的: 自动化完成从Python源代码到最终分发包的整个编译、打包和整理过程。
    
    核心功能:
        使用PyInstaller将所有独立的Python脚本 (guardian.py, watchdog.py, web_control_panel.py, launcher.py, setup.py) 打包成.exe文件。
        为Web控制面板添加数据文件（templates和static目录）。
        将所有生成的.exe文件整理到 /Project-IronContract/ 目录下的正确子目录中。
        创建一份简单的README.txt和空的blacklist.txt模板到分发包中。

2. uninstall.py (强制卸载脚本)

    目的: 为开发者提供一个快速、彻底清理开发环境中已安装程序所有组件的工具。
    
    警告: 此脚本破坏性极强，绝对不能分发给最终用户。
    
    核心功能:
        读取配置文件以获取安装路径和所有组件的名称。
        禁用并删除由程序创建的所有计划任务。
        强制终止所有正在运行的守护进程 (guardian, watchdog)。
        重置安装目录的系统级权限锁。
        删除整个安装目录。
        删除加密的配置文件和桌面快捷方式。