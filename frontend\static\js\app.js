// 自律守护者 - 前端JavaScript

// 全局变量
let systemStatus = 'unknown';
let statusTimer = null;
let allProcesses = [];

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
});

// 初始化应用
function initializeApp() {
    console.log('初始化自律守护者控制面板...');
    
    // 设置事件监听器
    setupEventListeners();
    
    // 加载初始数据
    loadSystemStatus();
    loadRules();
    
    // 启动状态检查定时器
    startStatusTimer();
}

// 设置事件监听器
function setupEventListeners() {
    // 规则类型切换
    document.querySelectorAll('input[name="ruleType"]').forEach(radio => {
        radio.addEventListener('change', toggleTimeRangeInputs);
    });
    
    // 添加规则表单提交
    document.getElementById('addRuleForm').addEventListener('submit', handleAddRule);
    
    // 进程搜索
    document.getElementById('processSearch').addEventListener('input', filterProcesses);
    
    // 窗口期配置表单
    document.getElementById('windowConfigForm').addEventListener('submit', function(e) {
        e.preventDefault();
        saveWindowConfig();
    });
}

// 切换时间范围输入框显示
function toggleTimeRangeInputs() {
    const timeRangeInputs = document.getElementById('timeRangeInputs');
    const isTimeRange = document.getElementById('timeRange').checked;
    
    if (isTimeRange) {
        timeRangeInputs.style.display = 'block';
        timeRangeInputs.classList.add('fade-in');
    } else {
        timeRangeInputs.style.display = 'none';
        timeRangeInputs.classList.remove('fade-in');
    }
}

// 加载系统状态
async function loadSystemStatus() {
    try {
        const response = await fetch('/api/status');
        const data = await response.json();
        
        updateStatusDisplay(data);
        updateControlButtons(data);
        
    } catch (error) {
        console.error('加载系统状态失败:', error);
        updateStatusDisplay({
            system_status: 'unknown',
            status_text: '连接失败',
            status_detail: '无法连接到服务器',
            status_color: 'danger'
        });
    }
}

// 更新状态显示
function updateStatusDisplay(data) {
    const indicator = document.getElementById('statusIndicator');
    const statusText = document.getElementById('statusText');
    const statusDetail = document.getElementById('statusDetail');
    
    // 清除所有状态类
    indicator.className = 'status-indicator me-3';
    
    // 根据状态设置样式和内容
    switch (data.status_color) {
        case 'success':
            indicator.classList.add('status-success');
            indicator.innerHTML = '<i class="bi bi-shield-check"></i>';
            break;
        case 'warning':
            indicator.classList.add('status-warning');
            indicator.innerHTML = '<i class="bi bi-exclamation-triangle"></i>';
            break;
        case 'danger':
            indicator.classList.add('status-danger');
            indicator.innerHTML = '<i class="bi bi-x-circle"></i>';
            break;
        default:
            indicator.classList.add('status-info');
            indicator.innerHTML = '<i class="bi bi-question-circle"></i>';
    }
    
    statusText.textContent = data.status_text || '未知状态';
    statusDetail.textContent = data.status_detail || '';
    
    // 保存全局状态
    systemStatus = data.system_status || 'unknown';
}

// 更新控制按钮
function updateControlButtons(data) {
    const activateBtn = document.getElementById('activateBtn');
    const deactivateBtn = document.getElementById('deactivateBtn');
    const controlHint = document.getElementById('controlHint');
    
    // 重置按钮状态
    activateBtn.disabled = true;
    deactivateBtn.disabled = true;
    
    switch (data.system_status) {
        case 'protected':
            // 守护中状态
            deactivateBtn.disabled = false;
            if (data.in_window) {
                deactivateBtn.textContent = '暂停守护 (窗口期)';
                deactivateBtn.classList.add('pulse');
            } else {
                deactivateBtn.textContent = '暂停守护';
                deactivateBtn.classList.remove('pulse');
                deactivateBtn.disabled = true;
            }
            controlHint.textContent = '系统正在守护中，可在窗口期暂停';
            break;
            
        case 'hibernating':
            // 休眠中状态
            activateBtn.disabled = false;
            controlHint.textContent = '守护已暂停，点击开启守护恢复保护';
            break;
            
        default:
            // 未知状态
            activateBtn.disabled = false;
            controlHint.textContent = '无法连接后台服务，请检查系统状态';
    }
}

// 加载黑名单规则
async function loadRules() {
    try {
        const response = await fetch('/api/rules');
        const data = await response.json();
        
        updateRulesTable(data.rules || []);
        updateRulesCount(data.count || 0);
        
    } catch (error) {
        console.error('加载规则失败:', error);
        updateRulesTable([]);
        updateRulesCount(0);
    }
}

// 更新规则表格
function updateRulesTable(rules) {
    const tbody = document.getElementById('rulesTableBody');
    
    if (rules.length === 0) {
        tbody.innerHTML = `
            <tr>
                <td colspan="4" class="text-center text-muted">
                    <i class="bi bi-inbox"></i>
                    暂无规则
                </td>
            </tr>
        `;
        return;
    }
    
    tbody.innerHTML = rules.map(rule => `
        <tr class="fade-in">
            <td>
                <strong>${escapeHtml(rule.process_name)}</strong>
            </td>
            <td>
                <span class="badge ${rule.rule_type === 'all_day' ? 'bg-danger' : 'bg-warning'}">
                    ${rule.rule_type === 'all_day' ? '全天' : '时间段'}
                </span>
            </td>
            <td>
                <small class="text-muted">${escapeHtml(rule.time_range)}</small>
            </td>
            <td>
                <button class="btn btn-sm btn-outline-danger" onclick="deleteRule('${escapeHtml(rule.raw)}')">
                    <i class="bi bi-trash"></i>
                    删除
                </button>
            </td>
        </tr>
    `).join('');
}

// 更新规则计数
function updateRulesCount(count) {
    document.getElementById('rulesCount').textContent = count;
}

// 处理添加规则
async function handleAddRule(event) {
    event.preventDefault();
    
    const processName = document.getElementById('processName').value.trim();
    const ruleType = document.querySelector('input[name="ruleType"]:checked').value;
    const startTime = document.getElementById('startTime').value;
    const endTime = document.getElementById('endTime').value;
    
    if (!processName) {
        showAlert('请输入进程名称', 'warning');
        return;
    }
    
    const ruleData = {
        process_name: processName,
        rule_type: ruleType,
        start_time: startTime,
        end_time: endTime
    };
    
    try {
        const response = await fetch('/api/rules', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(ruleData)
        });
        
        const data = await response.json();
        
        if (data.status === 'success') {
            showAlert('规则添加成功', 'success');
            document.getElementById('addRuleForm').reset();
            document.getElementById('allDay').checked = true;
            toggleTimeRangeInputs();
            loadRules();
        } else {
            showAlert(data.message || '添加规则失败', 'danger');
        }
        
    } catch (error) {
        console.error('添加规则失败:', error);
        showAlert('添加规则失败: ' + error.message, 'danger');
    }
}

// 删除规则
async function deleteRule(rule) {
    if (!confirm('确定要删除这个规则吗？')) {
        return;
    }
    
    try {
        const response = await fetch('/api/rules', {
            method: 'DELETE',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ rule: rule })
        });
        
        const data = await response.json();
        
        if (data.status === 'success') {
            showAlert('规则删除成功', 'success');
            loadRules();
        } else {
            showAlert(data.message || '删除规则失败', 'danger');
        }
        
    } catch (error) {
        console.error('删除规则失败:', error);
        showAlert('删除规则失败: ' + error.message, 'danger');
    }
}

// 激活保护
async function activateProtection() {
    try {
        const response = await fetch('/api/protection/activate', {
            method: 'POST'
        });
        
        const data = await response.json();
        
        if (data.status === 'success') {
            showAlert('守护已成功激活！', 'success');
            setTimeout(loadSystemStatus, 2000);
        } else {
            showAlert(data.message || '激活保护失败', 'danger');
        }
        
    } catch (error) {
        console.error('激活保护失败:', error);
        showAlert('激活保护失败: ' + error.message, 'danger');
    }
}

// 停用保护
async function deactivateProtection() {
    if (!confirm('确定要暂停守护吗？这将停止所有保护功能。')) {
        return;
    }
    
    try {
        const response = await fetch('/api/protection/deactivate', {
            method: 'POST'
        });
        
        const data = await response.json();
        
        if (data.status === 'success') {
            showAlert('保护已暂停', 'info');
            setTimeout(loadSystemStatus, 1000);
        } else {
            showAlert(data.message || '暂停保护失败', 'danger');
        }
        
    } catch (error) {
        console.error('停用保护失败:', error);
        showAlert('停用保护失败: ' + error.message, 'danger');
    }
}

// 显示进程列表
async function showProcessList() {
    const modal = new bootstrap.Modal(document.getElementById('processModal'));
    modal.show();

    try {
        const response = await fetch('/api/processes');
        const data = await response.json();

        allProcesses = data.processes || [];
        updateProcessTable(allProcesses);

    } catch (error) {
        console.error('加载进程列表失败:', error);
        document.getElementById('processTableBody').innerHTML = `
            <tr>
                <td colspan="4" class="text-center text-danger">
                    <i class="bi bi-exclamation-triangle"></i>
                    加载失败: ${error.message}
                </td>
            </tr>
        `;
    }
}

// 更新进程表格
function updateProcessTable(processes) {
    const tbody = document.getElementById('processTableBody');

    if (processes.length === 0) {
        tbody.innerHTML = `
            <tr>
                <td colspan="4" class="text-center text-muted">
                    <i class="bi bi-inbox"></i>
                    无进程数据
                </td>
            </tr>
        `;
        return;
    }

    tbody.innerHTML = processes.map(proc => `
        <tr>
            <td><small>${proc.pid}</small></td>
            <td><strong>${escapeHtml(proc.name)}</strong></td>
            <td><small class="text-muted">${escapeHtml(proc.exe)}</small></td>
            <td>
                <button class="btn btn-sm btn-primary" onclick="selectProcess('${escapeHtml(proc.name)}')">
                    选择
                </button>
            </td>
        </tr>
    `).join('');
}

// 过滤进程
function filterProcesses() {
    const searchTerm = document.getElementById('processSearch').value.toLowerCase();

    if (!searchTerm) {
        updateProcessTable(allProcesses);
        return;
    }

    const filteredProcesses = allProcesses.filter(proc =>
        proc.name.toLowerCase().includes(searchTerm) ||
        proc.exe.toLowerCase().includes(searchTerm) ||
        proc.pid.toString().includes(searchTerm)
    );

    updateProcessTable(filteredProcesses);
}

// 选择进程
function selectProcess(processName) {
    document.getElementById('processName').value = processName;
    const modal = bootstrap.Modal.getInstance(document.getElementById('processModal'));
    modal.hide();
}

// 显示窗口期配置
function showWindowConfig() {
    const modal = new bootstrap.Modal(document.getElementById('windowConfigModal'));
    modal.show();
}

// 保存窗口期配置
async function saveWindowConfig() {
    const enabled = document.getElementById('enableWindow').checked;
    const dayOfWeek = parseInt(document.getElementById('dayOfWeek').value);
    const startTime = document.getElementById('windowStartTime').value;
    const endTime = document.getElementById('windowEndTime').value;

    const configData = {
        enabled: enabled,
        day_of_week: dayOfWeek,
        start_time: startTime,
        end_time: endTime
    };

    try {
        const response = await fetch('/api/window-config', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(configData)
        });

        const data = await response.json();

        if (data.status === 'success') {
            showAlert('窗口期配置已保存', 'success');
            const modal = bootstrap.Modal.getInstance(document.getElementById('windowConfigModal'));
            modal.hide();
        } else {
            showAlert(data.message || '保存配置失败', 'danger');
        }

    } catch (error) {
        console.error('保存窗口期配置失败:', error);
        showAlert('保存配置失败: ' + error.message, 'danger');
    }
}

// 启动状态检查定时器
function startStatusTimer() {
    // 每5秒检查一次状态
    statusTimer = setInterval(loadSystemStatus, 5000);
}

// 停止状态检查定时器
function stopStatusTimer() {
    if (statusTimer) {
        clearInterval(statusTimer);
        statusTimer = null;
    }
}

// 显示警告消息
function showAlert(message, type = 'info') {
    // 创建警告元素
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    // 添加到页面
    document.body.appendChild(alertDiv);

    // 3秒后自动移除
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 3000);
}

// HTML转义函数
function escapeHtml(text) {
    const map = {
        '&': '&amp;',
        '<': '&lt;',
        '>': '&gt;',
        '"': '&quot;',
        "'": '&#039;'
    };
    return text.replace(/[&<>"']/g, function(m) { return map[m]; });
}

// 页面卸载时清理
window.addEventListener('beforeunload', function() {
    stopStatusTimer();
});
