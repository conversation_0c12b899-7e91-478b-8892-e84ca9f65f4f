项目开发方案：自律守护者 v7.0 (融合版)
版本: 2.0
日期: 2025年7月16日
作者: Roo

第一部分：最终功能清单 (Functional Requirements)
本部分清晰定义了项目完成后，软件需要具备的所有核心功能。这是开发的最终目标。

1. 统一后台守护 (Unified Guardian Core):

整个系统后台只有一个核心组件：一个以Windows服务形式运行的、难以被常规手段关闭的守护程序。
该服务集成了原有两个项目的所有核心能力，并实现了自我恢复机制。
2. 双层过滤系统 (Dual-Layer Filtering System):

系统提供两层独立、可开关的过滤机制：
全局进程扫描 (Global Process Scan): 继承自项目一，根据黑名单（如 game.exe）和设定的时间段，在后台持续查杀所有匹配的进程，无论其是否有窗口。
焦点内容监控 (Focus Content Monitor): 继承自项目二，仅针对用户当前正在使用的前台浏览器窗口，通过读取URL和标题，进行更精细的内容过滤。
用户可以在设置中独立启用或禁用这两层过滤。
3. AI驱动的浏览器内容审核 (AI-Powered Content Audit):

当“焦点内容监控”启用时，系统会自动将当前浏览的网页标题和URL发送给文本AI（支持OpenAI/Gemini）。
AI会根据预设的规则（如禁止游戏、色情、暴力内容）判断页面是否合规。
如果AI判定为“拒绝”(deny)，系统将自动执行关闭该浏览器标签页的操作。
4. 智能视觉分析与自动分类 (Visual AI Analysis & Auto-Classification):

这是本次融合的核心新功能。
系统将持续监控并记录所有前台应用程序的累计使用时长。
当任何一个陌生应用（不在记录之中，不限于浏览器）的单日累计使用时长超过一个可配置的阈值（如30分钟）时，系统将自动触发以下流程：
对该应用的窗口进行截图。
将截图发送给视觉AI（支持OpenAI/Gemini）。
视觉AI负责将该应用分类为预设的类别，例如：“游戏”、“浏览器”、“工具”、“聊天软件”等。
分类结果将被永久记录在本地数据库中，后续该应用将自动归为此类。
5. 基于分类的时间管理 (Category-Based Time Management):

一旦一个应用被AI分类（如“游戏”），它将自动受到该类别的规则约束。
用户可以在设置中为每个类别设定每日总使用时长上限（如“游戏”类别每天最多1小时）。
系统会根据记录的专注时间，在用户使用该类别的应用总时长超限时，自动将其强制关闭。

6. 拒绝后的交互与临时豁免 (Post-Denial Interaction & Override):

当AI关闭一个浏览器标签页后，系统必须立即从桌面右下角弹出一个自定义的通知窗口。
该窗口会明确告知用户“当前页面已被AI拒绝并关闭”。
窗口上提供一个“强制通过”按钮。点击后，该被拒绝的URL将被加入一个临时的白名单，有效期为24小时。在此期间，再次访问该URL将不会被拦截。强制通过有次数限制，时间段为当前到下一个窗口期。

7. 统一打包与安装 (Unified Installation):

所有后台服务、前端界面、辅助工具都将被打包成一个单一的、对用户友好的安装程序。
安装过程将自动完成服务的部署、权限的设定和快捷方式的创建，遵循项目一的“高墙壁垒”和“身份伪装”哲学。

2. 系统架构
系统将围绕一个统一的后台服务构建，该服务内部包含多个可独立开关的功能模块（线程），并由一个统一的配置和数据中心支持。

无法渲染图表

3. 关键数据结构 (地基部分)
3.1 统一配置文件 (config.dat) 结构
common/config_handler.py 需重构以支持以下分层结构：

{
    "version": "7.0",
    "installation_path": "C:\\Program Files\\Guardian",
    "service_name": "WinSecEnhSvc",
    "features": {
        "enable_global_process_scan": True,
        "enable_focus_content_monitor": True,
        "enable_vision_analysis": True
    },
    "rules": {
        "process_blacklist": [
            "game1.exe",
            "game2.exe,20:00,22:00"
        ],
        "browser_content_blacklist": {
            "keywords": ["游戏", "广告", "抽奖"],
            "urls": ["example-malicious-domain.com"]
        },
        "time_limit_rules": {
            "游戏": {"limit_minutes_per_day": 60, "enabled": True},
            "聊天软件": {"limit_minutes_per_day": 120, "enabled": False}
        }
    },
    "ai_config": {
        "text_audit": {
            "enabled": True,
            "provider": "openai", # 'openai' or 'gemini'
            "api_key": "sk-...",
            "api_endpoint": "https://openrouter.ai/api/v1/chat/completions",
            "model": "google/gemma-3-27b-it:free",
            "prompt_template": "作为一名网络内容审核员..."
        },
        "vision_audit": {
            "enabled": True,
            "focus_time_trigger_minutes": 30,
            "provider": "openai", # 'openai' or 'gemini'
            "api_key": "sk-...",
            "model": "gpt-4-vision-preview",
            "prompt_template": "请将此截图中的应用分类为：游戏、浏览器、工具、聊天软件..."
        }
    },
    "uninstall_window": {
        "enabled": True,
        "day_of_week": 0,
        "start_time": "02:00",
        "end_time": "04:00"
    }
}

python



3.2 数据库 (database.db) 表结构
common/database_manager.py 需创建并管理以下表：

应用分类表 (app_classification): 存储视觉AI的分析结果。

CREATE TABLE IF NOT EXISTS app_classification (
    process_name TEXT PRIMARY KEY,
    category TEXT NOT NULL,
    last_updated INTEGER NOT NULL
);

sql


专注时间日志表 (focus_log): 记录每个窗口的专注时间。

CREATE TABLE IF NOT EXISTS focus_log (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    process_name TEXT NOT NULL,
    window_title TEXT,
    start_time INTEGER NOT NULL,
    end_time INTEGER,
    duration_seconds INTEGER
);

sql


临时白名单表 (temp_whitelist): 用于“强制通过”功能。

CREATE TABLE IF NOT EXISTS temp_whitelist (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    entry_type TEXT NOT NULL, -- 'url' or 'process_name'
    value TEXT NOT NULL UNIQUE,
    expiry_timestamp INTEGER NOT NULL
);

sql


4. 模块化实施指南
4.1 backend/unified_guardian_service.py (新)
目的: 项目的唯一核心，作为Windows服务运行，管理所有后台工作线程。
关键类:
GuardianService(win32serviceutil.ServiceFramework): 服务外壳，负责服务的启动和停止。
GuardianCore: 核心逻辑类。
实施要点:
GuardianCore.__init__: 初始化配置、数据库管理器、AI管理器、通知管理器和线程停止事件 self.stop_event。
GuardianCore.run:
读取配置中的 features 开关。
根据开关，创建并启动以下三个核心工作线程：
self.global_scan_thread = threading.Thread(target=self._worker_global_scan)
self.focus_monitor_thread = threading.Thread(target=self._worker_focus_monitor)
self.vision_analyzer_thread = threading.Thread(target=self._worker_vision_analyzer)
主线程进入等待循环，直到 self.stop_event 被设置。
_worker_... 函数: 每个worker都是一个死循环，循环内部有 time.sleep() 和对 self.stop_event 的检查。
_worker_global_scan: 实现项目一的逻辑，从配置读取 process_blacklist 并调用 process_utils.kill_process_by_name。
_worker_focus_monitor: 实现项目二的核心逻辑，调用 browser_info_fetcher，并根据黑名单和AI结果调用 tab_closer 和 notification_manager。
_worker_vision_analyzer: 实现新功能，管理 focus_log 和 app_classification 表，并根据 time_limit_rules 查杀超时进程。
4.2 common/database_manager.py (新)
目的: 提供一个线程安全的、统一的数据库访问接口。
实施要点:
使用 sqlite3 模块。
在 __init__ 中创建数据库连接和游标。
简化项: 使用一个 threading.Lock() 对象 self.db_lock。所有执行 commit() 的操作（增、删、改）都必须在 with self.db_lock: 代码块内执行，以保证线程安全。
为每个表提供清晰的CRUD方法，例如 add_focus_log(), get_app_category(), is_in_whitelist()。
4.3 common/ai_manager.py (新)
目的: 封装对不同AI服务商（文本、视觉）的API调用。
实施要点:
__init__: 读取 ai_config 配置。
audit_text(url, title): 根据配置的 provider 调用私有的 _call_openai_text() 或 _call_gemini_text()。
classify_image(image_bytes): 根据配置的 provider 调用私有的 _call_openai_vision() 或 _call_gemini_vision()。
简化项: 在第一轮开发中，这些私有的 _call... 方法可以只返回写死的模拟数据（例如 return "deny" 或 return "游戏"），以便在没有API Key的情况下也能完成流程调试。
4.4 common/notification_manager.py (新)
目的: 创建独立、健壮的自定义弹窗。
实施要点:
健壮性设计: 创建一个辅助脚本 notifier.py，它使用 tkinter 创建一个带标题、消息和按钮的窗口，并能实现右下角滑入动画。
notification_manager.show(title, message, button_config): 此函数的核心功能是调用 subprocess.Popen 来执行打包后的 notifier.exe，并通过命令行参数传递 title, message 等信息。
这种进程隔离的设计确保了即使弹窗逻辑出错，也绝不会影响核心守护服务的稳定性。
5. 开发与实施计划
此计划遵循我们商定的“在坚实地基上快速迭代”策略。

第一阶段：架构与基础重构

任务: 创建 unified_guardian_service.py 的基本框架。修改 config_handler.py 以支持新的分层配置。创建 database_manager.py 并实现表结构和带锁的CRUD方法。创建 ai_manager.py 和 notification_manager.py 的空壳或模拟实现。
目标: 拥有一个可以运行但无实际功能的新服务骨架，以及统一的配置和数据接口。
第二阶段：功能模块集成

任务: 将项目一的全局进程扫描逻辑和项目二的焦点内容监控逻辑（不含AI和通知）迁移到 unified_guardian_service.py 的 _worker_global_scan 和 _worker_focus_monitor 线程中。
目标: 新服务已具备两个项目的核心监控能力，但尚未联动。
第三阶段：核心新功能开发 - 专注时间与视觉AI

任务: 实现 _worker_vision_analyzer 线程的完整逻辑。完成 system_utils.take_screenshot。用真实的API调用（或模拟数据）填充 ai_manager.classify_image。
目标: 系统能够自动计时、分类应用，并根据时长限制查杀进程。
第四阶段：核心新功能开发 - 交互与反馈

任务: 完成 notifier.py 和 notification_manager.py 的开发。在 _worker_focus_monitor 中集成AI文本审核、关闭标签页和调用通知的完整流程。实现“强制通过”的回调和临时白名单逻辑。
目标: AI审核流程完全打通，用户可以收到反馈并进行交互。
第五阶段：前端界面升级

任务: 扩展 web_control_panel.py 的API，并更新前端页面以支持新功能的配置和数据展示。
目标: 用户可以通过GUI完全控制新系统的所有功能。
第六、七阶段：打包、部署、测试与交付

任务: 更新构建和安装脚本，进行端到端测试，修复bug。
目标: 交付一个稳定、可靠的最终产品。
